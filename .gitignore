/node_modules
/public/hot
/public/storage
/storage/*.key
/storage/debugbar
/vendor
/.idea
/.vscode
/.vagrant
/wordpress/media
/wordpress/media/*
/wordpress/*.log
/wordpress/wp-config.php
/wordpress/wp-content/advanced-cache.php
/wordpress/wp-content/backup-db/
/wordpress/wp-content/backups/
/wordpress/wp-content/blogs.dir/
/wordpress/wp-content/cache/
/wordpress/wp-content/upgrade/
/wordpress/wp-content/uploads/
/wordpress/wp-content/mu-plugins/
/wordpress/wp-content/wp-cache-config.php
/wordpress/wp-content/plugins/hello.php
/wordpress/license.txt
/wordpress/readme.html
/wordpress/sitemap.xml
/wordpress/sitemap.xml.gz
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
.env
.env.dusk.local
.env.dusk.staging
.phpunit.result.cache
/public/js/tenants/*
/public/js/documents/*
/public/js/translations/*
/public/js/editor/*
/public/js/app.js
/public/mix-manifest.json
/public/css/app.css
/public/css/editor.css
/public/css/preview.css
/public/css/translations.css
/storage/.DS_Store
/storage/framework/disposable_domains.json
/public/sitemap.xml
/public/css/tenant.css
/public/js/admin/*
phpunit.dusk.xml
phpstan.neon
# Ignore PhpStorm temporary files
*~