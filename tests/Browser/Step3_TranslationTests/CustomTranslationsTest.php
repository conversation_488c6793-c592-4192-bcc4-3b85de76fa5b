<?php

namespace Tests\Browser\Step3_TranslationTests;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverKeys;
use Illuminate\Support\Facades\App;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

/**
 * Class CustomTranslationsTest
 *
 * Test translation flow for custom user uploaded documents
 *
 *
 * @package Tests\Browser
 */
class CustomTranslationsTest extends DuskTestCase {

    public function testCustomTranslationOrder() {
        $this->browse(function (Browser $browser) {

	        $browser->visit('/prijevod');

	        $this->acceptTOS($browser);

			$this->processOrder($browser, 'certified');

			$this->processOrder($browser, 'normal');

			$this->login($browser);

			$browser->waitForText('Zatražili ste ponudu za prijevod dokumenta.')
			        ->assertSee('Zatražili ste ponudu za prijevod dokumenta.');
        });
    }

	private function processOrder($browser, $type) {

		$browser->visit('/prijevod')->assertSee('Narudžba prijevoda');

		$browser->select('type', $type);
		$browser->attach('documents[0]', base_path('tests/Browser/Step3_TranslationTests/custom.pdf'));

		$browser->type('name', 'Dusk');
		$browser->type('email', env('DUSK_USER'));
		$browser->type('address', 'Dusk');
		$browser->type('city', 'Dusk');
		$browser->type('postal_code', 'Dusk');
		$browser->type('oib', '1234567890');
		$browser->type('phone', '1234567890');

		$browser->press('Zatraži ponudu');
	}

	private function login($browser) {
		$browser->visit('/login')->assertSee('Prijavi se');
		$browser->type('email', env('DUSK_USER'));
		$browser->type('password', 'dusk123');
		$browser->press('Prijavi se');
	}

    private function acceptTOS(Browser $browser) {
        $browser->waitForText('O kolačićima na ovoj stranici');
        $browser->press('Prihvaćam sve');
    }

}
