<?php

declare(strict_types=1);

use App\Http\Controllers\DebugController;
use App\Http\Controllers\Tenant\SearchController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;
use App\Http\Controllers\Tenant\HomeController;
use App\Http\Controllers\Tenant\DashboardController;
use App\Http\Controllers\Tenant\Auth\LoginController;
use App\Http\Controllers\Tenant\Auth\RegisterController;
use App\Http\Controllers\Tenant\Auth\ForgotPasswordController;
use App\Http\Controllers\Tenant\Auth\ResetPasswordController;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {

	// only allow in local environment
	Route::group(['middleware' => ['local']], function () {
		Route::any('/debug', [DebugController::class, 'index']);
	});

    // Home route
    Route::get('/', [HomeController::class, 'index'])->name('tenant.home');

    // Authenticated routes
    Route::group(['middleware' => ['auth']], function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('tenant.dashboard');
    });

	// Throttled routes
	Route::group(['middleware' => ['throttle']], function () {
		// Authentication routes
		Route::get('/login', [LoginController::class, 'showLoginForm'])->name('tenant.login');
		Route::post('/login', [LoginController::class, 'login']);
		Route::post('/logout', [LoginController::class, 'logout'])->name('tenant.logout');

		// Registration routes
		Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('tenant.register');
		Route::post('/register', [RegisterController::class, 'register']);

		// Password reset routes
		Route::get('/password/reset', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('tenant.password.request');
		Route::post('/password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('tenant.password.email');
		Route::get('/password/reset/{token}', [ResetPasswordController::class, 'showResetForm'])->name('tenant.password.reset');
		Route::post('/password/reset', [ResetPasswordController::class, 'reset'])->name('tenant.password.update');

		// OTP Verification Routes
		Route::get('/otp/verify', [LoginController::class, 'showOtpForm'])->name('tenant.otp.form');
		Route::post('/otp/verify', [LoginController::class, 'verifyOtp'])->name('tenant.otp.verify');

        // search routes
        Route::post('/template/search', [SearchController::class, 'templates'])->name('tenant.search.templates');
	});

});
