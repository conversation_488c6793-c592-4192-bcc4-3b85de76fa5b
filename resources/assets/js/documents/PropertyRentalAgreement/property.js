$(document).ready(function () {

    // add additional bedroom
    $('#add_additional_bedroom').click(function () {
        $('#additional_bedrooms').append($('#additional_bedroom_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now()));
        bindToggleAdditionalBedroom();
    });

    // remove additional bedroom on uncheck
    function bindToggleAdditionalBedroom() {
        $('.toggle_additional_bedroom').unbind('click').click(function () {
            if (!$(this).is(':checked')) {
                let target = $(this).closest('.additional_bedroom_content');
                target.remove();
                $(document).trigger('updatePreviewEvent');
            }
        });
    }

    bindToggleAdditionalBedroom();

    // add additional bathroom
    $('#add_additional_bathroom').click(function () {
        $('#additional_bathrooms').append($('#additional_bathroom_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now()));
        bindToggleAdditionalBathroom();
    });

    // remove additional bathroom on uncheck
    function bindToggleAdditionalBathroom() {
        $('.toggle_additional_bathroom').unbind('click').click(function () {
            if (!$(this).is(':checked')) {
                let target = $(this).closest('.additional_bathroom_content');
                target.remove();
                $(document).trigger('updatePreviewEvent');
            }
        });
    }

    bindToggleAdditionalBathroom();

    // add additional room
    $('#add_additional_room').click(function () {
        $('#additional_rooms').append($('#additional_room_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now()));
        $('#additional_rooms').find('.checkbox_input_text').last().focus();
        bindToggleAdditionalRoom();
    });

    // remove additional room on uncheck
    function bindToggleAdditionalRoom() {
        $('.toggle_additional_room').unbind('click').click(function () {
            if (!$(this).is(':checked')) {
                let target = $(this).closest('.additional_room_content');
                target.remove();
                $(document).trigger('updatePreviewEvent');
            }
        });
    }

    bindToggleAdditionalRoom();

    // private parking
    $('#has_private_parking').click(function () {
        $('#private_parking').removeClass("sliding-up").slideDown();
    });
    $('#has_not_private_parking').click(function () {
        $('#private_parking').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    // private parking fee
    $('#private_parking_fee_included_in_rent').click(function () {
        $('#private_parking_fee_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });
    $('#private_parking_fee_not_included_in_rent').click(function () {
        $('#private_parking_fee_container').removeClass("sliding-up").slideDown();
    });

    // private storage
    $('#has_private_storage').click(function () {
        $('#private_storage').removeClass("sliding-up").slideDown();
    });
    $('#has_not_private_storage').click(function () {
        $('#private_storage').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    // private storage fee
    $('#private_storage_fee_included_in_rent').click(function () {
        $('#private_storage_fee_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });
    $('#private_storage_fee_not_included_in_rent').click(function () {
        $('#private_storage_fee_container').removeClass("sliding-up").slideDown();
    });

});
