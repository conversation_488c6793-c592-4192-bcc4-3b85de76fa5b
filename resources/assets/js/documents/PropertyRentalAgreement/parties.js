$(document).ready(function () {

    let max_additional_tenants = 10;
    let max_additional_landlords = 10;

    // toggle authorized landlord persons

    function bindToggleAuthorizedLandlordPersonsExist() {
        $('.authorized_landlord_persons_exist').unbind('click').click(function () {

            // if tooltip was clicked, ignore and exit
            if($('.tooltip-inner:visible').length){
                $(this).prop("checked", !$(this).is(':checked'));
                return null;
            }

            if ($(this).is(':checked')) {

                // if none exist yet, add one
                if (!$(this).closest('.authorized_landlord_persons').find('.authorized_landlord_person_content').length) {
                    $(this).closest('.authorized_landlord_persons').find('.add_authorized_landlord_person').trigger({
                        type: 'click',
                        programmatic: true
                    });
                }

                $(this).closest('.authorized_landlord_persons').find('.authorized_landlord_persons_container').removeClass("sliding-up").slideDown();
                $(this).closest('.authorized_landlord_persons').find('.add_authorized_landlord_person_container').removeClass("sliding-up").slideDown();
            } else {
                $(this).closest('.authorized_landlord_persons').find('.authorized_landlord_persons_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
                $(this).closest('.authorized_landlord_persons').find('.add_authorized_landlord_person_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            }
        });
    }

    // add authorized landlord person

    function bindAddAuthorizedLandlordPerson() {
        $('.add_authorized_landlord_person').unbind('click').click(function () {
            let person_id = $(this).data('id');
            let person_index = Date.now();  // we just care that it is unique
            let new_authorized_landlord_person = $('#authorized_landlord_person_template').html().replace(new RegExp('{ID}', 'g'), person_id).replace(new RegExp('{INDEX}', 'g'), person_index);
            $(this).closest('.authorized_landlord_persons').find('.authorized_landlord_persons_container').append(new_authorized_landlord_person);
            bindRemoveAuthorizedLandlordPerson();

            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            });

            window.bindMapsAutofillInputs($('.maps-autofill-input'));
        });
    }


    // remove authorized landlord person
    function bindRemoveAuthorizedLandlordPerson() {
        $('.remove_authorized_landlord_person').unbind('click').click(function () {
            let target = $(this).closest('.authorized_landlord_person_content');

            target.hide('slow', function () {
                // if last one removed, hide divs, uncheck checkbox
                if ($(this).closest('.authorized_landlord_persons').find('.remove_authorized_landlord_person').length == 1) {
                    $(this).closest('.authorized_landlord_persons').find('.authorized_landlord_persons_container').hide();
                    $(this).closest('.authorized_landlord_persons').find('.add_authorized_landlord_person_container').hide();
                    $('.authorized_landlord_persons_exist').prop('checked', false);
                }

                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindToggleAuthorizedLandlordPersonsExist();
    bindAddAuthorizedLandlordPerson();
    bindRemoveAuthorizedLandlordPerson();

    // toggle spouse tenant
    $('#spouse_tenant_exists').click(function () {

        // if tooltip was clicked, ignore and exit
        if($('.tooltip-inner:visible').length){
            $(this).prop("checked", !$(this).is(':checked'));
            return null;
        }

        if ($(this).is(':checked')) {
            $('#spouse_tenant').removeClass("sliding-up").slideDown();
        } else {
            $('#spouse_tenant').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        }
    });

    // add additional tenant
    $('#add_additional_tenant').click(function (e) {

        // prevent propagation if tooltip was clicked
        if($(e.target).is("i")){
            return;
        } // otherwise, hide all tooltips
        else{
            $(".tooltip").tooltip('hide');
        }

        if ($('.remove_additional_tenant:visible').length <= max_additional_tenants) {
            let new_tenant = $('#additional_tenant_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
            $('#additional_tenants').append(new_tenant);
            bindRemoveAdditionalTenant();
            window.bindMapsAutofillInputs($('.maps-autofill-input'));
        }
    });

    // remove additional tenant
    function bindRemoveAdditionalTenant() {
        $('.remove_additional_tenant').unbind('click').click(function () {
            let target = $(this).closest('.additional_tenant_content');

            target.hide('slow', function () {
                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindRemoveAdditionalTenant();

    // add additional landlord
    $('#add_additional_landlord').click(function () {
        if ($('.remove_additional_landlord:visible').length <= max_additional_landlords) {
            let new_landlord = $('#additional_landlord_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
            $('#additional_landlords').append(new_landlord);
            bindRemoveAdditionalLandlord();
            bindAddAuthorizedLandlordPerson();
            bindRemoveAuthorizedLandlordPerson();
            bindToggleAuthorizedLandlordPersonsExist();
            window.bindMapsAutofillInputs($('.maps-autofill-input'));

            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            });

            bindLandlordCosigner();
        }


    });

    function bindLandlordCosigner() {
        $('.landlord_cosigner_exists').unbind('click').click(function () {

            // if tooltip was clicked, ignore and exit
            if($('.tooltip-inner:visible').length){
                $(this).prop("checked", !$(this).is(':checked'));
                return null;
            }

            if ($(this).is(':checked')) {
                $(this).closest('.card').find('.landlord_cosigner_container').removeClass("sliding-up").slideDown();
            } else {
                $(this).closest('.card').find('.landlord_cosigner_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
            }
        });
    }

    bindLandlordCosigner();

    // remove additional landlord
    function bindRemoveAdditionalLandlord() {
        $('.remove_additional_landlord').unbind('click').click(function () {
            let target = $(this).closest('.additional_landlord_content');

            target.hide('slow', function () {
                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindRemoveAdditionalLandlord();

    // generate parties
    function appendParties(e) {
        // generate and save party fields
        let parties = [];

        // if empty authorized landlord persons
        if (!$('#landlord_content').find('.authorized_landlord_person_content:visible:not(.sliding-up)').length) {
            // add landlord
            parties.push({
                label: "Najmodavac (" + $('input[name="landlord_name"]').val().trim() + ")",
                name: $('input[name="landlord_name"]').val().trim(),
                side: "left"
            })
        } else {
            // else, add authorized persons
            $('#landlord_content').find('.authorized_landlord_person_content:visible:not(.sliding-up)').each(function () {
                parties.push({
                    label: "Za Najmodavca (" + $('input[name="landlord_name"]').val().trim() + ") <br> " + $(this).find('input')[1].value.trim() + " - " + $(this).find('input')[0].value.trim(),
                    name: $(this).find('input')[1].value.trim(),
                    side: "left"
                })
            })
        }

        // add landlord cosigner
        if ($('#landlord_content').find('.landlord_cosigner_container:visible').length) {

            let container = $('#landlord_content').find('.landlord_cosigner_container:visible');

            let cosigner_type = container.find('select')[0].value.trim();

            if (cosigner_type == "0") {
                cosigner_type = "Bračni drug";
            } else if (cosigner_type == "1") {
                cosigner_type = "Izvanbračni drug";
            } else if (cosigner_type == "2") {
                cosigner_type = "Životni partner";
            }

            // append landlord cosigner
            parties.push({
                label: cosigner_type + " Najmodavca " + $('input[name="landlord_name"]').val().trim() + " (" + container.find('input')[0].value.trim() + ")",
                name: container.find('input')[0].value.trim(),
                side: "left"
            })
        }

        // if not empty additional landlords
        if ($('.additional_landlord_content:visible').length) {
            $('.additional_landlord_content:visible').each(function () {
                // if empty authorized landlord persons
                if (!$(this).find('.authorized_landlord_person_content:visible:not(.sliding-up)').length) {
                    // add landlord
                    parties.push({
                        label: "Najmodavac (" + $(this).find('input')[0].value.trim() + ")",
                        name: $(this).find('input')[0].value.trim(),
                        side: "left"
                    })
                } else {
                    // else, add authorized persons
                    let landlord_name = $(this).find('input')[0].value.trim();
                    $(this).find('.authorized_landlord_person_content:visible:not(.sliding-up)').each(function () {
                        parties.push({
                            label: "Za Najmodavca (" + landlord_name + ") <br> " + $(this).find('input')[1].value.trim() + " - " + $(this).find('input')[0].value.trim(),
                            name: $(this).find('input')[1].value.trim(),
                            side: "left"
                        })
                    })
                }

                // add landlord cosigner
                if ($(this).find('.landlord_cosigner_container:visible').length) {

                    let container = $(this).find('.landlord_cosigner_container:visible');
                    let landlord_name = $(this).find('input')[0].value.trim();

                    let cosigner_type = container.find('select')[0].value.trim();

                    if (cosigner_type == "0") {
                        cosigner_type = "Bračni drug";
                    } else if (cosigner_type == "1") {
                        cosigner_type = "Izvanbračni drug";
                    } else if (cosigner_type == "2") {
                        cosigner_type = "Životni partner";
                    }

                    parties.push({
                        label: cosigner_type + " Najmodavca " + landlord_name + " (" + container.find('input')[0].value.trim() + ")",
                        name: container.find('input')[0].value.trim(),
                        side: "left"
                    })
                }
            })
        }

        parties.push({
            label: "Najmoprimac (" + $('#tenant_content').find('input')[0].value.trim() + ")",
            name: $('#tenant_content').find('input')[0].value.trim(),
            side: "right"
        });

        if ($('#spouse_tenant:visible').length) {
            parties.push({
                label: "Najmoprimac (" + $('#spouse_tenant').find('input')[0].value.trim() + ")",
                name: $('#spouse_tenant').find('input')[0].value.trim(),
                side: "right"
            });
        }

        // create parties element, populate it and append to form
        if (!$('#parties').length) {
            $('#form-container').find('form').append("<input type='hidden' name='parties' id='parties'>");
        }

        $('#parties').val(JSON.stringify(parties));
    }


    window.appendParties = appendParties;
});
