$(document).ready(function () {

    // price payment type switch
    $('#price_payment_type_full_radio').click(function () {
        $('#price_payment_installments_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        $('#price_payment_full_container').removeClass("sliding-up").slideDown();
    });
    $('#price_payment_type_installments_radio').click(function () {
        $('#price_payment_full_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        $('#price_payment_installments_container').removeClass("sliding-up").slideDown();
    });

    // change installment number

    $('#price_payment_installments_number').on('change', function (e) {
        let installments_number = $(this).val();

        let installments_html = "";

        for (let i = 0; i < installments_number; i++) {
            installments_html += $('#price_installment_template').html().replace(new RegExp('{INDEX}', 'g'), (i + 1))
                .replace(new RegExp('{KEY}', 'g'), i);
        }

        $('#price_payment_installments').html(installments_html);
    });

});
