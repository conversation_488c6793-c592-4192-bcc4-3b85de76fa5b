$(document).ready(function () {
    // container toggle
    $('#cancellation_fee_included_in_price').on('click', function (e) {
        $('#cancellation_fee_to_be_refunded_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

        $('#financing_method_label').html('ostatka kupoprodajne cijene (iznos kupoprodajne cijene umanjen za iznos kapare)');
        $('#financing_method_radio_1').html('kupoprodajne cijene umanjene za iznos kapare');
        $('#financing_method_radio_2').html('kupoprodajne cijene umanjene za iznos kapare');
        $('#financing_method_radio_3').html('kupoprodajnu cijenu umanjenu za iznos kapare');
    });

    $('#cancellation_fee_to_be_refunded').on('click', function (e) {
        $('#cancellation_fee_to_be_refunded_container').removeClass("sliding-up").slideDown();

        $('#financing_method_label').html('kupoprodajne cijene');
        $('#financing_method_radio_1').html('kupoprodajne cijene');
        $('#financing_method_radio_2').html('kupoprodajne cijene');
        $('#financing_method_radio_3').html('kupoprodajnu cijenu');
    });

    $('#cancellation_fee_is_agreed').on('click', function (e) {
        $('#cancellation_fee_container').removeClass("sliding-up").slideDown();

        if ($('#cancellation_fee_included_in_price').is(':checked')) {
            $('#financing_method_label').html('ostatka kupoprodajne cijene (iznos kupoprodajne cijene umanjen za iznos kapare)');
            $('#financing_method_radio_1').html('kupoprodajne cijene umanjene za iznos kapare');
            $('#financing_method_radio_2').html('kupoprodajne cijene umanjene za iznos kapare');
            $('#financing_method_radio_3').html('kupoprodajnu cijenu umanjenu za iznos kapare');
        }

    });

    $('#cancellation_fee_is_not_agreed').on('click', function (e) {
        $('#cancellation_fee_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

        $('#financing_method_label').html('kupoprodajne cijene');
        $('#financing_method_radio_1').html('kupoprodajne cijene');
        $('#financing_method_radio_2').html('kupoprodajne cijene');
        $('#financing_method_radio_3').html('kupoprodajnu cijenu');
    });

    $('#financing_method_loan, #financing_method_loan_and_own').on('click', function (e) {
        $('#financing_method_loan_container').removeClass("sliding-up").slideDown();
    });

    $('#financing_method_own').on('click', function (e) {
        $('#financing_method_loan_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    $('#credit_institution_is_determined').on('click', function (e) {
        $('#credit_insitution_container').removeClass("sliding-up").slideDown();
    });

    $('#credit_institution_is_not_determined').on('click', function (e) {
        $('#credit_insitution_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    $('.mortgage_is_for_loan').on('click', function (e) {
        $(this).closest('.encumbrances_container').find('.mortgage_is_for_loan_container').removeClass("sliding-up").slideDown();
    });

    $('.mortgage_is_not_for_loan').on('click', function (e) {
        $(this).closest('.encumbrances_container').find('.mortgage_is_for_loan_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    $('.is_realestate_tenant_purchase').on('click', function (e) {
        $(this).closest('.encumbrances_container').find('.realestate_tenant_purchase_container').removeClass("sliding-up").slideDown();
    });

    $('.is_not_realestate_tenant_purchase').on('click', function (e) {
        $(this).closest('.encumbrances_container').find('.realestate_tenant_purchase_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    $('.buyout_payment_type_1').on('click', function (e) {
        $(this).closest('.buyout_payment_type_container').find('.buyout_payment_type_1_container').removeClass("sliding-up").slideDown();
    });

    $('.buyout_payment_type_2').on('click', function (e) {
        $(this).closest('.buyout_payment_type_container').find('.buyout_payment_type_1_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });


});
