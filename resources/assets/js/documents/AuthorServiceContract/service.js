$(document).ready(function () {
    // service responsibilities

    // add service responsibility
    $('#add_service_responsibility').click(function () {
        let new_service_responsibility = $('#service_responsibility_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
        $('#service_responsibilities').append(new_service_responsibility);
        bindRemoveServiceResponsibility();
        $('.service_responsibility_content:last input').focus();
    });

    function bindRemoveServiceResponsibility() {
        $('.remove_service_responsibility').unbind('click').click(function () {
            let target = $(this).closest('.service_responsibility_content');

            if ($('.remove_service_responsibility:visible').length > 1) {
                target.remove();
                $(document).trigger('updatePreviewEvent');
                $('.service_responsibility_content:last input').focus();
            }

        });
    }

    bindRemoveServiceResponsibility();

    // service use type toggle
    $('#service_use_type_specific').on('click', function () {
        $('#service_use_type_specific_container').removeClass("sliding-up").slideDown();
    });

    $('#service_use_type_unspecific').on('click', function () {
        $('#service_use_type_specific_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });


    // add specific service use case
    $('#add_specific_service_use_case').click(function () {
        let new_specific_use_case = $('#specific_service_use_case_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
        $('#specific_service_use_cases').append(new_specific_use_case);
        bindRemoveUnspecificServiceUseCase();
    });

    function bindRemoveUnspecificServiceUseCase() {
        $('.remove_specific_service_use_case').unbind('click').click(function () {
            let target = $(this).closest('.specific_service_use_case_content');

            if ($('.remove_specific_service_use_case:visible').length > 1) {
                target.remove();
                $(document).trigger('updatePreviewEvent');
            }

        });
    }

    bindRemoveUnspecificServiceUseCase();

    // service exclusive rights

    $('#service_exclusive_rights').on('click', function () {
        $('#service_exclusive_rights_container').removeClass("sliding-up").slideDown();
    });

    $('#service_non_exclusive_rights').on('click', function () {
        $('#service_exclusive_rights_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    $('#service_has_content_use_restrictions').on('click', function () {
        $('#service_has_content_use_restrictions_container').removeClass("sliding-up").slideDown();
    });

    $('#service_has_not_content_use_restrictions').on('click', function () {
        $('#service_has_content_use_restrictions_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });


    $('#service_has_time_use_restrictions').on('click', function () {
        $('#service_has_time_use_restrictions_container').removeClass("sliding-up").slideDown();
    });

    $('#service_has_not_time_use_restrictions').on('click', function () {
        $('#service_has_time_use_restrictions_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    $('#service_has_location_use_restrictions').on('click', function () {
        $('#service_has_location_use_restrictions_container').removeClass("sliding-up").slideDown();
    });

    $('#service_has_not_location_use_restrictions').on('click', function () {
        $('#service_has_location_use_restrictions_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });


    // add service content use restriction
    $('#add_service_content_use_restriction').click(function () {
        let new_service_content_use_restriction = $('#service_content_use_restriction_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
        $('#service_content_use_restrictions').append(new_service_content_use_restriction);
        bindRemoveServiceContentUseRestriction();
    });

    function bindRemoveServiceContentUseRestriction() {
        $('.remove_service_content_use_restriction').unbind('click').click(function () {
            let target = $(this).closest('.service_content_use_restriction_content');

            if ($('.remove_service_content_use_restriction:visible').length > 1) {
                target.remove();
                $(document).trigger('updatePreviewEvent');
            }

        });
    }

    bindRemoveServiceContentUseRestriction();

    // on enter, add double line break (to be converted to paragraph)
    $('#service_description').keypress(function(e) {
        if (e.which == 13) {
            e.preventDefault();
            let s = $(this).val();
            $(this).val(s+"\n\n");
        }
    }).keydown(function(e){
        if(e.which == 8) {
            let s = $(this).val();
            let charToBeDeletedUnicode = s.charCodeAt(s.length-1);

            // if new line, remove one extra line break
            if(charToBeDeletedUnicode === 10){
                $(this).val(s.substring(0, s.length - 1));
            }
        }
    });

});