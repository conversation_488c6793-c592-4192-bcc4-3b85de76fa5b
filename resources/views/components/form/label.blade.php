@if(!empty($label))
    <label for="{{ $for }}">
        @if($image_tooltip)
            @if(str_word_count($label) > 1)
                {!! substr($label, 0, -strlen(strrchr($label,' '))) !!}

                <span style="white-space: nowrap">
                   {!! strrchr($label,' ') !!}
                    <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>
                </span>
            @else
                {!! $label !!}
                <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>
            @endif
        @elseif($tooltip)
            @if(str_word_count($label) > 1)
                {!! substr($label, 0, -strlen(strrchr($label,' '))) !!}

                <span style="white-space: nowrap">
                   {!! strrchr($label,' ') !!}
                     <span data-toggle="tooltip"
                           data-original-title="{{ $tooltip }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                    </span>
                </span>
            @else
                {!! $label !!}
                <span data-toggle="tooltip"
                      data-original-title="{{ $tooltip }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                </span>
            @endif
        @else
            {!! $label !!}
        @endif
    </label>
@endif
