<div class="row mb-3 mt-2">
    <div class="col-lg-12">
        <label>
            <strong>
                <PERSON><PERSON><PERSON> <PERSON>
                <a data-toggle="tooltip" data-original-title="<PERSON>limo da unesete podatke osobe na koju će glasiti račun za prijevod.">
                    <i class="fa fa-info-circle"></i>
                </a>
            </strong>
        </label>
        <div class="custom-control custom-radio">
            <input checked value="individual" type="radio" id="person-type-individual" name="person_type"
                   class="custom-control-input">
            <label class="custom-control-label" for="person-type-individual"><PERSON><PERSON><PERSON> je fizička osoba</label>
        </div>
        <div class="custom-control custom-radio">
            <input value="business" type="radio" id="person-type-business" name="person_type"
                   class="custom-control-input">
            <label class="custom-control-label" for="person-type-business"><PERSON><PERSON><PERSON> je pravna osoba</label>
        </div>
    </div>
</div>
<hr/>
<div class="row">
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="name">
            <strong @if(old('person_type') == 'business') style="display: none" @endif class="person-type-individual">Ime i prezime</strong>
            <strong @if(empty(old('person_type')) || old('person_type') == 'individual') style="display: none" @endif class="person-type-business">Naziv</strong>
        </label>

        <input
            name="name"
            value="{{ auth()->user() ? auth()->user()->name : '' }}"
            placeholder="@if(old('person_type') == 'business') Unesi naziv... @else Unesi ime i prezime... @endif"
            type="text"
            id="name"
            class="form-control"
            required
        >
    </div>
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="email">
            <strong>Adresa e-pošte</strong>
        </label>
        <input name="email" value="{{ auth()->user() ? auth()->user()->email : '' }}" placeholder="Unesi adresu e-pošte..." type="email" id="email" class="form-control" required>
    </div>
</div>
<div class="row mt-0 mt-lg-2">
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="address">
            <strong>Adresa</strong>
        </label>
        <input name="address" placeholder="Unesi adresu..." type="text" id="address"
               class="form-control" required>
    </div>
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="city">
            <strong>Grad</strong>
        </label>
        <input name="city" placeholder="Unesi grad..." type="text" id="city" class="form-control"
               required>
    </div>
</div>
<div class="row mt-0 mt-lg-2">
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="postal-code">
            <strong>Poštanski broj</strong>
        </label>
        <input name="postal_code" placeholder="Unesi poštanski broj..." type="text" id="postal-code"
               class="form-control" required>
    </div>
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="country">
            <strong>Država</strong>
        </label>
        <select name="country" id="country" class="form-control">
            @foreach(\App\Helpers\ISOCountryCodes::get() as $iso => $country)
                <option @if($iso === 'HR') selected @endif value="{{ $iso }}">{{ $country }}</option>
            @endforeach
        </select>
    </div>
</div>
<div class="row mt-0 mt-lg-2">
    <div class="col-lg-6 mb-3 mb-lg-0">
        <label for="postal-code">
            <strong>OIB <small class="person-type-individual">(opcionalno)</small></strong>
        </label>
        <input name="oib" placeholder="Unesi OIB..." type="number" id="oib"
               class="form-control">
    </div>
    <div class="col-lg-6">
        <label for="country">
            <strong>Telefon/mobitel <small class="normal-translation" style="display: none">(opcionalno)</small></strong>
        </label>
        <input name="phone" placeholder="Unesi broj telefona/mobitela..." type="text" id="phone"
               class="form-control" required>
    </div>
</div>