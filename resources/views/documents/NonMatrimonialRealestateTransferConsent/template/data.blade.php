@extends($builder->getLayout())
@section('content')
    @php

        $data["issuer_name"] = $builder->get("issuer_name");
        $data["issuer_address"] = $builder->get("issuer_address");
        $data["issuer_city"] = $builder->get("issuer_city");
        $data["issuer_postal_code"] = $builder->get("issuer_postal_code");
        $data["issuer_country"] = $builder->get("issuer_country");
        $data["issuer_oib"] = $builder->get("issuer_oib");

		$issuer_type = $builder->get("issuer_type", false);
        
        $data["issuer_type"] = "bračni drug zemljišnoknjižnog vlasnika";
        $data["acquisition_type"] = "bračnu stečevinu";

		if($issuer_type == 1){
            $data["issuer_type"] = "izvanbračni drug zemljišnoknjižnog vlasnika";
			$data["acquisition_type"] = "izvanbračnu stečevinu";
        }
        elseif($issuer_type == 2){
            $data["issuer_type"] = "životni partner zemljišnoknjižnog vlasnika";
			$data["acquisition_type"] = "partnersku stečevinu";
        }
		elseif($issuer_type == 3){
            $data["issuer_type"] = "bivši bračni drug zemljišnoknjižnog vlasnika";
			$data["acquisition_type"] = "bračnu stečevinu";
        }
		elseif($issuer_type == 4){
            $data["issuer_type"] = "bivši izvanbračni drug zemljišnoknjižnog vlasnika";
			$data["acquisition_type"] = "izvanbračnu stečevinu";
        }
		elseif($issuer_type == 5){
            $data["issuer_type"] = "bivši životni partner zemljišnoknjižnog vlasnika";
			$data["acquisition_type"] = "partnersku stečevinu";
        }
		elseif($issuer_type == 6){
            $data["issuer_type"] = "zemljišnoknjižni vlasnik";
			$data["acquisition_type"] = "bračnu, izvanbračnu ni partnersku stečevinu";
        }

		// realestate
        $data["additional_realestates"] = $builder->get("additional_realestates", false);

        // standardize array keys for additional_realestates
        if(!empty($data['additional_realestates'])){

            // standardize array keys
            $data['additional_realestates'] = array_values($data['additional_realestates']);

            foreach($data['additional_realestates'] as &$_additional_realestate){

                if(!empty($_additional_realestate['type_particular_additional_land_plots'])){
                    $_additional_realestate['type_particular_additional_land_plots'] = array_values($_additional_realestate['type_particular_additional_land_plots']);
                }

                if(!empty($_additional_realestate['type_other_additional_land_plots'])){
                    $_additional_realestate['type_other_additional_land_plots'] = array_values($_additional_realestate['type_other_additional_land_plots']);

                    foreach($_additional_realestate['type_other_additional_land_plots'] as &$_additional_land_plot) {
                        if(!empty($_additional_land_plot['type_other_additional_possession_area_and_identification'])) {
                            $_additional_land_plot['type_other_additional_possession_area_and_identification'] = array_values($_additional_land_plot['type_other_additional_possession_area_and_identification']);
                        }
                    }

                    unset($_additional_land_plot);
                }

                if(!empty($_additional_realestate['type_other_additional_possession_area_and_identification'])){
                    $_additional_realestate['type_other_additional_possession_area_and_identification'] = array_values($_additional_realestate['type_other_additional_possession_area_and_identification']);
                }

            }

            unset($_additional_realestate); // always unset if looping by reference!
        }

        $data['realestate_type'] = $builder->get('realestate_type', false) ?: 'particular';
        $data['type_particular_municipal_court'] = $builder->get('type_particular_municipal_court');
        $data['type_particular_land_registry'] = $builder->get('type_particular_land_registry');
        $data['type_particular_cadastral_municipality'] = $builder->get('type_particular_cadastral_municipality');
        $data['type_particular_land_registry_folio_number'] = $builder->get('type_particular_land_registry_folio_number');
        $data['type_particular_possession_number'] = $builder->get('type_particular_possession_number');
        $data['type_particular_possession_area'] = $builder->get('type_particular_possession_area');
		$data["type_particular_possession_area_type"] = $builder->get("type_particular_possession_area_type", null);
        $data['type_particular_possession_identification'] = $builder->get('type_particular_possession_identification');
        $data['type_particular_ownership_sheet_data'] = $builder->get('type_particular_ownership_sheet_data');

        $data["type_particular_additional_land_plots"] = $builder->get("type_particular_additional_land_plots", false);
        if(!empty($data['type_particular_additional_land_plots'])){
            $data['type_particular_additional_land_plots'] = array_values($data['type_particular_additional_land_plots']);
        }

        $data['type_flat_municipal_court'] = $builder->get('type_flat_municipal_court');
        $data['type_flat_land_registry'] = $builder->get('type_flat_land_registry');
        $data['type_flat_deposited_contracts_book_name'] = $builder->get('type_flat_deposited_contracts_book_name');
        $data['type_flat_subfolio_number'] = $builder->get('type_flat_subfolio_number');
        $data['type_flat_folio_number'] = $builder->get('type_flat_folio_number', false);
        $data['type_flat_possession_sheet_section_one_data'] = $builder->get('type_flat_possession_sheet_section_one_data');
        $data['type_flat_possession_sheet_section_two_data'] = $builder->get('type_flat_possession_sheet_section_two_data');

        $data['type_other_municipal_court'] = $builder->get('type_other_municipal_court');
        $data['type_other_land_registry'] = $builder->get('type_other_land_registry');
        $data['type_other_cadastral_municipality'] = $builder->get('type_other_cadastral_municipality');
        $data['type_other_land_registry_folio_number'] = $builder->get('type_other_land_registry_folio_number');
        $data['type_other_possession_number'] = $builder->get('type_other_possession_number');
        $data['type_other_possession_area'] = $builder->get('type_other_possession_area');
		$data["type_other_possession_area_type"] = $builder->get("type_other_possession_area_type", null);
        $data['type_other_possession_identification'] = $builder->get('type_other_possession_identification');

        $data["type_other_additional_possession_area_and_identification"] = $builder->get("type_other_additional_possession_area_and_identification", false);
        if(!empty($data['type_other_additional_possession_area_and_identification'])){
            $data['type_other_additional_possession_area_and_identification'] = array_values($data['type_other_additional_possession_area_and_identification']);
        }

        $data["type_other_additional_land_plots"] = $builder->get("type_other_additional_land_plots", false);
        if(!empty($data['type_other_additional_land_plots'])){
            $data['type_other_additional_land_plots'] = array_values($data['type_other_additional_land_plots']);

            foreach($data['type_other_additional_land_plots'] as &$_type_other_additional_land_plot) {
                if(!empty($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'])) {
                    $_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'] = array_values($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification']);
                }
            }

            unset($_type_other_additional_land_plot);
        }

        $data['registered_owner_has_full_realestate_ownership'] = $builder->get('registered_owner_has_full_realestate_ownership', false);
        $data['registered_owner_realestate_ownership_ratio_numerator'] = $builder->get('registered_owner_realestate_ownership_ratio_numerator', false);
        $data['registered_owner_realestate_ownership_ratio_denominator'] = $builder->get('registered_owner_realestate_ownership_ratio_denominator', false);

        $data['is_full_disposal'] = $builder->get('is_full_disposal', false);
        $data['partial_disposal_numerator'] = $builder->get('partial_disposal_numerator', false);
        $data['partial_disposal_denominator'] = $builder->get('partial_disposal_denominator', false);
		

		// final provisions
        $data["consent_form_date"] = $builder->get("consent_form_date");
        $data["consent_form_place"] = $builder->get("consent_form_place");
        $data["custom_provisions"] = $builder->get("custom_provisions", false);

		// parties
		$data['parties'] = $builder->getParties()->where('side', 'left')->values()->toArray();

    @endphp

    @include($builder->render_view, ['builder' => $builder, 'data' => $data])

@endsection
