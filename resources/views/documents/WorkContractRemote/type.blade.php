@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Vrsta i trajanje ugovora
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            <label>1. Sklapa li se ugovor na neodređeno ili na određeno vrijeme?
                            <span data-toggle="tooltip"
                                  data-original-title="Prema Zakonu o radu, ugovor o radu u pravilu se sklapa na neodređeno vrijeme. Ugovor o radu može se iznimno sklopiti na određeno vrijeme za zasnivanje radnog odnosa čiji je prestanak unaprijed utvrđen rokom, izvršenjem određenog posla ili nastupanjem određenog događaja.">
                        <i class="fa fa-info-circle"></i></span>
                            </label>
                            {{ Form::fRadio($model, 'contract_type', 'indefinite', 'Ugovor o radu na neodređeno vrijeme', ['id' => 'indefinite_contract','checked' => !isset($model->contract_type)]) }}
                            {{ Form::fRadio($model, 'contract_type', 'definite', 'Ugovor o radu na određeno vrijeme', ['id' => 'definite_contract', ])}}
                        </div>
                    </div>
                    <div id="indefinite_contract_container"
                         style="{{ isset($model->contract_type) &&  $model->contract_type == 'definite' ? "display:none" : ""}}">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'is_subsequent_indefinite_contract','2. Je li ovo prvi ugovor o radu između radnika i poslodavca ili između njih već postoji radni odnos na temelju prethodnog ugovora o radu?', 'Opciju "Postoji prethodni ugovor" odaberi ako se ovim novim ugovorom o radu nastavlja već postojeći radni odnos između poslodavca i radnika, ali pod drukčijim uvjetima npr. ako je radnik do sada bio zaposlen na određeno vrijeme pa sada poslodavac i radnik sklapaju ugovor o radu na neodređeno vrijeme, ako je radnik i do sada bio zaposlen na neodređeno vrijeme, ali poslodavac i radnik ovim novim ugovorom o radu drukčije ugovoraju uvjete rada (npr. radnik prelazi na drugo radno mjesto, radnik će raditi u drugom mjestu rada, radniku se povećava plaća), i slično.' ) }}
                                {{ Form::fRadio($model, 'is_subsequent_indefinite_contract', 0, 'Ovo je prvi ugovor', ['id' => 'is_first_indefinite_contract','checked' => !isset($model->is_subsequent_indefinite_contract)])}}
                                {{ Form::fRadio($model, 'is_subsequent_indefinite_contract', 1, 'Postoji prethodni ugovor', ['id' => 'is_subsequent_indefinite_contract', ])}}
                            </div>
                        </div>
                    </div>
                    <div id="definite_contract_container"
                         style="{{ !isset($model->contract_type) ||  $model->contract_type == 'indefinite' ? "display:none" : ""}}">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fText($model, 'expected_duration', null, '<span class="dot"></span> Upiši očekivano trajanje ugovora o radu na određeno vrijeme', ['placeholder' => 'Npr: do 31. 12. 2020., 6 mjeseci, i slično', 'data-force-start-case' => 'lower']) }}
                            </div>
                        </div>
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'is_subsequent_definite_contract', '2. Je li ovo prvi ili uzastopni ugovor o radu na određeno vrijeme između poslodavca i radnika?', 'Prema Zakonu o radu, ukupno trajanje svih uzastopnih ugovora o radu sklopljenih na određeno vrijeme, uključujući i prvi ugovor o radu, ne smije biti neprekinuto duže od tri godine, osim ako je to potrebno zbog zamjene privremeno nenazočnog radnika ili je zbog neki drugih objektivnih razloga dopušteno zakonom ili kolektivnim ugovorom. Prekid kraći od dva mjeseca ne smatra se prekidom razdoblja od tri godine. Prvi ugovor o radu sklopljen na određeno vrijeme može se sklopiti na vrijeme dulje od tri godine, ali nakon isteka tog ugovora u pravilu se ne smije sklopiti uzastopni ugovor o radu na određeno vrijeme.') }}
                                {{ Form::fRadio($model, 'is_subsequent_definite_contract', 0, 'Ovo je prvi ugovor', ['id' => 'is_first_definite_contract', 'checked' => !isset($model->is_subsequent_definite_contract)])}}
                                {{ Form::fRadio($model, 'is_subsequent_definite_contract', 1, 'Ovo je uzastopni ugovor', ['id' => 'is_subsequent_definite_contract', ])}}
                            </div>
                        </div>
                        <div id="is_subsequent_definite_contract_container"
                             style="{{ !isset($model->is_subsequent_definite_contract) || $model->is_subsequent_definite_contract == 0 ? "display:none" : "" }}">
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    <hr/>
                                    {{ Form::fTextArea($model, 'subsequent_definite_contract_reason', null, '<span class="dot"></span> Koji je razlog sklapanja uzastopnog ugovora o radu na određeno vrijeme?', ['data-force-start-case' => 'lower', 'placeholder' => 'Npr: zamjena radnice Ive Ivić koja je na rodiljnom dopustu, implementacija novog računovodstvenog programa, privremeno povećanje opsega poslova zbog reorganizacije poslovanja poslodavca, itd.', 'rows' => 5]) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="common_subsequent_contract_container"
                         style="{{ (empty($model->is_subsequent_indefinite_contract) && empty($model->is_subsequent_definite_contract)) ? "display:none" : ""  }}">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'previous_contract_date','<span class="dot"></span> Kada su poslodavac i radnik sklopili prethodni ugovor o radu?' ) }}
                                {{ Form::fText($model,  'previous_contract_date', null, null, ['class' => 'form-control', 'data-datepicker' => 1, 'autocomplete' => 'off', 'placeholder' => 'Npr: 15. 10. '.date('Y').'.']) }}
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'probation_period', '3. Ugovara li se probni rad radnika?', 'Prema Zakonu o radu, probni rad ne može trajati duže od 6 mjeseci. Ako se radi o ugovoru o radu na određeno vrijeme treba paziti da trajanje probnog rada bude kraće od vremena na koje se sklapa ugovor o radu na određeno vrijeme. Trajanje probnog rada se u kolokvijalnom govoru često naziva i "probni rok".') }}
                            {{ Form::fRadio($model, 'probation_period', 0, 'Ne', ['id' => 'is_not_probation_period', 'checked' => !isset($model->probation_period)])}}
                            {{ Form::fRadio($model, 'probation_period', 1, 'Da', ['id' => 'is_probation_period']) }}
                        </div>
                    </div>
                    <div id="probation_period_container" style="{{ !isset($model->probation_period) || $model->probation_period == 0 ? "display:none" : "" }}">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fText($model, 'probation_period_duration', null, '<span class="dot"></span> Upiši trajanje probnog rada', ['placeholder' => 'Npr: 3 mjeseca', 'data-force-start-case' => 'lower'], null, 'Prema Zakonu o radu, probni rad ne može trajati duže od 6 mjeseci. Ako se radi o ugovoru o radu na određeno vrijeme treba paziti da trajanje probnog rada bude kraće od vremena na koje se sklapa ugovor o radu na određeno vrijeme. Trajanje probnog rada se u kolokvijalnom govoru često naziva i "probni rok".') }}
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'has_work_regulations', '4. Primjenjuje li se kod poslodavca pravilnik o radu?', 'Prema Zakonu o radu, pravilnik o radu ima obvezu donijeti poslodavac koji zapošljava najmanje 20 radnika. Poslodavac koji zapošljava od 1 do 19 radnika nema obvezu donijeti pravilnik o radu, ali to može učiniti.') }}
                            {{ Form::fRadio($model, 'has_work_regulations', 0, 'Ne', [ 'checked' => !isset($model->can_be_cancelled)]) }}
                            {{ Form::fRadio($model, 'has_work_regulations', 1, 'Da')}}
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>


    {{ Form::close() }}
@endsection
