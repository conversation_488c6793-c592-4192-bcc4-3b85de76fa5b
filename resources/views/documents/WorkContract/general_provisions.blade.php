@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                @php $question_index = 1; @endphp
                <div class="card-header">
                    Mjesto i vrsta rada
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'work_location_type', $question_index++.'. Hoće li radnik rad obavljati u stalnom (glavnom) mjestu rada ili u različitim mjestima?', 'Mjesto rada je lokacija na kojoj radnik obavlja poslove iz ugovora o radu. Kao mjesto rada može se navesti točna zemljopina lokacija (npr. Rijeka), ali i odredbe poput "mjesto registriranog sjedišta poslodavca" i slično. Ako zbog prirode posla ne postoji stalno ili glavno mjesto rada ili je ono promjenjivo, prema Zakonu o radu obvezno se mora navesti podatak o različitim mjestima na kojima se rad obavlja ili bi se mogao obavljati (npr. na gradilištima na kojima je Poslodavac angažiran na području Republike Hrvatske).<br><br>Kao mjesto rada može se navesti i točna adresa poslovnog prostora u kojem će radnik obavljati rad (npr. u uredu na adresi Korzo bb, Rijeka). Međutim, ako je u ugovoru o radu navedeno samo jedno konkretno mjesto rada, poslodavac mora voditi računa o tome da ne može jednostrano (odnosno bez suglasnosti radnika) izmijeniti ugovor o radu i radnika poslati na rad na druge lokacije.') }}
                            {{ Form::fRadio($model, 'work_location_type', 'fixed', 'Radnik će rad obavljati u stalnom (glavnom) mjestu rada', ['id' => 'fixed_work_location','checked' => !isset($model->work_location_type)]) }}
                            {{ Form::fRadio($model, 'work_location_type', 'flexible', 'Radnik će rad obavljati u različitim mjestima', ['id' => 'flexible_work_location', ])}}
                        </div>
                    </div>

                    <div id="fixed_work_location_container"
                         style="{{ isset($model->work_location_type) && $model->work_location_type != "fixed" ? "display:none" : null }}">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'work_location', null, '<span class="dot"></span> U kojem mjestu će radnik obavljati rad?', ['placeholder' => 'Npr: u Rijeci, u prostorijama koje koristi Poslodavac u Rijeci, na adresi sjedišta Poslodavca, i slično', 'data-preview' => 'work-location'], null, 'Ovdje mjesto upiši u obliku "u Rijeci", "na adresi sjedišta Poslodavca", "u prostorijama Poslodavca na adresi Korzo bb, Rijeka" i slično. Nemoj upisivati mjesta u obliku "Rijeka", "adresa sjedišta Poslodavca" ili "prostorije Poslodavca na adresi Korzo bb, Rijeka", jer rečenica koja se pojavljuje u ugovoru neće biti jezično ispravna.') }}
                            </div>
                        </div>
                    </div>

                    <div id="flexible_work_location_container"
                         style="{{ !isset($model->work_location_type) || $model->work_location_type != "flexible" ? "display:none" : null }}">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'work_locations', null, '<span class="dot"></span> U kojim mjestima će radnik obavljati rad?', ['placeholder' => 'Npr: u Rijeci, u Puli i u Zagrebu; na gradilištima na kojima je Poslodavac angažiran na području Republike Hrvatske, i slično', 'data-preview' => 'work-locations'], null, 'Ovdje mjesta upiši u obliku "u Rijeci, u Puli i u Zagrebu" ili "na gradilištima na kojima je Poslodavac angažiran na području Republike Hrvatske" i slično. Nemoj upisivati mjesta u obliku "Rijeka, Pula i Zagreb" ili "gradilišta na kojima je Poslodavac angažiran na području Republike Hrvatske", jer rečenica koja se pojavljuje u ugovoru neće biti jezično ispravna.') }}
                            </div>
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'work_title', null, $question_index++ . '. Upiši naziv radnog mjesta, odnosno narav ili vrstu rada na koje se radnik zapošljava', ['placeholder' => 'Npr: serviser rashladnih i klimatizacijskih uređaja']) }}
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'work_responsibilities', $question_index++.'. Upiši kratak popis ili opis poslova koje će radnik obavljati u okviru radnog mjesta na koje se zapošljava', "
                            Npr:
                            <ul style='text-align:left!important;'>
                                <li>instaliranje i montaža rashladnih i klimatizacijskih uređaja</li>
                                <li>preventivni servisi i popravci na rashladnim i klimatizacijskim uređajima</li>
                                <li>podrška kupcima</li>
                                <li>rad na visini</li>
                                <li>vođenje evidencije o obavljenim poslovima i zadacima</li>
                            </ul>
                            ") }}

                            <div id="work_responsibilities" class="dynamic">
                                <div id="work_responsibility_template" class="d-none">
                                    <div class="work_responsibility_content">
                                        <div class="input-group mt-2 mb-2">
                                            <input placeholder="Unesi naziv i kratak opis posla" type="text"
                                                   class="work_responsibilities form-control"
                                                   name="work_responsibilities[{INDEX}]">
                                            <div class="input-group-append remove_work_responsibility"
                                                 style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                @if(isset($model->work_responsibilities))
                                    @foreach($model->work_responsibilities as $i => $_work_responsibility)
                                        <div class="work_responsibility_content">
                                            <div class="input-group mt-2 mb-2">
                                                <input value="{{ $model->work_responsibilities[$i] }}"
                                                       placeholder="Unesi naziv i kratak opis posla" type="text"
                                                       class="work_responsibilities form-control"
                                                       name="work_responsibilities[{{$i}}]">
                                                <div class="input-group-append remove_work_responsibility"
                                                     style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="work_responsibility_content">
                                        <div class="input-group mt-2 mb-2">
                                            <input placeholder="Npr: instaliranje i montaža rashladnih i klimatizacijskih uređaja"
                                                   type="text" class="work_responsibilities form-control"
                                                   name="work_responsibilities[0]">
                                            <div class="input-group-append remove_work_responsibility"
                                                 style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="work_responsibility_content">
                                        <div class="input-group mt-2 mb-2">
                                            <input placeholder="Npr: preventivni servisi i popravci na rashladnim i klimatizacijskim uređajima"
                                                   type="text" class="work_responsibilities form-control"
                                                   name="work_responsibilities[1]">
                                            <div class="input-group-append remove_work_responsibility"
                                                 style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <a class="btn btn-info mt-2" id="add_work_responsibility">+ Dodaj novi opis posla</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                @php $question_index = 1; @endphp
                <div class="card-header">
                    Radno vrijeme i godišnji odmor
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'is_full_time', $question_index++.'. Zapošljava li se radnik na puno ili nepuno radno vrijeme?', 'Prema Zakonu o radu, radno vrijeme je vrijeme u kojem je radnik obvezan obavljati poslove, odnosno u kojem je spreman (raspoloživ) obavljati poslove prema uputama poslodavca, na mjestu gdje se njegovi poslovi obavljaju ili drugom mjestu koje odredi poslodavac. Puno radno vrijeme radnika ne može biti duže od četrdeset sati tjedno. Nepuno radno vrijeme radnika je svako radno vrijeme kraće od punog radnog vremena.') }}
                            {{ Form::fRadio($model, 'is_full_time', 1, 'Puno radno vrijeme', ['id' => 'is_full_time_1', 'checked' => !isset($model->is_full_time)]) }}
                            {{ Form::fRadio($model, 'is_full_time', 0, 'Nepuno radno vrijeme', ['id' => 'is_full_time_0']) }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'week_hours', $question_index++.'. Koliko radnih sati traje redoviti radni tjedan radnika?', (isset($model->is_full_time) && !$model->is_full_time) ? 'Prema Zakonu o radu, nepuno radno vrijeme radnika ne može biti duže od 39 sati tjedno.' : 'Prema Zakonu o radu, puno radno vrijeme radnika ne može biti duže od 40 sati tjedno.') }}

                            @if($model->document->getValue('has_work_regulations'))
                                <div id="week_hours_defined_in_work_regulations"
                                     class="dynamic @if(isset($model->is_full_time) && !$model->is_full_time) d-none @endif">
                                    {{ Form::fRadio($model, 'week_hours', 'defined_in_work_regulations', 'Trajanje redovitog radnog tjedna radnika već je uređeno Pravilnikom o radu poslodavca pa želim da ugovor o radu upućuje na Pravilnik o radu', ['checked' => !isset($model->week_hours) || ($model->week_hours === 'defined_in_collective_agreement' && !$model->document->getValue('has_collective_agreement'))]) }}
                                </div>
                            @endif
                            @if($model->document->getValue('has_collective_agreement'))
                                <div id="week_hours_defined_in_collective_agreement"
                                     class="dynamic @if(isset($model->is_full_time) && !$model->is_full_time) d-none @endif">
                                    {{ Form::fRadio($model, 'week_hours', 'defined_in_collective_agreement', 'Trajanje redovitog radnog tjedna radnika već je uređeno Kolektivnim ugovorom koji se primjenjuje na ovaj radni odnos pa želim da ugovor o radu upućuje na Kolektivni ugovor', ['checked' => (!isset($model->week_hours) && !$model->document->getValue('has_work_regulations')) || (isset($model->week_hours) && $model->week_hours === 'defined_in_work_regulations' && !$model->document->getValue('has_work_regulations'))]) }}
                                </div>
                            @endif
                            <div class="form-check radio-input">
                                <label class="form-check-label">
                                    {{ Form::radio('week_hours', 'custom', null, ['class' => 'form-check-input', 'id' => 'week_hours_custom_cb', 'checked' => (isset($model->is_full_time) && !$model->is_full_time) || (!$model->document->getValue('has_work_regulations') && !$model->document->getValue('has_collective_agreement'))])  }}
                                    {{
                                        Form::number(
                                        'week_hours_custom',
                                        null,
                                        [
											'id' => 'week_hours_custom',
                                            'class' => 'form-control',
                                            'placeholder' => 'Npr: '. (isset($model->is_full_time) && $model->is_full_time == 0 ? 30 : 40),
                                            'max' => isset($model->is_full_time) && $model->is_full_time == 0 ? 39 : 40,
                                        ])
                                    }}
                                </label>
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'working_hours', $question_index++.'. Kako će u pravilu biti raspoređeno radno vrijeme radnika? <small>(opcionalno)</small>', 'Ako ne želiš ove podatke unijeti u ugovor o radu, nemoj odgovarati na ovo pitanje. Prema Zakonu o radu, raspored radnog vremena nije obvezan sastojak ugovora o radu. Raspored radnog vremena može se, osim ugovorom o radu, utvrditi propisom, kolektivnim ugovorom, sporazumom sklopljenim između radničkog vijeća i poslodavca ili pravilnikom o radu, a ako nije utvrđen na jedan od tih načina, o rasporedu radnog vremena odlučuje poslodavac pisanom odlukom.') }}
                            <div id="full_time_working_hours"
                                 class="@if(isset($model->is_full_time) && $model->is_full_time == 0) d-none @endif">
                                {{ Form::fCheckbox($model, 'working_hours[]', 'mon_fri_8_16', 'Od ponedjeljka do petka, od 8 do 16 sati', ['class' => 'form-check-input working_hours']) }}
                                {{ Form::fCheckbox($model, 'working_hours[]', 'mon_fri_9_17', 'Od ponedjeljka do petka, od 9 do 17 sati', ['class' => 'form-check-input working_hours']) }}
                            </div>
                            <div class="row mt-3">
                                <div class="col-lg-6">
                                    {{ Form::fCheckbox($model, 'working_hours[]', 'custom', 'Od', ['class' => 'form-check-input working_hours', 'id' => 'working_hours_custom_radio', 'checked' => isset($model->is_full_time) && $model->is_full_time == 0]) }}
                                    {{ Form::fDropdown($model, "working_hours_custom_start_day", null, ['ponedjeljka' => 'Ponedjeljka', 'utorka' => 'Utorka', 'srijede' => 'Srijede', 'četvrtka' => 'Četvrtka', 'petka' => 'Petka', 'subote' => 'Subote', 'nedjelje' => 'Nedjelje'], '', ['placeholder' => 'Odaberi dan...', 'class' => 'custom_working_hours form-control']) }}
                                    <div class="mt-1"></div>
                                    {{ Form::fDropdown($model, "working_hours_custom_end_day", null, ['ponedjeljka' => 'Ponedjeljka', 'utorka' => 'Utorka', 'srijede' => 'Srijede', 'četvrtka' => 'Četvrtka', 'petka' => 'Petka', 'subote' => 'Subote', 'nedjelje' => 'Nedjelje'], 'Do', ['placeholder' => 'Odaberi dan...', 'class' => 'custom_working_hours form-control', 'zero-margin-label' => true]) }}
                                </div>
                                <div class="col-lg-6">
                                    {{ Form::fDropdown($model, "working_hours_custom_start_hour", null, range(0,23) , 'Od (h)', ['placeholder' => 'Odaberi sat...', 'class' => 'custom_working_hours form-control', 'zero-margin-label' => true]) }}
                                    <div class="mt-1"></div>
                                    {{ Form::fDropdown($model, "working_hours_custom_end_hour", null, range(0,23), 'Do (h)', ['placeholder' => 'Odaberi sat...', 'class' => 'custom_working_hours form-control', 'zero-margin-label' => true]) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'paid_holidays_duration', $question_index++.'. Upiši trajanje plaćenog godišnjeg odmora na koje će radnik imati pravo', 'Prema Zakonu o radu, radnik ima pravo na plaćeni godišnji odmor za svaku kalendarsku godinu u trajanju od najmanje 4 tjedna, uz izuzetak maloljetnih radnika i radnika koji rade na poslovima na kojima, uz primjenu mjera zaštite zdravlja i sigurnosti na radu, nije moguće zaštititi radnika od štetnih utjecaja, koji imaju pravo na godišnji odmor u trajanju od najmanje 5 tjedana. Postoji i izuzetak za radnike s utvrđenim invaliditetom koji imaju pravo na godišnji odmor u trajanju od najmanje 5 tjedana za svaku kalendarsku godinu. <br><br>U praksi, radnici koji rade 5 dana u tjednu imaju pravo na najmanje trajanje godišnjeg odmora od 20 radnih dana (4 tjedna x 5), a radnici koji rade 6 dana u tjednu imaju pravo na najmanje 24 radna dana (4 tjedna x 6). Radnici s invaliditetom koji rade 5 dana u tjednu imaju pravo na najmaje trajanje godišnjeg odmora od 25 radnih dana (5 tjedana x 5), a radnici koji rade 6 dana  tjednu imaju pravo na najmanje 30 radnih dana (5 tjedana x 6).') }}
                            {{ Form::fRadio($model, 'paid_holidays_duration', 'minimum', 'Želim da se primjenjuje godišnji odmor u najmanjem trajanju propisanom Zakonom o radu', ['class' => 'form-check-input paid_holidays_duration_non_custom_radio', 'checked' => (!isset($model->paid_holidays_duration) || $model->paid_holidays_duration !== 'minimum') && (!$model->document->getValue('has_work_regulations') && !$model->document->getValue('has_collective_agreement'))]) }}
                            @if($model->document->getValue('has_work_regulations'))
                                <div class="dynamic">
                                    {{ Form::fRadio($model, 'paid_holidays_duration', 'defined_in_work_regulations', 'Trajanje plaćenog godišnjeg odmora već je uređeno Pravilnikom o radu poslodavca pa želim da ugovor o radu upućuje na Pravilnik o radu', ['class' => 'form-check-input paid_holidays_duration_non_custom_radio',  'checked' => !isset($model->paid_holidays_duration) || ($model->paid_holidays_duration === 'defined_in_collective_agreement' && !$model->document->getValue('has_collective_agreement'))])}}
                                </div>
                            @endif
                            @if($model->document->getValue('has_collective_agreement'))
                                <div class="dynamic">
                                    {{ Form::fRadio($model, 'paid_holidays_duration', 'defined_in_collective_agreement', 'Trajanje plaćenog godišnjeg odmora već je uređeno Kolektivnim ugovorom koji se primjenjuje na ovaj radni odnos pa želim da ugovor o radu upućuje na Kolektivni ugovor', ['class' => 'form-check-input paid_holidays_duration_non_custom_radio',  'checked' => (!isset($model->paid_holidays_duration) && !$model->document->getValue('has_work_regulations')) || (isset($model->paid_holidays_duration) && $model->paid_holidays_duration === 'defined_in_work_regulations' && !$model->document->getValue('has_work_regulations'))])}}
                                </div>
                            @endif
                            <div class="form-check">
                                <label class="form-check-label radio-input">
                                    {{ Form::radio('paid_holidays_duration', 'custom', null, ['class' => 'form-check-input']) }}
                                    {{ Form::fText($model, 'paid_holidays_duration_custom', null, null, ['class' => 'form-control', 'data-force-start-case' => 'lower', 'placeholder' => 'Npr: 5 tjedana']) }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                @php $question_index = 1; @endphp
                <div class="card-header">
                    Plaća, dodaci i ostali primici
                </div>

                <div class="card-body">
                    @if($model->document->getValue('has_work_regulations') || $model->document->getValue('has_collective_agreement'))
                        <div class="row dynamic">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'compensation_management_type', "Na koji način želiš urediti pitanja vezana za plaću i druga materijalna prava radnika?") }}
                                @if($model->document->getValue('has_work_regulations'))
                                    {{ Form::fRadio($model, 'compensation_management_type', 'defined_in_work_regulations', 'Pitanja vezana za plaću i druga materijalna prava radnika već su uređena Pravilnikom o radu poslodavca pa želim da ugovor o radu upućuje na Pravilnik o radu', ['class' => 'form-check-input compensation_management_type_non_custom_radio',  'checked' => !isset($model->compensation_management_type) || ($model->compensation_management_type === 'defined_in_collective_agreement' && !$model->document->getValue('has_collective_agreement'))])}}
                                @endif
                                @if($model->document->getValue('has_collective_agreement'))
                                    {{ Form::fRadio($model, 'compensation_management_type', 'defined_in_collective_agreement', 'Pitanja vezana za plaću, dodatke i ostale primitke radnika već su uređena Kolektivnim ugovorom koji se primjenjuje na ovaj radni odnos pa želim da ugovor o radu upućuje na Kolektivni ugovor', ['class' => 'form-check-input compensation_management_type_non_custom_radio',  'checked' => (!isset($model->compensation_management_type) && !$model->document->getValue('has_work_regulations')) || (isset($model->compensation_management_type) && $model->compensation_management_type === 'defined_in_work_regulations' && !$model->document->getValue('has_work_regulations'))])}}
                                @endif
                                {{ Form::fRadio($model, 'compensation_management_type', 'custom', 'Želim upisati podatke o plaći i drugim materijalnim pravima u ugovor o radu', ['id' => 'compensation_management_type_custom_radio'])}}
                            </div>
                        </div>
                    @endif


                    <div id="custom_compensation_management_type_container"
                         style="
                         @if(($model->document->getValue('has_work_regulations') || $model->document->getValue('has_collective_agreement')) && (!isset($model->compensation_management_type) || $model->compensation_management_type != 'custom'))
                            display:none;
                        @endif
                    ">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'gross_monthly_salary_custom', null, ($model->document->getValue('has_work_regulations') ? '<span class="dot"></span>' : ($question_index++ .".")) . ' Koju osnovnu bruto mjesečnu plaću će primati radnik za obavljeni rad?', ['placeholder' => 'Npr: 1.200,00', 'data-currency' => 'EUR'], '€', 'Plaća, u smislu Zakona o radu, je primitak radnika koji poslodavac isplaćuje radniku za obavljeni rad u određenom mjesecu. Plaća se može sastojati od osnovne odnosno ugovorene plaće, dodataka i ostalih primitaka.<br><br>Dodaci, u smislu Zakona o radu, su novčani primici radnika koje radnik ostvaruje na temelju posebnog propisa, kolektivnog ugovora, pravilnika o radu ili ugovora o radu razmjerno odrađenim radnim satima pod određenim uvjetima (otežani uvjeti rada, prekovremeni rad, noćni rad, rad nedjeljom, rad blagdanom i slično) i koje ostvaruje neovisno o efektivnom radu (uvećanje za navršene godine radnoga staža i slično), odnosno koje u skladu s propisanim, utvrđenim ili ugovorenim kriterijima i visini ostvaruje ovisno o ostvarenim rezultatima poslovanja i radnoj uspješnosti (stimulacija i slično).<br><br>Ostali primici radnika, u smislu Zakona o radu, su primici radnika koje poslodavac radniku isplaćuje u novcu ili naravi na temelju kolektivnog ugovora, pravilnika o radu, akta poslodavca ili ugovora o radu.<br><br>Pod plaćom se podrazumijeva plaća u bruto iznosu koji se sastoji od iznosa za isplatu radniku i javnih davanja iz plaće u skladu s posebnim propisima (tzv. bruto 1). Ukupan trošak plaće, u smislu Zakona o radu, je trošak plaće u bruto iznosu uvećan za trošak javnih davanja na plaću u skladu s propisima o porezima i doprinosima (tzv. bruto 2).') }}
                            </div>
                        </div>
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'salary_bonuses', ($model->document->getValue('has_work_regulations') || $model->document->getValue('has_collective_agreement') ? '<span class="dot"></span>' : $question_index++.".")  . ' Upiši postotke povećanja osnovne plaće (dodatke na plaću) na koje radnik po Zakonu o radu ima pravo, a ako želiš dodaj i druge dodatke na plaću i upiši njihove postotke odnosno iznose:', 'Za otežane uvjete rada, prekovremeni i noćni rad te za rad nedjeljom, blagdanom i neradnim danom utvrđenim posebnim zakonom radnik ima pravo na povećanu plaću, u visini i na način određenima kolektivnim ugovorom, pravilnikom o radu ili ugovorom o radu, pri čemu povećanje za svaki sat rada nedjeljom ne može biti manje od 50%. Za sve druge slučajeve (npr. za rad subotom i slično), Zakonom o radu nije uređena isplata povećane plaće te ona radniku nije zajamčena, ali se može urediti kolektivnim ugovorom, pravilnikom o radu, ugovorom o radu ili odlukom poslodavca. Da bi se neki dodatak smatrao dijelom plaće u smislu Zakona o radu mora biti ugovoreno odnosno utvrđeno da se isplaćuje za obavljeni rad u određenom mjesecu. <br><br>Otežani uvjeti rada, u smislu Zakona o radu, su uvjeti rada za koje su procjenom rizika na radu kod poslodavca utvrđene opasnosti, štetnosti i napori koji bi mogli izazvati štetne posljedice za sigurnost i zdravlje radnika.') }}

                                {{ Form::fNumber($model, 'salary_bonuses[overtime]', null, 'za rad prekovremeno', ['min' => 1, 'placeholder' => 'Npr: 5', 'zero-margin-label' => true], '%')  }}
                                <div class="mb-2"></div>

                                {{ Form::fNumber($model, 'salary_bonuses[night_shift]', null, 'za rad noću', ['min' => 1, 'placeholder' => 'Npr: 10', 'zero-margin-label' => true], '%')  }}
                                <div class="mb-2"></div>

                                {{ Form::fNumber($model, 'salary_bonuses[hard_conditions]', null, 'za rad u otežanim uvjetima rada', ['min' => 1, 'placeholder' => 'Npr: 15', 'zero-margin-label' => true], '%')  }}
                                <div class="mb-2"></div>

                                {{ Form::fNumber($model, 'salary_bonuses[sunday]', null, 'za rad nedjeljom', ['id' => 'sunday_salary_bonus', 'min' => 50, 'placeholder' => 'Npr: 50', 'zero-margin-label' => true], '%')  }}
                                <div class="mb-2"></div>

                                {{ Form::fNumber($model, 'salary_bonuses[holidays]', null, 'za rad blagdanom ili nekim drugim danom za koji je zakonom određeno da se ne radi', ['min' => 1, 'placeholder' => 'Npr: 20', 'zero-margin-label' => true], '%')  }}
                                <div class="mb-4"></div>

                                <div class="dynamic" id="custom_salary_bonuses_container">
                                    <div id="custom_salary_bonuses">

                                        @if(!empty($model->custom_salary_bonuses))
                                            @foreach($model->custom_salary_bonuses as $i => $_custom_salary_bonus)
                                                <div class="custom_salary_bonus_content">
                                                    <hr/>
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <strong><a class="btn btn-danger btn-sm float-right remove_custom_salary_bonus"><i
                                                                            class="fa fa-trash"></i> Ukloni</a></strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            {{ Form::fText($model, "custom_salary_bonuses[$i][name]", null, 'Naziv ili opis dodatka na plaću', ['placeholder' => 'Npr: za rad subotom']) }}
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            {{ Form::fDropdownText($model ,'Iznos dodatka na plaću', "custom_salary_bonuses[$i][value]", null, "custom_salary_bonuses[$i][type]", null, ["percentage" => "%", "eur" => "€"], ['placeholder' => 'Npr: 5', 'data-currency' => 'eur']) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>

                                    <div id="custom_salary_bonus_template" class="d-none">
                                        <div class="custom_salary_bonus_content">
                                            <hr/>
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <strong><a class="btn btn-danger btn-sm float-right remove_custom_salary_bonus"><i
                                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "custom_salary_bonuses[{INDEX}][name]", null, 'Naziv ili opis dodatka na plaću', ['placeholder' => 'Npr: za rad subotom']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fDropdownText($model ,'Iznos dodatka na plaću', 'custom_salary_bonuses[{INDEX}][value]', null, 'custom_salary_bonuses[{INDEX}][type]', null, ["percentage" => "%", "eur" => "€"], ['placeholder' => 'Npr: 5', 'data-currency' => 'eur']) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <a class="btn btn-info" id="add_custom_salary_bonus">+ Dodaj dodatak na plaću</a>

                                </div>

                            </div>
                        </div>

                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'salary_perks', ($model->document->getValue('has_work_regulations') || $model->document->getValue('has_collective_agreement') ? '<span class="dot"></span>' : $question_index++.".")  . ' Označi ostale primitke koji će se mjesečno isplaćivati radniku u novcu ili naravi za obavljeni rad kao dio plaće u smislu Zakona o radu: <small>(opcionalno)</small>', 'Prema Zakonu o radu, ostali primici radnika od kojih se, osim osnovne odnosno ugovorene plaće i dodataka, može sastojati plaća su primici radnika koje poslodavac radniku isplaćuje u novcu ili naravi na temelju kolektivnog ugovora, pravilnika o radu, akta poslodavca ili ugovora o radu. Da bi se neki primitak smatrao dijelom plaće u smislu Zakona o radu mora biti ugovoreno odnosno utvrđeno da se isplaćuje za obavljeni rad u određenom mjesecu. ') }}
                                <div class="mt-1"></div>
                                {{ Form::fCheckbox($model, 'salary_perks[]', 'car', 'Primitak u naravi po osnovi korištenja službenog automobila u privatne svrhe ', ['id' => 'salary_perk_car'] ) }}

                                <div id="salary_perk_car_container" class="mt-3" style="@if(!isset($model->salary_perks) || !in_array('car', $model->salary_perks)) display:none; @endif">
                                    <div class="row">
                                        <div class="form-group  col-lg-12">
                                            {{ Form::fDropdown($model, 'salary_perk_car_amount', null, ['1 % od nabavne vrijednosti vozila (uvećano za PDV)' => '1% od nabavne vrijednosti vozila (uvećano za PDV)', '20% od mjesečne rate za operativni leasing ili dugotrajni najam (uvećano za PDV)' => '20% od mjesečne rate za operativni leasing ili dugotrajni najam (uvećano za PDV)', '0,40 centi po prijeđenom km x broj km prijeđenih u privatne svrhe' => '0,40 centi po prijeđenom km x broj km prijeđenih u privatne svrhe'], 'Vrijednost primitka u naravi')  }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, 'salary_perk_car_licence_plates', null, 'Registarska oznaka automobila', ['placeholder' => 'Npr: ZG 543-AA'])  }}
                                        </div>
                                        <div class="form-group  col-lg-6">
                                            {{ Form::fText($model, 'salary_perk_car_model', null, 'Marka automobila', ['placeholder' => 'Npr: Peugeot'])  }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group  col-lg-6">
                                            {{ Form::fText($model, 'salary_perk_car_identification_number', null, 'Broj šasije automobila ', ['placeholder' => 'Npr: VF11C9HP0CS011111'])  }}
                                        </div>
                                    </div>
                                </div>

                                <div id="custom_salary_perks_container" class="dynamic">
                                    <div id="custom_salary_perks">
                                        @if(!empty($model->custom_salary_perks))
                                            @foreach($model->custom_salary_perks as $i => $_custom_salary_perk)
                                                <div class="custom_salary_perk_content">
                                                    <hr/>
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <strong><a class="btn btn-danger btn-sm float-right remove_custom_salary_perk"><i
                                                                            class="fa fa-trash"></i> Ukloni</a></strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            {{ Form::fText($model, "custom_salary_perks[$i][name]", null, 'Naziv ili opis primitka koji će se isplaćivati kao dio plaće', ['placeholder' => 'Unesi naziv ili opis primitka...']) }}
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            {{ Form::fDropdownText($model ,'Bruto iznos primitka koji će se mjesečno isplaćivati kao dio plaće', "custom_salary_perks[$i][value]", null, "custom_salary_perks[$i][type]", null, ["eur" => "€", "description" => "opis"], ['placeholder' => 'Npr: 150,00', 'data-currency' => 'eur']) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>

                                    <div id="custom_salary_perk_template" class="d-none">
                                        <div class="custom_salary_perk_content">
                                            <hr/>
                                            <div class="row mb-2">
                                                <div class="col-lg-12">
                                                    <strong><a class="btn btn-danger btn-sm float-right remove_custom_salary_perk"><i
                                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group  col-lg-12">
                                                    {{ Form::fText($model, 'custom_salary_perks[{INDEX}][name]', null, 'Naziv ili opis primitka koji će se isplaćivati kao dio plaće', ['placeholder' => 'Unesi naziv ili opis primitka...'])  }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group  col-lg-12">
                                                    {{ Form::fDropdownText($model ,'Bruto iznos primitka koji će se mjesečno isplaćivati kao dio plaće', 'custom_salary_perks[{INDEX}][value]', null, 'custom_salary_perks[{INDEX}][type]', null, ["eur" => "€", "description" => "opis"], ['placeholder' => 'Npr: 150,00', 'data-currency' => 'eur']) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <a class="btn btn-info mt-3" id="add_custom_salary_perk">+ Dodaj primitak kao dio plaće</a>
                                </div>

                            </div>
                        </div>

                        <hr/>

                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'material_perks', ($model->document->getValue('has_work_regulations') || $model->document->getValue('has_collective_agreement')  ? '<span class="dot"></span>' : $question_index++.".")  . ' Označi primitke radnika na temelju radnog odnosa koji će se isplaćivati radniku u novcu ili naravi, a ne smatraju se plaćom u smislu Zakona o radu: <small>(opcionalno)</small>', 'Prema Zakonu o radu, primici koje radnik može ostvariti na temelju radnog odnosa su: 1. primici koje poslodavac, u skladu s propisom, kolektivnim ugovorom, pravilnikom o radu, aktom poslodavca ili ugovorom o radu isplaćuje radniku kao materijalno pravo iz radnog odnosa (jubilarna nagrada, regres, božićnica i slično) i 2. primici koje poslodavac, u skladu s propisom, kolektivnim ugovorom, pravilnikom o radu, aktom poslodavca ili ugovorom o radu isplaćuje radniku, a koji predstavljaju naknadu troška. Takvi primici ne smatraju se plaćom u smislu Zakona o radu.') }}

                                {{ Form::fCheckbox($model, 'material_perks[]', 'christmas', 'božićnica (godišnji bruto iznos)', ['id' => 'material_perk_christmas_cb'] ) }}
                                {{ Form::fText($model, 'material_perk_christmas', null, '', ['placeholder' => 'Npr: 100,00', 'id' => 'material_perk_christmas_value', 'data-currency' => 'EUR'], '€')  }}
                                <div class="mb-2"></div>

                                {{ Form::fCheckbox($model, 'material_perks[]', 'easter', 'uskrsnica (godišnji bruto iznos)', ['id' => 'material_perk_easter_cb']) }}
                                {{  Form::fText($model, 'material_perk_easter', null, '', ['placeholder' => 'Npr: 100,00', 'id' => 'material_perk_easter_value', 'data-currency' => 'EUR'], '€')  }}
                                <div class="mb-2"></div>

                                {{ Form::fCheckbox($model, 'material_perks[]', 'child_gift', 'dar za djecu (po djetetu do 15 godina starosti, godišnji bruto iznos)', ['id' => 'material_perk_child_gift_cb']) }}
                                {{  Form::fText($model, 'material_perk_child_gift', null, '', ['placeholder' => 'Npr: 100,00', 'id' => 'material_perk_child_gift_value', 'data-currency' => 'EUR'], '€')  }}
                                <div class="mb-2"></div>

                                {{ Form::fCheckbox($model, 'material_perks[]', 'vacation_subvention', 'naknada za godišnji odmor (regres) (godišnji bruto iznos)', ['id' => 'material_perk_vacation_subvention_cb']) }}
                                {{  Form::fText($model, 'material_perk_vacation_subvention', null, '', ['placeholder' => 'Npr: 100,00', 'id' => 'material_perk_vacation_subvention_value', 'data-currency' => 'EUR'], '€')  }}
                                <div class="mb-2"></div>

                                {{ Form::fCheckbox($model, 'material_perks[]', 'meal_subvention', 'novčana paušalna naknada za podmirivanje troškova prehrane radnika (mjesečni bruto iznos)', ['id' => 'material_perk_meal_subvention_cb']) }}
                                {{  Form::fText($model, 'material_perk_meal_subvention', null, '', ['placeholder' => 'Npr: 100,00', 'id' => 'material_perk_meal_subvention_value', 'data-currency' => 'EUR'], '€')  }}
                                <div class="mb-2"></div>

                                {{ Form::fCheckbox($model, 'material_perks[]', 'transport_subvention', 'naknada troškova prijevoza na posao i s posla mjesnim javnim prijevozom (mjesečni bruto iznos)', ['id' => 'material_perk_transport_subvention_cb']) }}
                                {{ Form::fDropdownText($model, null, 'material_perk_transport_subvention[value]', null, 'material_perk_transport_subvention[type]' , null, ["eur" => "€", "description" => "opis"], ['id' => 'material_perk_transport_subvention_value', 'placeholder' => 'Npr: 150,00', 'data-currency' => 'eur'])  }}
                                <div class="mb-2"></div>

                                {{ Form::fCheckbox($model, 'material_perks[]', 'transport_subvention_intercity', 'naknada troškova prijevoza na posao i s posla međumjesnim javnim prijevozom (mjesečni bruto iznos)', ['id' => 'material_perk_transport_subvention_intercity_cb']) }}
                                {{  Form::fDropdownText($model, null, 'material_perk_transport_subvention_intercity[value]', null, 'material_perk_transport_subvention_intercity[type]' , null, ["eur" => "€", "description" => "opis"], ['id' => 'material_perk_transport_subvention_intercity_value', 'placeholder' => 'Npr: 150,00', 'data-currency' => 'eur'])  }}
                                <div class="mb-4"></div>

                                <div class="dynamic" id="custom_material_perks_container">
                                    <div id="custom_material_perks">
                                        @if(!empty($model->custom_material_perks))
                                            @foreach($model->custom_material_perks as $i => $_custom_material_perk)
                                                <div class="custom_material_perk_content">
                                                    <hr/>
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <strong><a class="btn btn-danger btn-sm float-right remove_custom_material_perk"><i
                                                                            class="fa fa-trash"></i> Ukloni</a></strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            {{ Form::fText($model, "custom_material_perks[$i][name]", null, 'Naziv ili opis primitka na temelju radnog odnosa', ['placeholder' => 'Unesi naziv ili opis primitka...']) }}
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            {{  Form::fDropdownText($model, 'Bruto iznos primitka na temelju radnog odnosa', "custom_material_perks[$i][value]", null, "custom_material_perks[$i][type]" , null, ["eur" => "€", "description" => "opis"], ['placeholder' => 'Npr: 150,00', 'data-currency' => 'eur'])  }}
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            {{ Form::fDropdown($model, "custom_material_perks[$i][frequency]", null, ['jednokratna isplata' => 'Jednokratna isplata', 'mjesečna isplata' => 'Mjesečna isplata', 'godišnja isplata' => 'Godišnja isplata'], 'Učestalost plaćanja') }}
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>

                                    <div id="custom_material_perk_template" class="d-none">
                                        <div class="custom_material_perk_content">
                                            <hr/>
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <strong><a class="btn btn-danger btn-sm float-right remove_custom_material_perk"><i
                                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fText($model, "custom_material_perks[{INDEX}][name]", null, 'Naziv ili opis primitka na temelju radnog odnosa', ['placeholder' => 'Unesi naziv ili opis primitka...']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{  Form::fDropdownText($model, 'Bruto iznos primitka na temelju radnog odnosa', 'custom_material_perks[{INDEX}][value]', null, 'custom_material_perks[{INDEX}][type]' , null, ["eur" => "€", "description" => "opis"], ['placeholder' => 'Npr: 150,00', 'data-currency' => 'eur'])  }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fDropdown($model, "custom_material_perks[{INDEX}][frequency]", null, ['jednokratna isplata' => 'Jednokratna isplata', 'mjesečna isplata' => 'Mjesečna isplata', 'godišnja isplata' => 'Godišnja isplata'], 'Učestalost plaćanja') }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <a class="btn btn-info" id="add_custom_material_perk">+ Dodaj primitak na temelju radnog odnosa</a>

                                </div>

                            </div>
                        </div>

                        <hr/>

                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'salary_day', ($model->document->getValue('has_work_regulations') ? '<span class="dot"></span>' : $question_index++.".") . ' Kojeg dana plaća za prethodni mjesec dospijeva na plaćanje?', 'Prema članku 92. stavku (4) Zakona o radu, plaća, naknada plaće i ostali primici isplaćuju se u rokovima određenim kolektivnim ugovorom ili ugovorom o radu, a najkasnije petnaestog dana tekućeg mjeseca za prethodni mjesec. Prema stavku (5) istog članka Zakona o radu, ako je za obavljanje rada ugovoreno ili utvrđeno ostvarivanje prava radnika na primitak u naravi, poslodavac ga je dužan radniku omogućiti do kraja tekućeg mjeseca za koji ostvaruje to pravo.') }}

                                {{ Form::fRadio($model, 'salary_day', 'by_15th_day', 'Najkasnije do 15. dana u mjesecu za prethodni mjesec', ['checked' => !isset($model->work_location)]) }}
                                <div class="form-check">
                                    <label class="form-check-label radio-input">
                                        {{ Form::radio('salary_day', 'custom', null, ['class' => 'form-check-input', 'id' => 'salary_day_custom_cb']) }}
                                        {{ Form::number('salary_day_custom', null, ['class' => 'form-control', 'id' => 'salary_day_custom', 'placeholder' => 'Npr: 10', 'max' => 15]) }}
                                    </label>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="card mb-4">
                @php $question_index = 1; @endphp
                <div class="card-header">
                    Otkaz i otkazni rokovi
                </div>

                <div class="card-body">

                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'notice_management_type',  $question_index++ ."."  . ' Na koji način želiš urediti pitanja vezana za postupak u slučaju otkazivanja ugovora o radu i otkazne rokove koji će se primjenjivati u slučaju otkaza?') }}
                            @if($model->document->getValue('has_work_regulations'))
                                <div class="dynamic">
                                    {{ Form::fRadio($model, 'notice_management_type', 'defined_in_work_regulations', 'Postupak otkazivanja i otkazni rokovi već su uređeni Pravilnikom o radu poslodavca pa želim da ugovor o radu upućuje na Pravilnik o radu', ['class' => 'form-check-input notice_management_type_non_custom_radio',  'checked' => !isset($model->notice_management_type) || ($model->notice_management_type === 'defined_in_collective_agreement' && !$model->document->getValue('has_collective_agreement'))])}}
                                </div>
                            @endif
                            @if($model->document->getValue('has_collective_agreement'))
                                <div class="dynamic">
                                    {{ Form::fRadio($model, 'notice_management_type', 'defined_in_collective_agreement', 'Postupak otkazivanja i otkazni rokovi već su uređeni Kolektivnim ugovorom koji se primjenjuje na ovaj radni odnos pa želim da ugovor o radu upućuje na Kolektivni ugovor', ['class' => 'form-check-input notice_management_type_non_custom_radio',  'checked' => (!isset($model->notice_management_type) && !$model->document->getValue('has_work_regulations')) || (isset($model->notice_management_type) && $model->notice_management_type === 'defined_in_work_regulations' && !$model->document->getValue('has_work_regulations'))])}}
                                </div>
                            @endif
                            {{ Form::fRadio($model, 'notice_management_type', 'defined_by_law', 'Želim da se primjenjuju otkazni rokovi u najmanjem trajanju propisanom Zakonom o radu', ['class' => 'form-check-input notice_management_type_non_custom_radio', 'checked' => (!isset($model->notice_management_type) || in_array($model->notice_management_type, ['defined_in_collective_agreement', 'defined_in_work_regulations']) ) && (!$model->document->getValue('has_work_regulations') && !$model->document->getValue('has_collective_agreement'))]) }}

                            {{ Form::fRadio($model, 'notice_management_type', 'custom', 'Želim upisati trajanje otkaznih rokova u ugovor o radu', ['id' => 'notice_management_type_custom_radio'])}}
                        </div>
                    </div>

                    <div class="dynamic" id="custom_notice_management_type_container"
                         style="{{ (isset($model->notice_management_type) && $model->notice_management_type != "custom") || (!isset($model->notice_management_type)) ? "display:none" : null }}">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'custom_notice_period_1', null, '<span class="dot"></span> Koji otkazni rok će se primjenjivati u slučaju da radnik otkaže ugovor o radu poslodavcu (redoviti otkaz)?', ['placeholder' => 'Npr: 1 mjesec', 'data-force-start-case' => 'lower'], null, 'Prema članku 122. stavku (7) Zakona o radu, ako radnik otkazuje ugovor o radu, otkazni rok ne može biti duži od mjesec dana, ako on za to ima osobito važan razlog.') }}
                            </div>
                        </div>
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'custom_notice_period_2', null, '<span class="dot"></span> Koji otkazni rok će se primjenjivati ako poslodavac otkaže ugovor o radu radniku u slučaju prestanka potrebe za obavljanjem određenog posla zbog gospodarskih, tehnoloških ili organizacijskih razloga (poslovno uvjetovani otkaz)?', ['placeholder' => 'Npr: 3 mjeseca', 'data-force-start-case' => 'lower'], null, 'Prema članku 122. stavku (1) i (2) Zakona o radu u slučaju poslovno uvjetovanog otkaza otkazni rok je najmanje:<br><br>
                                    <ul>
                                    <li>dva tjedna, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno manje od jedne godine</li>
                                    <li>mjesec dana, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno jednu godinu</li>
                                    <li>mjesec dana i dva tjedna, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno dvije godine</li>
                                    <li>dva mjeseca, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno pet godina</li>
                                    <li>dva mjeseca i dva tjedna, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno deset godina</li>
                                    <li>tri mjeseca, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno dvadeset godina.</li>
                                    </ul>
                                    Otkazni rok radniku koji je kod poslodavca proveo u radnom odnosu neprekidno dvadeset godina, povećava se za dva tjedna ako je radnik navršio pedeset godina života, a za mjesec dana ako je navršio pedeset pet godina života.') }}
                            </div>
                        </div>
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'custom_notice_period_3', null, '<span class="dot"></span> Koji otkazni rok će se primjenjivati ako poslodavac otkaže ugovor o radu radniku u slučaju da radnik nije u mogućnosti uredno izvršavati svoje obveze iz radnog odnosa zbog određenih trajnih osobina ili sposobnosti (osobno uvjetovani otkaz)?', ['placeholder' => 'Npr: 3 mjeseca', 'data-force-start-case' => 'lower'], null, 'Prema članku 122. (1) i (2) Zakona o radu u slučaju osobno uvjetovanog otkaza otkazni rok je najmanje:<br><br>
                                    <ul>
                                    <li>dva tjedna, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno manje od jedne godine</li>
                                    <li>mjesec dana, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno jednu godinu</li>
                                    <li>mjesec dana i dva tjedna, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno dvije godine</li>
                                    <li>dva mjeseca, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno pet godina</li>
                                    <li>dva mjeseca i dva tjedna, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno deset godina</li>
                                    <li>tri mjeseca, ako je radnik u radnom odnosu kod istog poslodavca proveo neprekidno dvadeset godina.</li>
                                    </ul>
                                    Otkazni rok radniku koji je kod poslodavca proveo u radnom odnosu neprekidno dvadeset godina, povećava se za dva tjedna ako je radnik navršio pedeset godina života, a za mjesec dana ako je navršio pedeset pet godina života.') }}
                            </div>
                        </div>
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'custom_notice_period_4', null, '<span class="dot"></span> Koji otkazni rok će se primjenjivati ako poslodavac otkaže ugovor o radu radniku u slučaju da radnik krši obveze iz radnog odnosa (otkaz uvjetovan skrivljenim ponašanjem radnika)?', ['placeholder' => 'Npr: 1 mjesec', 'data-force-start-case' => 'lower'], null, 'Prema članku 122. stavku (3) Zakona o radu, radniku kojem se ugovor o radu otkazuje zbog povrede obveze iz radnog odnosa (otkaz uvjetovan skrivljenim ponašanjem radnika) utvrđuje se otkazni rok u dužini polovice najmanjih otkaznih rokova propisanih člankom 122. stavkom (1) i (2) u slučaju poslovno ili osobno uvjetovanog otkaza.') }}
                            </div>
                        </div>
                        @if($model->document->getValue('probation_period'))
                            <hr/>
                            <div class="row dynamic">
                                <div class="form-group col-lg-12">
                                    {{ Form::fText($model, 'custom_notice_period_5', null, '<span class="dot"></span> Koji otkazni rok će se primjenjivati u slučaju da poslodavac otkaže ugovor o radu radniku za vrijeme trajanja probnog rada (otkaz zbog nezadovoljavanja na probnom radu) ili ako radnik otkaže ugovor o radu za vrijeme trajanja probnog rada?', ['placeholder' => 'Npr: 15 dana', 'data-force-start-case' => 'lower'], null, 'Prema članku 53. stavku (10) Zakona o radu, otkazni rok kod ugovorenog probnog rada je najmanje jedan tjedan.') }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

        </div>

    </div>

    {{ Form::close() }}
@endsection
