@push('scripts')
    <script type="text/javascript">
        $(function() {

            let shouldShowTutorial = !isMobile();

            // document translations DataTable
            $('#translations-table').DataTable({
                pageLength: 4,
                searchDelay: 500,
                pagingType: isMobile() ? "simple" : "simple_numbers",
                language: {
                    url: "{{ env('APP_URL') }}/vendor/datatables/translations-croatian.json"
                },
                fixedHeader: true,
                serverSide: true,
                responsive: true,
                ajax: '{!! route('user.translations') !!}',
                columns: [
                    {data: 'id', name: 'id', class: 'never'},
                    {data: 'title', name: 'title'},
                    {data: 'status', name: 'status'},
                    {data: 'created_at', name: 'translations.created_at'},
                    {
                        data: 'updated_at', name: 'translations.updated_at', render: function(dateTime){
                            let date =  new Date(Date.parse(dateTime));

                            return date.toLocaleDateString("hr-HR", {
                                year: "numeric",
                                month: "2-digit",
                                day: "2-digit",
                            }).replace(/\s/g, '');
                        }
                    },
                    {
                        data: 'links', name: 'links', sortable: false, render: function(links, type, row) {
                            let statusId = row['status_id'];
                            let hasManyDocuments = row['has_many_documents'];

                            let dropdownItems = "";

                            if (statusId === 1) {
                                dropdownItems += "<a class='dropdown-item' href='" + links['checkout']['url'] + "'>Naruči prijevod</a>\n";
                                dropdownItems += "<a class='dropdown-item' href='" + links['downloadInput']['url'] + "'>" + (hasManyDocuments ? "Preuzmi dokumente" : "Preuzmi dokument") + "</a>\n";
                                dropdownItems += "<a data-message='Jeste li sigurni da želite ukloniti zahtjev za prijevod dokumenta iz košarice?' class='dropdown-item please-confirm' href='" + links['cancel']['url'] + "'>Ukloni iz košarice</a>\n";
                            } else if (statusId === 2) {
                                dropdownItems += "<a class='dropdown-item' href='" + links['details']['url'] + "'>Pregledaj narudžbu</a>\n";
                                dropdownItems += "<a class='dropdown-item' href='" + links['downloadInput']['url'] + "'>" + (hasManyDocuments ? "Preuzmi dokumente" : "Preuzmi dokument") + "</a>\n";
                                dropdownItems += "<a data-message='Jeste li sigurni da želite otkazati narudžbu?' class='dropdown-item please-confirm' href='" + links['cancel']['url'] + "'>Otkaži narudžbu</a>\n";
                            } else if (statusId === 3) {
                                dropdownItems += "<a class='dropdown-item' href='" + links['details']['url'] + "'>Pregledaj narudžbu</a>\n";
                                dropdownItems += "<a class='dropdown-item' href='" + links['downloadInput']['url'] + "'>" + (hasManyDocuments ? "Preuzmi dokumente" : "Preuzmi dokument") + "</a>\n";
                                dropdownItems += "<a data-message='Jeste li sigurni da želite otkazati narudžbu?' class='dropdown-item please-confirm' href='" + links['cancel']['url'] + "'>Otkaži narudžbu</a>\n";
                            } else if (statusId === 4) {
                                dropdownItems += "<a class='dropdown-item' href='" + links['details']['url'] + "'>Pregledaj narudžbu</a>\n";
                                dropdownItems += "<a class='dropdown-item' href='" + links['downloadInput']['url'] + "'>" + (hasManyDocuments ? "Preuzmi dokumente" : "Preuzmi dokument") + "</a>\n";
                                dropdownItems += "<a data-message='Jeste li sigurni da želite otkazati narudžbu?' class='dropdown-item please-confirm' href='" + links['cancel']['url'] + "'>Otkaži narudžbu</a>\n";
                            } else if (statusId === 5 || statusId === 6 || statusId === 7 || statusId === 8) {
                                dropdownItems += "<a class='dropdown-item' href='" + links['details']['url'] + "'>Pregledaj narudžbu</a>\n";
                                dropdownItems += "<a class='dropdown-item' href='" + links['downloadInput']['url'] + "'>" + (hasManyDocuments ? "Preuzmi dokumente" : "Preuzmi dokument") + "</a>\n";

                                if (links['downloadOutput']) {
                                    dropdownItems += "<a class='dropdown-item' href='" + links['downloadOutput']['url'] + "'>Preuzmi prijevod</a>\n";
                                }

                                if (links['send']) {
                                    dropdownItems += "<a class='dropdown-item' href='" + links['send']['url'] + "'>Pošalji prijevod</a>\n";
                                }

                            } else if (statusId === 9 || statusId === 10 || statusId === 0) {
                                dropdownItems += "<a class='dropdown-item' href='" + links['details']['url'] + "'>Pregledaj narudžbu</a>\n";
                            }

                            return "<div class='translationDropdown dropdown text-center text-small-left'>\n" +
                                "  <button class='btn btn-info dropdown-toggle' type='button' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>\n" +
                                "    Odaberi\n" +
                                "  </button>\n" +
                                "  <div class='dropdown-menu'>\n" +
                                dropdownItems +
                                "  </div>\n" +
                                "</div>";
                        }
                    }

                ],
                bLengthChange: false,
                bInfo: false,
                bAutoWidth: false,
                columnDefs: [
                    {
                        targets: [2,5],
                        className: 'min-tablet',
                    },
                    {
                        targets: [3,4],
                        className: 'min-desktop',
                    },
                    {
                        targets: [0],
                        visible: false,
                    },
                    {
                        targets: [0,2,3,4,5],
                        searchable: false
                    },
                    {
                        targets: [0,1,2],
                        orderable: false
                    },
                    {
                        targets: [5],
                        width: "110px"
                    },
                ],
                order: [[4, 'desc']],
                drawCallback: (settings) => {

                    // hide pagination if only 1 page
                    let api = new $.fn.dataTable.Api(settings);
                    let pageInfo = api.page.info();

                    if (pageInfo.pages <= 1) {
                        $('.dataTables_paginate', api.table().container()).hide();
                    } else {
                        $('.dataTables_paginate', api.table().container()).show();
                    }

                    @if(!\Illuminate\Support\Facades\Auth::user()->options || !\Illuminate\Support\Facades\Auth::user()->options->is_translation_tutorial_shown)
                    if(shouldShowTutorial && $('#translations-table').is(':visible') &&  $('.translationDropdown').first().length) {

                        $('#overlay').show();

                        $('.translationDropdown').first().css('z-index', 3).find('.dropdown-toggle').popover({
                            placement : 'left',
                            html : true,
                            title : 'Prijevod dokumenta <a href="#" class="close hideTutorial" data-dismiss="alert">&times;</a>',
                            content : "Klikni ovdje ako želiš pregledati sve mogućnosti vezane uz prijevod dokumenta."
                        }).popover('show').on('shown.bs.popover', function () {

                            let context = $(this);

                            $('.hideTutorial').on('click', function(){
                                $('#overlay').hide();
                                context.popover('dispose');
                            })

                            shouldShowTutorial = false;

                            $.ajax({
                                url: '/user/translation/tutorial',
                                type: 'POST',
                                data: {
                                    _token: $("[name='_token']").val(),
                                }
                            });

                        }).on('click', function(e){
                            $(this).popover('dispose');
                            $('#overlay').hide();
                        });
                    }
                    @endif

                    // when dropdown open, attach popovers to options
                    $('.translationDropdown').each(function(index){
                        $(this).on('show.bs.dropdown', function () {

                            $(this).find('.dropdown-item').each(function(index){

                                let tooltip = "";

                                switch ($(this).text()) {
                                    case 'Preuzmi dokument':
                                        tooltip = "Odaberi ovu opciju ako želiš preuzeti izvorni dokument.";
                                        break;
                                    case 'Preuzmi dokumente':
                                        tooltip = "Odaberi ovu opciju ako želiš preuzeti izvorne dokumente.";
                                        break;
                                    case 'Naruči prijevod':
                                        tooltip = "Odaberi ovu opciju ako želiš naručiti uslugu prijevoda ovog dokumenta.";
                                        break;
                                    case 'Pregledaj narudžbu':
                                        tooltip = "Odaberi ovu opciju ako želiš pregledati detalje vezane uz narudžbu prijevoda.";
                                        break;
                                    case 'Ukloni zahtjev':
                                        tooltip = "Odaberi ovu opciju ako želiš ukloniti zahtjev za prijevod ovog dokumenta.";
                                        break;
                                    case 'Ukloni iz košarice':
                                        tooltip = "Odaberi ovu opciju ako želiš ukloniti zahtjev za prijevod dokumenta iz košarice.";
                                        break;
                                    case 'Otkaži narudžbu':
                                        tooltip = "Odaberi ovu opciju ako želiš otkazati narudžbu i ukloniti zahtjev za prijevod dokumenta.";
                                        break;
                                    case 'Preuzmi prijevod':
                                        tooltip = "Odaberi ovu opciju ako želiš preuzeti prijevod u PDF formatu.";
                                        break;
                                    case 'Pošalji prijevod':
                                        tooltip = "Odaberi ovu opciju ako želiš poslati prijevod ovog dokumenta u PDF formatu na jednu ili više adresa e-pošte.";
                                        break;
                                }

                                $(this).popover({
                                    animation: false,
                                    html : true,
                                    title: $(this).text(),
                                    content: tooltip,
                                    trigger: 'hover'

                                });

                            });

                        });
                    })
                }

            });
        });

        function isMobile() {
            return $(window).width() < 480;
        }

    </script>
@endpush
