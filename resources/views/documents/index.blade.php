@extends('layouts.common.master')
@section('title', 'Moji dokumenti')

@section('content')
    <h1 class="mb-3">Moji dokumenti</h1>

    <div class="row mb-4 mt-2 mb-lg-0 mt-lg-0">
        <div class="col-lg-12">
            <div class="btn-group" role="group">
                <button id="wizard-toggle" type="button" class="btn @if(request()->hasCookie('documentsTab') && request()->cookie('documentsTab') !== 'wizard') btn-default @else btn-info @endif">
                    Obrasci
                    @if($is_desktop)
                        <span style="font-size: .9em;" data-placement="right"  data-toggle="tooltip" data-original-title="Klikni ovdje ako u tablici ispod želiš prikazati dokumente izrađene pomoću obrazaca.">
                            <i class="fa fa-info-circle"></i>
                        </span>
                    @endif
                </button>
                <button id="editor-toggle" type="button" class="btn @if(request()->cookie('documentsTab') === 'editor') btn-info @else btn-default @endif">
                    Uređivač
                    @if($is_desktop)
                        <span style="font-size: .9em;" data-placement="right" data-toggle="tooltip" data-original-title="Klikni ovdje ako u tablici ispod želiš prikazati dokumente izrađene pomoću uređivača.">
                            <i class="fa fa-info-circle"></i>
                        </span>
                    @endif
                </button>
                <button id="translations-toggle" type="button" class="btn @if(request()->cookie('documentsTab') === 'translations') btn-info @else btn-default @endif">
                    Prijevodi
                    @if($is_desktop)
                        <span style="font-size: .9em;" data-placement="right" data-toggle="tooltip" data-original-title="Klikni ovdje ako u tablici ispod želiš prikazati zahtjeve za prijevod dokumenata.">
                            <i class="fa fa-info-circle"></i>
                        </span>
                    @endif
                </button>
            </div>
        </div>
    </div>

    <div id="download-message" class="d-none mt-3 text-center">
        <div class="spinner-border spinner-border-sm text-info" role="status">
            <span class="sr-only">Loading...</span>
        </div>
        Priprema dokumenta za preuzimanje...
    </div>

    <div id="wizard-table-container" @if(request()->hasCookie('documentsTab') && request()->cookie('documentsTab') !== 'wizard') style="display: none;" @endif>
        <table class="table table-bordered table-hover table-striped" id="documents-table">
            <thead>
            <tr>
                <th>ID</th>
                <th>Dokument</th>
                <th>Bilješka</th>
                <th>Stvoren</th>
                <th>Ažuriran</th>
                <th></th>
            </tr>
            </thead>
        </table>
    </div>
    <div id="editor-table-container" @if(request()->cookie('documentsTab') !== 'editor') style="display: none;" @endif>
        <table class="table table-bordered table-hover table-striped" id="document-drafts-table">
            <thead>
            <tr>
                <th>ID</th>
                <th>Dokument</th>
                <th>Bilješka</th>
                <th>Stvoren</th>
                <th>Ažuriran</th>
                <th></th>
            </tr>
            </thead>
        </table>
    </div>

    <div id="translations-table-container" @if(request()->cookie('documentsTab') !== 'translations') style="display: none;" @endif>
        <table class="table table-bordered table-hover table-striped" id="translations-table">
            <thead>
            <tr>
                <th>ID</th>
                <th>Prijevod</th>
                <th>Status</th>
                <th>Stvoren</th>
                <th>Ažuriran</th>
                <th></th>
            </tr>
            </thead>
        </table>
    </div>

    <div class="mt-5 text-center @if(request()->cookie('documentsTab') !== 'translations') d-none @endif" id="upload-translation">
        Prijevode možeš naručiti za sve dokumente izrađene putem naših obrazaca i primjera klikom na <strong>"Odaberi > Prevedi na engleski"</strong>
        <div class="mt-3">
            ili
        </div>

        <hr/>

        <h4 class="mt-3">
            <span class="d-none d-lg-inline">
                <span data-placement="right" data-toggle="tooltip" data-original-title="Klikni ovdje ako želiš zatražiti prijevod vlastitog dokumenta koji nije izrađen preko Pravomata.">
                    <a href="{{ route('translation.custom.checkout') }}">Zatraži prijevod vlastitog dokumenta <i class="fa fa-info-circle"></i></a>
                </span>
            </span>
            <span class="d-inline d-lg-none">
                <a href="{{ route('translation.custom.checkout') }}">Zatraži prijevod vlastitog dokumenta</a>
            </span>
        </h4>

        <hr/>

    </div>

    <div id="modal-comment" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <form method="post" action="">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Bilješka</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <textarea rows="4" placeholder="Upiši bilješku..." name="comment"
                                      class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-info">Spremi bilješku</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div id="modal-editor-comment" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <form method="post" action="">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Bilješka</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <textarea rows="4" placeholder="Upiši bilješku..." name="comment"
                                      class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-info">Spremi bilješku</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div id="modal-title" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <form method="post" action="">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Naziv dokumenta</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <input required type="text" placeholder="Unesi naziv dokumenta..." name="title"
                                   class="form-control"/>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-info">Spremi naziv</button>
                    </div>
                </div>
            </form>>
        </div>
    </div>
@endsection

@include('documents.partials.index.wizard_scripts')
@include('documents.partials.index.editor_scripts')
@include('documents.partials.index.translations_scripts')

@push('scripts')
    <script type="text/javascript">
        $(function() {

            // set documents tab cookie expiration date (+1 year)
            const date = new Date();
            date.setTime(date.getTime() + (365 * 24 * 60 * 60 * 1000));

            const tabCookieExpiration = "expires=" + date.toUTCString();

            $('#wizard-toggle').on('click', function(){
                // remember tab
                document.cookie = 'documentsTab=wizard; ' + tabCookieExpiration + "; path=/";

                $('#editor-toggle').removeClass('btn-info').addClass('btn-default');
                $('#translations-toggle').removeClass('btn-info').addClass('btn-default');
                $(this).removeClass('btn-default').addClass('btn-info');

                $('#editor-table-container').hide();
                $('#translations-table-container').hide();
                $('#wizard-table-container').show();
                $('#documents-table').DataTable().draw(); // Redraw the DataTable

                $('#upload-translation').addClass('d-none');
            });

            $('#editor-toggle').on('click', function(){
                // remember tab
                document.cookie = 'documentsTab=editor; ' + tabCookieExpiration + "; path=/";

                $('#wizard-toggle').removeClass('btn-info').addClass('btn-default');
                $('#translations-toggle').removeClass('btn-info').addClass('btn-default');
                $(this).removeClass('btn-default').addClass('btn-info');

                $('#wizard-table-container').hide();
                $('#translations-table-container').hide();
                $('#editor-table-container').show();
                $('#document-drafts-table').DataTable().draw(); // Redraw the DataTable

                $('#upload-translation').addClass('d-none');
            });

            $('#translations-toggle').on('click', function(){
                // remember tab
                document.cookie = 'documentsTab=translations; ' + tabCookieExpiration + "; path=/";

                $('#wizard-toggle').removeClass('btn-info').addClass('btn-default');
                $('#editor-toggle').removeClass('btn-info').addClass('btn-default');
                $(this).removeClass('btn-default').addClass('btn-info');

                $('#wizard-table-container').hide();
                $('#editor-table-container').hide();
                $('#translations-table-container').show();
                $('#translations-table').DataTable().draw(); // Redraw the DataTable

                $('#upload-translation').removeClass('d-none');
            });
        });
    </script>
@endpush
