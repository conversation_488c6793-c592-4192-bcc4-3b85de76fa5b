@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Upi<PERSON>i podatke o darovatelju
                </div>
                <div class="card-body" id="donor_content">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'donor_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: <PERSON><PERSON>']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'donor_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'donor_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'donor_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'donor_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'donor_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>

                    </div>

                    <div class="authorized_donor_persons">

                        <div class="row">
                            <div class="form-group col-lg-12 pt-2">
                                {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog darovatelja', ['class' => 'authorized_donor_persons_exist form-check-input', 'checked' => !empty($model->authorized_donor_persons)], 'Ovu opciju označi ako je darovatelj pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun darovatelja u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je darovatelj fizička osoba (obrt, samostalna djelatnost, itd.), ali je darovatelj ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                            </div>
                        </div>

                        <div class="authorized_donor_persons_container dynamic"
                             style="@if(empty($model->authorized_donor_persons)) display:none; @endif">

                            @if(!empty($model->authorized_donor_persons))
                                @foreach($model->authorized_donor_persons as $i => $_authorized_person)
                                    <div class="authorized_donor_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_donor_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_donor_persons[$i][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'],  null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa darovatelja') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_donor_persons[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_donor_persons[$i][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_donor_persons[$i][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_donor_persons[$i][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_donor_persons[$i][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_donor_persons[$i][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif

                        </div>

                        <div class="row add_authorized_donor_person_container"
                             style="@if(empty($model->authorized_donor_persons)) display:none; @endif">
                            <div class="form-group col-lg-12">
                                <a data-id="authorized_donor_persons" class="btn btn-info add_authorized_donor_person">+ Dodaj
                                    zastupnika</a>
                            </div>
                        </div>

                        <hr class="authorized_donor_persons_separator" style="@if(empty($model->authorized_donor_persons)) display:none; @endif"/>

                    </div>

                    <div class="row">
                        <div class="form-group col-lg-12 pt-2">
                            {{ Form::fCheckbox($model, 'donor_cosigner_exists', 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se daruje dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input donor_cosigner_exists'], 'Prema Obiteljskom zakonu za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike poput otuđenja cijele stvari potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                        </div>
                    </div>

                    <div class="donor_cosigner_container"
                         style="{{ !isset($model->donor_cosigner_exists) || (!$model->donor_cosigner_exists) ? 'display:none;' : '' }}">
                        <hr/>
                        <div class="maps_autofill_container">
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fDropdown($model, 'donor_cosigner_type', null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                </div>
                            </div>
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'donor_cosigner_name', null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'donor_cosigner_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'donor_cosigner_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fNumber($model, 'donor_cosigner_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'donor_cosigner_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fNumber($model, 'donor_cosigner_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div id="additional_donors_container"
                 class="dynamic">
                <div id="additional_donors">
                    @if(!empty($model->additional_donors))
                        @foreach($model->additional_donors as $i => $_additional_donor)

                            <div class="card mb-4 additional_donor_content">
                                <div class="card-header">
                                    Dodani darovatelj
                                    <strong><a class="btn btn-danger btn-sm float-right remove_additional_donor"><i
                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                </div>
                                <div class="card-body">
                                    <div class="maps_autofill_container">
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_donors[$i][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_donors[$i][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_donors[$i][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_donors[$i][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_donors[$i][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_donors[$i][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="authorized_donor_persons">

                                        <div class="row">
                                            <div class="form-group col-lg-12 pt-2">
                                                {{ Form::fCheckbox($model, "", 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog darovatelja', ['class' => 'authorized_donor_persons_exist form-check-input', 'checked' => !empty($model->authorized_additional_donor_persons[$i])], 'Ovu opciju označi ako je darovatelj pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun darovatelja u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je darovatelj fizička osoba (obrt, samostalna djelatnost, itd.), ali je darovatelj ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                            </div>
                                        </div>

                                        <div class="authorized_donor_persons_container dynamic"
                                             style="@if(empty($model->authorized_additional_donor_persons[$i])) display:none; @endif">

                                            @if(!empty($model->authorized_additional_donor_persons[$i]))
                                                @foreach($model->authorized_additional_donor_persons[$i] as $j => $_authorized_additional_donor_person)
                                                    <div class="authorized_donor_person_content maps_autofill_container">
                                                        <hr/>
                                                        <div class="row">
                                                            <div class="col-lg-12">
                                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_donor_person"><i
                                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_donor_persons[$i][$j][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa darovatelja') }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_donor_persons[$i][$j][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_donor_persons[$i][$j][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_donor_persons[$i][$j][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_donor_persons[$i][$j][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_donor_persons[$i][$j][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fNumber($model, "authorized_additional_donor_persons[$i][$j][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif

                                        </div>

                                        <div class="row add_authorized_donor_person_container"
                                             style="@if(empty($model->authorized_additional_donor_persons[$i])) display:none; @endif">
                                            <div class="form-group col-lg-12">
                                                <a data-id="authorized_additional_donor_persons[{{$i}}]"
                                                   class="btn btn-info add_authorized_donor_person">+ Dodaj
                                                    zastupnika</a>
                                            </div>
                                        </div>

                                        <hr class="authorized_donor_persons_separator" style="@if(empty($model->authorized_additional_donor_persons[$i])) display:none; @endif"/>

                                    </div>

                                    <div class="row">
                                        <div class="form-group col-lg-12 pt-2">
                                            {{ Form::fCheckbox($model, "additional_donors[$i][donor_cosigner_exists]", 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se prodaje dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input donor_cosigner_exists', 'checked' => !empty($model->additional_donors[$i]['donor_cosigner_exists'])], 'Prema Obiteljskom zakonu za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike poput otuđenja cijele stvari potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                                        </div>
                                    </div>

                                    <div class="donor_cosigner_container"
                                         style="{{ !isset($model->additional_donors[$i]["donor_cosigner_exists"]) || (!$model->additional_donors[$i]["donor_cosigner_exists"]) ? 'display:none;' : '' }}">
                                        <hr/>
                                        <div class="maps_autofill_container">
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fDropdown($model, "additional_donors[$i][donor_cosigner_type]", null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                                </div>
                                            </div>
                                            <hr/>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_donors[$i][donor_cosigner_name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_donors[$i][donor_cosigner_address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_donors[$i][donor_cosigner_city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fNumber($model, "additional_donors[$i][donor_cosigner_postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_donors[$i][donor_cosigner_country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fNumber($model, "additional_donors[$i][donor_cosigner_oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                    <div id="additional_donor_template" class="d-none">
                        <div class="card mb-4 additional_donor_content">
                            <div class="card-header">
                                Dodani darovatelj
                                <strong><a class="btn btn-danger btn-sm float-right remove_additional_donor"><i
                                                class="fa fa-trash"></i> Ukloni</a></strong>
                            </div>
                            <div class="card-body">
                                <div class="maps_autofill_container">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_donors[{INDEX}][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_donors[{INDEX}][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_donors[{INDEX}][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_donors[{INDEX}][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_donors[{INDEX}][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_donors[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="authorized_donor_persons">

                                    <div class="row">
                                        <div class="form-group col-lg-12 pt-2">
                                            {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog darovatelja', ['class' => 'authorized_donor_persons_exist form-check-input'], 'Ovu opciju označi ako je darovatelj pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun darovatelja u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je darovatelj fizička osoba (obrt, samostalna djelatnost, itd.), ali je darovatelj ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                        </div>
                                    </div>

                                    <div class="authorized_donor_persons_container dynamic"
                                         style="display:none;">

                                    </div>

                                    <div class="row add_authorized_donor_person_container" style="display:none;">
                                        <div class="form-group col-lg-12">
                                            <a data-id="authorized_additional_donor_persons[{INDEX}]"
                                               class="btn btn-info add_authorized_donor_person">+ Dodaj
                                                zastupnika</a>
                                        </div>
                                    </div>

                                    <hr class="authorized_donor_persons_separator" style="display:none;"/>

                                </div>

                                <div class="row">
                                    <div class="form-group col-lg-12 pt-2">
                                        {{ Form::fCheckbox($model, "additional_donors[{INDEX}][donor_cosigner_exists]", 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se daruje dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input donor_cosigner_exists'], 'Prema Obiteljskom zakonu za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike poput otuđenja cijele stvari potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                                    </div>
                                </div>

                                <div class="donor_cosigner_container"
                                     style="display:none;">
                                    <hr/>
                                    <div class="maps_autofill_container">
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fDropdown($model, "additional_donors[{INDEX}][donor_cosigner_type]", null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                            </div>
                                        </div>
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_donors[{INDEX}][donor_cosigner_name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_donors[{INDEX}][donor_cosigner_address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_donors[{INDEX}][donor_cosigner_city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_donors[{INDEX}][donor_cosigner_postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_donors[{INDEX}][donor_cosigner_country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_donors[{INDEX}][donor_cosigner_oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-2">
                <div class="form-group col-lg-12">
                      <span data-toggle="tooltip"
                            data-original-title='Ako je nekretnina koja se daruje u suvlasništvu, pomoću ove opcije možeš dodati sve suvlasnike koji ugovorom daruju svoj suvlasnički udio.'>

                          <a class="btn btn-info btn-block" id="add_additional_donor">+ Dodaj darovatelja <i class="fa fa-info-circle px-lg-0 px-2"></i></a>
                      </span>
                </div>
            </div>

            <div id="authorized_donor_person_template" class="d-none">
                <div class="authorized_donor_person_content maps_autofill_container">
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            <strong><a class="btn btn-danger btn-sm float-right remove_authorized_donor_person"><i
                                            class="fa fa-trash"></i> Ukloni</a></strong>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa darovatelja') }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fNumber($model, "{ID}[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o obdareniku
                </div>
                <div class="card-body" id="beneficiary_content">
                    <div class="maps_autofill_container">

                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'beneficiary_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Stanko Zorić']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'beneficiary_address', null, 'Adresa',
                                ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'beneficiary_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'beneficiary_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'beneficiary_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'beneficiary_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>
                    </div>

                    <div class="authorized_beneficiary_persons">

                        <div class="row">
                            <div class="form-group col-lg-12 pt-2">
                                {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog obdarenika', ['class' => 'authorized_beneficiary_persons_exist form-check-input', 'checked' => !empty($model->authorized_beneficiary_persons)], 'Ovu opciju označi ako je obdarenik pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun obdarenika u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je obdarenik fizička osoba (obrt, samostalna djelatnost, itd.), ali je obdarenik ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                            </div>
                        </div>

                        <div class="authorized_beneficiary_persons_container dynamic"
                             style="@if(empty($model->authorized_beneficiary_persons)) display:none; @endif">

                            @if(!empty($model->authorized_beneficiary_persons))
                                @foreach($model->authorized_beneficiary_persons as $i => $_authorized_person)
                                    <div class="authorized_beneficiary_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_beneficiary_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_beneficiary_persons[$i][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa obdarenika') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_beneficiary_persons[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_beneficiary_persons[$i][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_beneficiary_persons[$i][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_beneficiary_persons[$i][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_beneficiary_persons[$i][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_beneficiary_persons[$i][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif

                        </div>

                        <div class="row add_authorized_beneficiary_person_container"
                             style="@if(empty($model->authorized_beneficiary_persons)) display:none; @endif">
                            <div class="form-group col-lg-12">
                                <a data-id="authorized_beneficiary_persons" class="btn btn-info add_authorized_beneficiary_person">+ Dodaj
                                    zastupnika</a>
                            </div>
                        </div>

                    </div>

                </div>
            </div>

            <div id="additional_beneficiaries_container"
                 class="dynamic">
                <div id="additional_beneficiaries">
                    @if(!empty($model->additional_beneficiaries))
                        @foreach($model->additional_beneficiaries as $i => $_additional_beneficiary)

                            <div class="card mb-4 additional_beneficiary_content">
                                <div class="card-header">
                                    Dodani obdarenik
                                    <strong><a class="btn btn-danger btn-sm float-right remove_additional_beneficiary"><i
                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                </div>
                                <div class="card-body">
                                    <div class="maps_autofill_container">
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_beneficiaries[$i][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_beneficiaries[$i][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_beneficiaries[$i][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_beneficiaries[$i][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_beneficiaries[$i][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_beneficiaries[$i][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="authorized_beneficiary_persons">

                                        <div class="row">
                                            <div class="form-group col-lg-12 pt-2">
                                                {{ Form::fCheckbox($model, "", 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog obdarenika', ['class' => 'authorized_beneficiary_persons_exist form-check-input', 'checked' => !empty($model->authorized_additional_beneficiary_persons[$i])], 'Ovu opciju označi ako je obdarenik pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun obdarenika u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je obdarenik fizička osoba (obrt, samostalna djelatnost, itd.), ali je obdarenik ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                            </div>
                                        </div>

                                        <div class="authorized_beneficiary_persons_container dynamic"
                                             style="@if(empty($model->authorized_additional_beneficiary_persons[$i])) display:none; @endif">

                                            @if(!empty($model->authorized_additional_beneficiary_persons[$i]))
                                                @foreach($model->authorized_additional_beneficiary_persons[$i] as $j => $_authorized_additional_beneficiary_person)
                                                    <div class="authorized_beneficiary_person_content maps_autofill_container">
                                                        <hr/>
                                                        <div class="row">
                                                            <div class="col-lg-12">
                                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_beneficiary_person"><i
                                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_beneficiary_persons[$i][$j][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa obdarenika') }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_beneficiary_persons[$i][$j][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_beneficiary_persons[$i][$j][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_beneficiary_persons[$i][$j][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_beneficiary_persons[$i][$j][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_beneficiary_persons[$i][$j][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fNumber($model, "authorized_additional_beneficiary_persons[$i][$j][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif

                                        </div>

                                        <div class="row add_authorized_beneficiary_person_container"
                                             style="@if(empty($model->authorized_additional_beneficiary_persons[$i])) display:none; @endif">
                                            <div class="form-group col-lg-12">
                                                <a data-id="authorized_additional_beneficiary_persons[{{$i}}]"
                                                   class="btn btn-info add_authorized_beneficiary_person">+ Dodaj
                                                    zastupnika</a>
                                            </div>
                                        </div>

                                    </div>

                                </div>
                            </div>
                        @endforeach
                    @endif
                    <div id="additional_beneficiary_template" class="d-none">
                        <div class="card mb-4 additional_beneficiary_content">

                            <div class="card-header">
                                Dodani obdarenik
                                <strong><a class="btn btn-danger btn-sm float-right remove_additional_beneficiary"><i
                                                class="fa fa-trash"></i> Ukloni</a></strong>
                            </div>
                            <div class="card-body">
                                <div class="maps_autofill_container">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_beneficiaries[{INDEX}][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_beneficiaries[{INDEX}][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_beneficiaries[{INDEX}][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_beneficiaries[{INDEX}][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_beneficiaries[{INDEX}][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_beneficiaries[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="authorized_beneficiary_persons">

                                    <div class="row">
                                        <div class="form-group col-lg-12 pt-2">
                                            {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog obdarenika', ['class' => 'authorized_beneficiary_persons_exist form-check-input'], 'Ovu opciju označi ako je obdarenik pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun obdarenika u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je obdarenik fizička osoba (obrt, samostalna djelatnost, itd.), ali je obdarenik ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                        </div>
                                    </div>

                                    <div class="authorized_beneficiary_persons_container dynamic"
                                         style="display:none;">

                                    </div>

                                    <div class="row add_authorized_beneficiary_person_container" style="display:none;">
                                        <div class="form-group col-lg-12">
                                            <a data-id="authorized_additional_beneficiary_persons[{INDEX}]"
                                               class="btn btn-info add_authorized_beneficiary_person">+ Dodaj
                                                zastupnika</a>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-lg-12">

                                <span data-toggle="tooltip"
                                      data-original-title='Ako nekretninu u dar prima više osoba koje će darovanjem postati suvlasnici nekretnine, pomoću ove opcije možeš dodati sve takve obdarenike.'>
                        <a class="btn btn-info btn-block" id="add_additional_beneficiary">+ Dodaj obdarenika  <i class="fa fa-info-circle px-lg-0 px-2"></i> </a>
                    </span>

                </div>
            </div>

            <div id="authorized_beneficiary_person_template" class="d-none">
                <div class="authorized_beneficiary_person_content maps_autofill_container">
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            <strong><a class="btn btn-danger btn-sm float-right remove_authorized_beneficiary_person"><i
                                            class="fa fa-trash"></i> Ukloni</a></strong>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa obdarenika') }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fNumber($model, "{ID}[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                        </div>
                    </div>
                </div>
            </div>


        </div>


    </div>

    {{ Form::close() }}
@endsection
