@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Upiši adresu stana
                </div>
                <div class="card-body">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'property_address', null, 'Ulica i broj', ['autocomplete' => 'nope', 'class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ulica Grada Vukovara 24A']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'property_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'property_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'property_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o stanu
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'property_floor', '1. Na kojem katu se nalazi stan?') }}
                            {{ Form::fRadio($model, 'property_floor', 'podrum', 'Podrum', ['checked' => !isset($model->property_floor) ? 1 : 0]) }}
                            {{ Form::fRadio($model, 'property_floor', 'suteren', 'Suteren')}}
                            {{ Form::fRadio($model, 'property_floor', 'prizemlje', 'Prizemlje')}}
                            {{ Form::fRadio($model, 'property_floor', 'razizemlje', 'Razizemlje')}}
                            {{ Form::fRadio($model, 'property_floor', 'custom', Form::number('custom_property_floor', null, ['class' => 'radio_input_text', 'min' => 0, 'max' => 100, 'placeholder' => 'Npr: 2', 'style' => 'width:100px;  text-align:center;']) )}}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'property_custom_code', null, '2. Ako su stanovi u zgradi označeni brojevima ili drugom oznakom upiši broj stana ili tu oznaku <small>(opcionalno)</small>', ['placeholder' => 'Npr: XII']) }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fNumber($model, 'property_size', null, '3. Kolika je približna ukupna površina stana u m2?', ['step' => '.1', 'placeholder' => 'Npr: 55'],  'm2') }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            {{ Form::fLabel($model, 'rooms', '4. Od kojih prostorija se sastoji stan?') }}
                        </div>
                        <div class="form-group col-lg-4">
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'hallway', 'Hodnik', ['class' => 'form-check-input', 'id' => 'hallway']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'lobby', 'Predsoblje', ['class' => 'form-check-input', 'id' => 'lobby']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'bedroom', 'Spavaća soba', ['class' => 'form-check-input', 'id' => 'bedroom']) }}
                            </div>
                            <div id="additional_bedrooms_container" class="dynamic">
                                <div id="additional_bedrooms">
                                    @if(!empty($model->additional_bedrooms))
                                        @foreach($model->additional_bedrooms as $i => $_additional_bedroom)
                                            <div class="additional_bedroom_content">
                                                <div class="pb-1">
                                                    {{ Form::fCheckbox($model, "additional_bedrooms[$i][exists]", 1, 'Spavaća
                                                        soba', ['class' => 'form-check-input toggle_additional_bedroom', 'id' => "additional_bedrooms[$i][exists]", 'checked' => 'checked']) }}
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                    <div id="additional_bedroom_template" class="d-none">
                                        <div class="additional_bedroom_content">
                                            <div class="pb-1">
                                                {{ Form::fCheckbox($model, 'additional_bedrooms[{INDEX}][exists]', 1, 'Spavaća soba', ['class' => 'form-check-input toggle_additional_bedroom', 'id' => 'additional_bedrooms[{INDEX}][exists]', 'checked' => 'checked']) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        <a class="btn btn-link" id="add_additional_bedroom">+ Dodaj spavaću sobu</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-lg-4">
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'living_room', 'Dnevni boravak', ['class' => 'form-check-input', 'id' => 'living_room']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'kitchen', 'Kuhinja', ['class' => 'form-check-input', 'id' => 'kitchen']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'diner', 'Blagovaonica', ['class' => 'form-check-input', 'id' => 'diner']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'bathroom', 'Kupaonica', ['class' => 'form-check-input', 'id' => 'bathroom']) }}
                            </div>
                            <div id="additional_bathrooms_container" class="dynamic">
                                <div id="additional_bathrooms">
                                    @if(!empty($model->additional_bathrooms))
                                        @foreach($model->additional_bathrooms as $i => $_additional_bathroom)
                                            <div class="additional_bathroom_content">
                                                <div class="pb-1">
                                                    {{ Form::fCheckbox($model, "additional_bathrooms[$i][exists]", 1, 'Kupaonica', ['class' => 'form-check-input toggle_additional_bathroom', 'id' => "additional_bathrooms[$i][exists]", 'checked' => 'checked']) }}
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                    <div id="additional_bathroom_template" class="d-none">
                                        <div class="additional_bathroom_content">
                                            <div class="pb-1">
                                                {{ Form::fCheckbox($model, 'additional_bathrooms[{INDEX}][exists]', 1, 'Kupaonica', ['class' => 'form-check-input toggle_additional_bathroom', 'id' => 'additional_bathrooms[{INDEX}][exists]', 'checked' => 'checked']) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        <a class="btn btn-link" id="add_additional_bathroom">+ Dodaj kupaonicu</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group col-lg-4">

                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'toilet', 'Sanitarni čvor', ['class' => 'form-check-input', 'id' => 'toilet']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'storage', 'Ostava', ['class' => 'form-check-input', 'id' => 'storage']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'balcony', 'Balkon', ['class' => 'form-check-input', 'id' => 'balcony']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'loggia', 'Loggia', ['class' => 'form-check-input', 'id' => 'loggia']) }}
                            </div>
                            <div class="pb-1">
                                {{ Form::fCheckbox($model, 'rooms[]', 'terrace', 'Terasa', ['class' => 'form-check-input', 'id' => 'terrace']) }}
                            </div>
                            <div id="additional_rooms_container" class="dynamic">
                                <div id="additional_rooms">
                                    @if(!empty($model->additional_rooms))
                                        @foreach($model->additional_rooms as $i => $_additional_room)
                                            <div class="additional_room_content">
                                                <div class="pb-1">
                                                    {{ Form::fCheckbox($model, "additional_rooms[$i][exists]", 1, Form::text("additional_rooms[$i][name]", null, ['class' => 'checkbox_input_text', 'placeholder' => 'Npr: Ured', 'style' => 'width:150px;  text-align:center;']), ['class' => 'form-check-input toggle_additional_room', 'id' => "additional_rooms[$i][exists]", 'checked' => 'checked']) }}
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                    <div id="additional_room_template" class="d-none">
                                        <div class="additional_room_content">
                                            <div class="pb-1">
                                                {{ Form::fCheckbox($model, 'additional_rooms[{INDEX}][exists]', 1, Form::text("additional_rooms[{INDEX}][name]", null, ['class' => 'checkbox_input_text', 'placeholder' => 'Npr: Ured', 'style' => 'width:150px;  text-align:center;']), ['class' => 'form-check-input toggle_additional_room', 'id' => 'additional_rooms[{INDEX}][exists]', 'checked' => 'checked']) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        <a class="btn btn-link" id="add_additional_room">+ Dodaj prostoriju</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'furnished', '5. Daje li se stan u najam namješten ili prazan?', 'Ako se stan u najam daje potpuno ili djelomično namješten, u primopredajnom zapisniku koji je sastavni dio ovog Ugovora potrebno je popisati i podrobno opisati pokućstvo, opremu i kućanske uređaje koji se nalaze u stanu. Ako se stan u najam daje nenamješten, u primopredajni zapisnik stavlja se naznaka da u stanu nema pokućstva, opreme ni kućanskih uređaja. U primopredajnom zapisniku potrebno je i navesti stanja svih brojila u stanu u trenutku primopredaje, kao i opisati stanje zidova, stropova, podova i vrata.') }}
                            {{ Form::fRadio($model, 'furnished', 'potpuno namješten', 'Potpuno namješten', ['checked' => !isset($model->furnished) ? 1 : 0])}}
                            {{ Form::fRadio($model, 'furnished', 'djelomično namješten', 'Djelomično namješten')}}
                            {{ Form::fRadio($model, 'furnished', 'prazan', 'Prazan')}}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'has_private_parking', '6. Ugovara li se uz najam stana i uporaba privatnog parkinga?') }}
                            {{ Form::fRadio($model, 'has_private_parking', 0, 'Ne', ['id' => 'has_not_private_parking', 'checked' => !isset($model->has_private_parking) ? 1 : 0])}}
                            {{ Form::fRadio($model, 'has_private_parking', 1, 'Da', ['id' => 'has_private_parking'])}}
                        </div>
                    </div>
                    <div id="private_parking"
                         style="{{ !isset($model->has_private_parking) ||  !$model->has_private_parking ? "display:none" : ""}}">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'private_parking_location', '<span class="dot"></span> Gdje se nalazi parking?') }}
                                {{ Form::fRadio($model, 'private_parking_location', 'u dvorištu zgrade', 'U dvorištu zgrade', ['checked' => !isset($model->private_parking_location) ? 1 : 0])}}
                                {{ Form::fRadio($model, 'private_parking_location', 'u garaži u zgradi', 'U garaži u zgradi')}}
                                {{ Form::fRadio($model, 'private_parking_location', 'u garaži u dvorištu zgrade', 'U garaži u dvorištu zgrade')}}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fNumber($model, 'private_parking_spots', null, '<span class="dot"></span> Upiši broj privatnih parkirnih mjesta koji se daje na korištenje uz najam stana', ['placeholder' => 'Npr: 2']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'private_parking_fee_included_in_rent', '<span class="dot"></span> Je li naknada za korištenje parkinga uključena u iznos najamnine?') }}
                                {{ Form::fRadio($model, 'private_parking_fee_included_in_rent', 1, 'Da', ['id' => 'private_parking_fee_included_in_rent', 'checked' => !isset($model->private_parking_fee_included_in_rent) ? 1 : 0] ) }}
                                {{ Form::fRadio($model, 'private_parking_fee_included_in_rent', 0, 'Ne', ['id' => 'private_parking_fee_not_included_in_rent'] ) }}
                            </div>
                        </div>
                        <div class="row" id="private_parking_fee_container"
                             style="{{ !isset($model->private_parking_fee_included_in_rent) ||  $model->private_parking_fee_included_in_rent ? "display:none" : ""}}">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fText($model, 'private_parking_fee', null, '<span class="dot"></span> Upiši bruto iznos mjesečne posebne naknade za korištenje parkinga', ['placeholder' => 'Npr: 150,00', 'data-currency' => 'eur'], '€') }}
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'has_private_storage', '7. Ugovara li se uz najam stana i uporaba drvarnice (spremišta) koja pripada stanu?') }}
                            {{ Form::fRadio($model, 'has_private_storage', 0, 'Ne', ['id' => 'has_not_private_storage', 'checked' => !isset($model->has_private_storage) ? 1 : 0] ) }}
                            {{ Form::fRadio($model, 'has_private_storage', 1, 'Da', ['id' => 'has_private_storage']) }}
                        </div>
                    </div>
                    <div id="private_storage"
                         style="{{ !isset($model->has_private_storage) ||  !$model->has_private_storage ? "display:none" : ""}}">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'private_storage_location', '<span class="dot"></span> Gdje se nalazi drvarnica (spremište)?') }}
                                {{ Form::fRadio($model, 'private_storage_location', 'unutar zgrade u kojoj je Stan', 'Unutar zgrade u kojoj je stan', ['checked' => !isset($model->private_storage_location) ? 1 : 0] ) }}
                                {{ Form::fRadio($model, 'private_storage_location', 'u dvorištu zgrade u kojoj je Stan', 'U dvorištu zgrade u kojoj je stan') }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fNumber($model, 'private_storage_size', null, '<span class="dot"></span> Koja je približna površina drvarnice (spremišta) u m2?', ['step' => '.1', 'placeholder' => 'Npr: 10'], 'm2') }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fLabel($model, 'private_storage_fee_included_in_rent', '<span class="dot"></span> Je li naknada za korištenje drvarnice (spremišta) uključena u iznos najamnine?') }}
                                {{ Form::fRadio($model, 'private_storage_fee_included_in_rent', 1, 'Da', ['id' => 'private_storage_fee_included_in_rent', 'checked' => !isset($model->private_storage_fee_included_in_rent) ? 1 : 0] ) }}
                                {{ Form::fRadio($model, 'private_storage_fee_included_in_rent', 0, 'Ne', ['id' => 'private_storage_fee_not_included_in_rent']) }}
                            </div>
                        </div>
                        <div class="row" id="private_storage_fee_container"
                             style="{{ !isset($model->private_storage_fee_included_in_rent) ||  $model->private_storage_fee_included_in_rent ? "display:none" : ""}}">
                            <div class="form-group col-lg-12">
                                <hr/>
                                {{ Form::fText($model, 'private_storage_fee', null, '<span class="dot"></span> Upiši bruto iznos mjesečne posebne naknade za korištenje drvarnice (spremišta)', ['placeholder' => 'Npr: 150,00', 'data-currency' => 'eur'], '€') }}
                            </div>
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'energy_certificate_class', '8. U koji energetski razred je svrstan stan koji se daje u najam?') }}
                            {{ Form::fRadio($model, 'energy_certificate_class', 'A+', 'A+', ['checked' => !isset($model->energy_certificate_class) ? 1 : 0]) }}
                            {{ Form::fRadio($model, 'energy_certificate_class', 'A', 'A') }}
                            {{ Form::fRadio($model, 'energy_certificate_class', 'B', 'B') }}
                            {{ Form::fRadio($model, 'energy_certificate_class', 'C', 'C') }}
                            {{ Form::fRadio($model, 'energy_certificate_class', 'D', 'D') }}
                            {{ Form::fRadio($model, 'energy_certificate_class', 'E', 'E') }}
                            {{ Form::fRadio($model, 'energy_certificate_class', 'F', 'F') }}
                            {{ Form::fRadio($model, 'energy_certificate_class', 'G', 'G') }}
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>


    {{ Form::close() }}
@endsection
