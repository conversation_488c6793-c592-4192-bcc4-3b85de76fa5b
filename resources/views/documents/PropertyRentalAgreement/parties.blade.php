@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">
            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o najmodavcu
                    <span data-toggle="tooltip"
                          data-original-title="Najmodavac je osoba koja je vlasnik stana ili druga osoba koja ima pravo dati stan u najam. Može biti fizička ili pravna osoba.">
                        <i class="fa fa-info-circle"></i>
                    </span>
                </div>
                <div class="card-body" id="landlord_content">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'landlord_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: <PERSON><PERSON>']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'landlord_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'landlord_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'landlord_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'landlord_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'landlord_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>
                    </div>

                    <div class="authorized_landlord_persons">

                        <div class="row">
                            <div class="form-group col-lg-12 pt-2">
                                {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog najmodavca', ['class' => 'authorized_landlord_persons_exist form-check-input', 'checked' => !empty($model->authorized_landlord_persons)], 'Ovu opciju označi ako je najmodavac pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun najmodavca u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je najmodavac fizička osoba (obrt, samostalna djelatnost, itd.), ali je najmodavac ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                            </div>
                        </div>

                        <div class="authorized_landlord_persons_container dynamic"
                             style="@if(empty($model->authorized_landlord_persons)) display:none; @endif">

                            @if(!empty($model->authorized_landlord_persons))
                                @foreach($model->authorized_landlord_persons as $i => $_authorized_person)
                                    <div class="authorized_landlord_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_landlord_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_landlord_persons[$i][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa najmodavca') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_landlord_persons[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_landlord_persons[$i][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_landlord_persons[$i][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_landlord_persons[$i][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_landlord_persons[$i][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_landlord_persons[$i][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif

                        </div>

                        <div class="row add_authorized_landlord_person_container"
                             style="@if(empty($model->authorized_landlord_persons)) display:none; @endif">
                            <div class="form-group col-lg-12">
                                <a data-id="authorized_landlord_persons" class="btn btn-info add_authorized_landlord_person">+ Dodaj
                                    zastupnika</a>
                            </div>
                        </div>

                        <hr class="authorized_landlord_persons_separator" style="@if(empty($model->authorized_landlord_persons)) display:none; @endif"/>

                    </div>

                    <div class="row">
                        <div class="form-group col-lg-12 pt-2">
                            {{ Form::fCheckbox($model, 'landlord_cosigner_exists', 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se u najam na dulje od jedne godine daje stan koji je dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input landlord_cosigner_exists'], 'Prema Obiteljskom zakonu, za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike, poput davanja cijele stvari u najam na dulje od jedne godine, potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                        </div>
                    </div>

                    <div class="landlord_cosigner_container"
                         style="{{ !isset($model->landlord_cosigner_exists) || (!$model->landlord_cosigner_exists) ? 'display:none;' : '' }}">
                        <hr/>
                        <div class="maps_autofill_container">
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{ Form::fDropdown($model, 'landlord_cosigner_type', null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                </div>
                            </div>
                            <hr/>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'landlord_cosigner_name', null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'landlord_cosigner_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'landlord_cosigner_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fNumber($model, 'landlord_cosigner_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'landlord_cosigner_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fNumber($model, 'landlord_cosigner_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="additional_landlords_container"
                 class="dynamic">
                <div id="additional_landlords">
                    @if(!empty($model->additional_landlords))
                        @foreach($model->additional_landlords as $i => $_additional_landlord)

                            <div class="card mb-4 additional_landlord_content">
                                <div class="card-header">
                                    Dodani najmodavac
                                    <strong><a class="btn btn-danger btn-sm float-right remove_additional_landlord"><i
                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                </div>
                                <div class="card-body">
                                    <div class="maps_autofill_container">
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_landlords[$i][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_landlords[$i][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_landlords[$i][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_landlords[$i][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_landlords[$i][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_landlords[$i][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="authorized_landlord_persons">

                                        <div class="row">
                                            <div class="form-group col-lg-12 pt-2">
                                                {{ Form::fCheckbox($model, "", 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog najmodavca', ['class' => 'authorized_landlord_persons_exist form-check-input', 'checked' => !empty($model->authorized_additional_landlord_persons[$i])], 'Ovu opciju označi ako je najmodavac pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun najmodavca u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je najmodavac fizička osoba (obrt, samostalna djelatnost, itd.), ali je najmodavac ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                            </div>
                                        </div>

                                        <div class="authorized_landlord_persons_container dynamic"
                                             style="@if(empty($model->authorized_additional_landlord_persons[$i])) display:none; @endif">

                                            @if(!empty($model->authorized_additional_landlord_persons[$i]))
                                                @foreach($model->authorized_additional_landlord_persons[$i] as $j => $_authorized_additional_landlord_person)
                                                    <div class="authorized_landlord_person_content maps_autofill_container">
                                                        <hr/>
                                                        <div class="row">
                                                            <div class="col-lg-12">
                                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_landlord_person"><i
                                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_landlord_persons[$i][$j][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa najmodavca') }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_landlord_persons[$i][$j][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_landlord_persons[$i][$j][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_landlord_persons[$i][$j][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_landlord_persons[$i][$j][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fText($model, "authorized_additional_landlord_persons[$i][$j][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                {{ Form::fNumber($model, "authorized_additional_landlord_persons[$i][$j][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif

                                        </div>

                                        <div class="row add_authorized_landlord_person_container"
                                             style="@if(empty($model->authorized_additional_landlord_persons[$i])) display:none; @endif">
                                            <div class="form-group col-lg-12">
                                                <a data-id="authorized_additional_landlord_persons[{{$i}}]"
                                                   class="btn btn-info add_authorized_landlord_person">+ Dodaj
                                                    zastupnika</a>
                                            </div>
                                        </div>

                                        <hr class="authorized_landlord_persons_separator" style="@if(empty($model->authorized_additional_landlord_persons[$i])) display:none; @endif"/>

                                    </div>

                                    <div class="row">
                                        <div class="form-group col-lg-12 pt-2">
                                            {{ Form::fCheckbox($model, "additional_landlords[$i][landlord_cosigner_exists]", 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se u najam na dulje od jedne godine daje stan koji je dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input landlord_cosigner_exists', 'checked' => !empty($model->additional_landlords[$i]['landlord_cosigner_exists'])], 'Prema Obiteljskom zakonu, za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike, poput davanja cijele stvari u najam na dulje od jedne godine, potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                                        </div>
                                    </div>

                                    <div class="landlord_cosigner_container"
                                         style="{{ !isset($model->additional_landlords[$i]["landlord_cosigner_exists"]) || (!$model->additional_landlords[$i]["landlord_cosigner_exists"]) ? 'display:none;' : '' }}">
                                        <hr/>
                                        <div class="maps_autofill_container">
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fDropdown($model, "additional_landlords[$i][landlord_cosigner_type]", null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                                </div>
                                            </div>
                                            <hr/>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_landlords[$i][landlord_cosigner_name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_landlords[$i][landlord_cosigner_address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_landlords[$i][landlord_cosigner_city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fNumber($model, "additional_landlords[$i][landlord_cosigner_postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "additional_landlords[$i][landlord_cosigner_country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fNumber($model, "additional_landlords[$i][landlord_cosigner_oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                    <div id="additional_landlord_template" class="d-none">
                        <div class="card mb-4 additional_landlord_content">
                            <div class="card-header">
                                Dodani najmodavac
                                <strong><a class="btn btn-danger btn-sm float-right remove_additional_landlord"><i
                                                class="fa fa-trash"></i> Ukloni</a></strong>
                            </div>
                            <div class="card-body">
                                <div class="maps_autofill_container">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_landlords[{INDEX}][name]", null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Ante Antić']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_landlords[{INDEX}][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_landlords[{INDEX}][city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_landlords[{INDEX}][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_landlords[{INDEX}][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_landlords[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                        </div>
                                    </div>
                                </div>

                                <div class="authorized_landlord_persons">

                                    <div class="row">
                                        <div class="form-group col-lg-12 pt-2">
                                            {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun ovog najmodavca', ['class' => 'authorized_landlord_persons_exist form-check-input'], 'Ovu opciju označi ako je najmodavac pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun najmodavca u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je najmodavac fizička osoba (obrt, samostalna djelatnost, itd.), ali je najmodavac ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                                        </div>
                                    </div>

                                    <div class="authorized_landlord_persons_container dynamic"
                                         style="display:none;">

                                    </div>

                                    <div class="row add_authorized_landlord_person_container" style="display:none;">
                                        <div class="form-group col-lg-12">
                                            <a data-id="authorized_additional_landlord_persons[{INDEX}]"
                                               class="btn btn-info add_authorized_landlord_person">+ Dodaj
                                                zastupnika</a>
                                        </div>
                                    </div>

                                    <hr class="authorized_landlord_persons_separator" style="display:none;"/>

                                </div>

                                <div class="row">
                                    <div class="form-group col-lg-12 pt-2">
                                        {{ Form::fCheckbox($model, "additional_landlords[{INDEX}][landlord_cosigner_exists]", 1, 'Postoji bračni ili izvanbračni drug ili životni partner koji na sklapanje ugovora mora dati suglasnost jer se u najam na dulje od jedne godine daje stan koji je dio bračne, izvanbračne ili partnerske stečevine', ['class' => 'form-check-input landlord_cosigner_exists'], 'Prema Obiteljskom zakonu, za izvanredne poslove na nekretninama ili pokretninama koje se upisuju u javne upisnike, poput davanja cijele stvari u najam na dulje od jedne godine, potrebno je zajedničko poduzimanje posla ili pisana suglasnost drugoga bračnog druga s ovjerom potpisa kod javnog bilježnika. Navedeno se primjenjuje i u slučaju partnerske stečevine uređene Zakonom o životnom partnerstvu osoba istog spola, s obzirom na to da je njime propisano da se na imovinske odnose životnih partnera koji nisu obuhvaćeni Zakonom o životnom partnerstvu osoba istog spola primjenjuju odredbe Obiteljskog zakona.') }}
                                    </div>
                                </div>

                                <div class="landlord_cosigner_container"
                                     style="display:none;">
                                    <hr/>
                                    <div class="maps_autofill_container">
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fDropdown($model, "additional_landlords[{INDEX}][landlord_cosigner_type]", null, [0 => 'Bračni drug', 1 => 'Izvanbračni drug', 2 => 'Životni partner'], 'Radi li se o bračnom ili izvanbračnom drugu ili životnom partneru?') }}
                                            </div>
                                        </div>
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_landlords[{INDEX}][landlord_cosigner_name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_landlords[{INDEX}][landlord_cosigner_address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_landlords[{INDEX}][landlord_cosigner_city]", null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_landlords[{INDEX}][landlord_cosigner_postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_landlords[{INDEX}][landlord_cosigner_country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "additional_landlords[{INDEX}][landlord_cosigner_oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-2">
                <div class="form-group col-lg-12">
                    <a class="btn btn-info btn-block" id="add_additional_landlord">+ Dodaj najmodavca</a>
                </div>
            </div>

            <div id="authorized_landlord_person_template" class="d-none">
                <div class="authorized_landlord_person_content maps_autofill_container">
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            <strong><a class="btn btn-danger btn-sm float-right remove_authorized_landlord_person"><i
                                            class="fa fa-trash"></i> Ukloni</a></strong>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa najmodavca') }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{ Form::fText($model, "{ID}[{INDEX}][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fNumber($model, "{ID}[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o najmoprimcu
                    <span data-toggle="tooltip"
                          data-original-title="Najmoprimac je fizička osoba koja sklapa ugovor o najmu stana i njime je pravno obvezana (primjerice, odgovorna je za plaćanje najamnine i druge obveze iz ugovora).">
                        <i class="fa fa-info-circle"></i>
                    </span>
                </div>
                <div class="card-body" id="tenant_content">
                    <div class="maps_autofill_container">

                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'tenant_name', null, 'Ime i prezime', ['placeholder' => 'Npr: Stanko Zorić']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'tenant_address', null, 'Adresa',
                                ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'tenant_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'tenant_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'tenant_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'tenant_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12 pt-2">
                                {{ Form::fCheckbox($model, 'spouse_tenant_exists', 1, 'Dodaj i bračnog druga kao sunajmoprimca', ['id' => 'spouse_tenant_exists'], 'Prema Zakonu o najmu stanova, ugovor o najmu stana sklapa se s jednom osobom, a iznimno s oba bračna druga. Ako želiš da bračni drug najmoprimca bude samo korisnik stana i ne želiš da istovremeno bude i sunajmoprimac, ovdje ga nemoj upisivati.' ) }}
                            </div>
                        </div>
                    </div>
                    <div id="spouse_tenant"
                         style="{{ !isset($model->spouse_tenant_exists) || (!$model->spouse_tenant_exists) ? 'display:none;' : '' }}">
                        <div class="maps_autofill_container">
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'spouse_tenant_name', null, 'Ime i prezime', ['placeholder' => 'Npr: Ana Zorić']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'spouse_tenant_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'spouse_tenant_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fNumber($model, 'spouse_tenant_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    {{ Form::fText($model, 'spouse_tenant_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                </div>
                                <div class="form-group col-lg-6">
                                    {{ Form::fNumber($model, 'spouse_tenant_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="additional_tenants_container"
                 class="dynamic">
                <div id="additional_tenants">
                    @if(!empty($model->additional_tenants))
                        @foreach($model->additional_tenants as $i => $_additional_tenant)

                            <div class="card mb-4 additional_tenant_content maps_autofill_container">
                                <div class="card-header">
                                    Dodani stanar
                                    <strong><a class="btn btn-danger btn-sm float-right remove_additional_tenant"><i
                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "additional_tenants[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "additional_tenants[$i][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                    <div id="additional_tenant_template" class="d-none">
                        <div class="card mb-4 additional_tenant_content maps_autofill_container">

                            <div class="card-header">
                                Dodani stanar
                                <strong><a class="btn btn-danger btn-sm float-right remove_additional_tenant"><i
                                                class="fa fa-trash"></i> Ukloni</a></strong>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, "additional_tenants[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić']) }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fNumber($model, "additional_tenants[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-lg-12">

                                <span data-toggle="tooltip"
                                      data-original-title="Prema Zakonu o najmu stanova, u ugovoru o najmu stana moraju se navesti podaci o osobama koje će uz najmoprimca koristiti stanom.">
                        <a class="btn btn-info btn-block" id="add_additional_tenant">+ Dodaj korisnika stana     <i class="fa fa-info-circle px-2 px-lg-0"></i> </a>
                    </span>

                </div>
            </div>


        </div>

    </div>


    {{ Form::close() }}
@endsection
