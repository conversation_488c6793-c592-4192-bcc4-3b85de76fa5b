@extends($builder->getLayout())
@section('content')
    @php

        $data["grantor_name"] = $builder->get("grantor_name");
        $data["grantor_address"] = $builder->get("grantor_address");
        $data["grantor_city"] = $builder->get("grantor_city");
        $data["grantor_postal_code"] = $builder->get("grantor_postal_code");
        $data["grantor_country"] = $builder->get("grantor_country");
        $data["grantor_oib"] = $builder->get("grantor_oib");

		$grantee_type = $builder->get("grantee_type", false);
        
        $data["grantee_type"] = "bračni drug";
        $data["acquisition_type"] = "bračnu stečevinu";
        if($grantee_type == 1){
            $data["grantee_type"] = "izvanbračni drug";
			$data["acquisition_type"] = "izvanbračnu stečevinu";
        }
        elseif($grantee_type == 2){
            $data["grantee_type"] = "životni partner";
			$data["acquisition_type"] = "partnersku stečevinu";
        }

        $data["grantee_name"] = $builder->get("grantee_name");
        $data["grantee_address"] = $builder->get("grantee_address");
        $data["grantee_city"] = $builder->get("grantee_city");
        $data["grantee_postal_code"] = $builder->get("grantee_postal_code");
        $data["grantee_country"] = $builder->get("grantee_country");
        $data["grantee_oib"] = $builder->get("grantee_oib");

		// realestate
        $data["additional_realestates"] = $builder->get("additional_realestates", false);

        // standardize array keys for additional_realestates
        if(!empty($data['additional_realestates'])){

            // standardize array keys
            $data['additional_realestates'] = array_values($data['additional_realestates']);

            foreach($data['additional_realestates'] as &$_additional_realestate){

                if(!empty($_additional_realestate['type_particular_additional_land_plots'])){
                    $_additional_realestate['type_particular_additional_land_plots'] = array_values($_additional_realestate['type_particular_additional_land_plots']);
                }

                if(!empty($_additional_realestate['type_other_additional_land_plots'])){
                    $_additional_realestate['type_other_additional_land_plots'] = array_values($_additional_realestate['type_other_additional_land_plots']);

                    foreach($_additional_realestate['type_other_additional_land_plots'] as &$_additional_land_plot) {
                        if(!empty($_additional_land_plot['type_other_additional_possession_area_and_identification'])) {
                            $_additional_land_plot['type_other_additional_possession_area_and_identification'] = array_values($_additional_land_plot['type_other_additional_possession_area_and_identification']);
                        }
                    }

                    unset($_additional_land_plot);
                }

                if(!empty($_additional_realestate['type_other_additional_possession_area_and_identification'])){
                    $_additional_realestate['type_other_additional_possession_area_and_identification'] = array_values($_additional_realestate['type_other_additional_possession_area_and_identification']);
                }

            }

            unset($_additional_realestate); // always unset if looping by reference!
        }

        $data['realestate_type'] = $builder->get('realestate_type', false) ?: 'particular';
        $data['type_particular_municipal_court'] = $builder->get('type_particular_municipal_court');
        $data['type_particular_land_registry'] = $builder->get('type_particular_land_registry');
        $data['type_particular_cadastral_municipality'] = $builder->get('type_particular_cadastral_municipality');
        $data['type_particular_land_registry_folio_number'] = $builder->get('type_particular_land_registry_folio_number');
        $data['type_particular_possession_number'] = $builder->get('type_particular_possession_number');
        $data['type_particular_possession_area'] = $builder->get('type_particular_possession_area');
		$data["type_particular_possession_area_type"] = $builder->get("type_particular_possession_area_type", null);
        $data['type_particular_possession_identification'] = $builder->get('type_particular_possession_identification');
        $data['type_particular_ownership_sheet_data'] = $builder->get('type_particular_ownership_sheet_data');

        $data["type_particular_additional_land_plots"] = $builder->get("type_particular_additional_land_plots", false);
        if(!empty($data['type_particular_additional_land_plots'])){
            $data['type_particular_additional_land_plots'] = array_values($data['type_particular_additional_land_plots']);
        }

        $data['type_flat_municipal_court'] = $builder->get('type_flat_municipal_court');
        $data['type_flat_land_registry'] = $builder->get('type_flat_land_registry');
        $data['type_flat_deposited_contracts_book_name'] = $builder->get('type_flat_deposited_contracts_book_name');
        $data['type_flat_subfolio_number'] = $builder->get('type_flat_subfolio_number');
        $data['type_flat_folio_number'] = $builder->get('type_flat_folio_number', false);
        $data['type_flat_possession_sheet_section_one_data'] = $builder->get('type_flat_possession_sheet_section_one_data');
        $data['type_flat_possession_sheet_section_two_data'] = $builder->get('type_flat_possession_sheet_section_two_data');

        $data['type_other_municipal_court'] = $builder->get('type_other_municipal_court');
        $data['type_other_land_registry'] = $builder->get('type_other_land_registry');
        $data['type_other_cadastral_municipality'] = $builder->get('type_other_cadastral_municipality');
        $data['type_other_land_registry_folio_number'] = $builder->get('type_other_land_registry_folio_number');
        $data['type_other_possession_number'] = $builder->get('type_other_possession_number');
        $data['type_other_possession_area'] = $builder->get('type_other_possession_area');
		$data["type_other_possession_area_type"] = $builder->get("type_other_possession_area_type", null);
        $data['type_other_possession_identification'] = $builder->get('type_other_possession_identification');

        $data["type_other_additional_possession_area_and_identification"] = $builder->get("type_other_additional_possession_area_and_identification", false);
        if(!empty($data['type_other_additional_possession_area_and_identification'])){
            $data['type_other_additional_possession_area_and_identification'] = array_values($data['type_other_additional_possession_area_and_identification']);
        }

        $data["type_other_additional_land_plots"] = $builder->get("type_other_additional_land_plots", false);
        if(!empty($data['type_other_additional_land_plots'])){
            $data['type_other_additional_land_plots'] = array_values($data['type_other_additional_land_plots']);

            foreach($data['type_other_additional_land_plots'] as &$_type_other_additional_land_plot) {
                if(!empty($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'])) {
                    $_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'] = array_values($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification']);
                }
            }

            unset($_type_other_additional_land_plot);
        }

        $data['grantee_has_full_realestate_ownership'] = $builder->get('grantee_has_full_realestate_ownership', false);
        $data['grantee_realestate_ownership_ratio_numerator'] = $builder->get('grantee_realestate_ownership_ratio_numerator', false);
        $data['grantee_realestate_ownership_ratio_denominator'] = $builder->get('grantee_realestate_ownership_ratio_denominator', false);

        $data['is_full_disposal'] = $builder->get('is_full_disposal', false);
        $data['partial_disposal_numerator'] = $builder->get('partial_disposal_numerator', false);
        $data['partial_disposal_denominator'] = $builder->get('partial_disposal_denominator', false);

		// authorities
		$data['grantee_authorities'] = $builder->get('grantee_authorities', false);

		if(!empty($data['grantee_authorities'])) {
			foreach($data['grantee_authorities'] as &$_grantee_authority) {
				if(!\Illuminate\Support\Str::contains($_grantee_authority, ['predmetne nekretnine'])) {
					if(empty($data['additional_realestates'])) {
						$_grantee_authority = preg_replace_callback(
                            '/\b(nekretninu|nekretnine|nekretnini)\b/',
                            function ($matches) {
                                $word = $matches[0];
                                if ($word === 'nekretninu') {
                                    return 'predmetnu nekretninu';
                                } elseif ($word === 'nekretnine') {
                                    return 'predmetne nekretnine';
                                } elseif ($word === 'nekretnini') {
                                    return 'predmetnoj nekretnini';
                                }
                            },
                            $_grantee_authority
                        );

					} else {
						$_grantee_authority = preg_replace_callback(
                            '/\b(nekretninu|nekretnine|nekretnini)\b/',
                            function ($matches) {
                                $word = $matches[0];
                                if ($word === 'nekretninu') {
                                    return 'predmetne nekretnine';
                                } elseif ($word === 'nekretnine') {
                                    return 'predmetnih nekretnina';
                                } elseif ($word === 'nekretnini') {
                                    return 'predmetnim nekretninama';
                                }
                            },
                            $_grantee_authority
                        );

					}
				} else {
					if(!empty($data['additional_realestates'])) {
						$_grantee_authority = \Illuminate\Support\Str::replace('predmetne nekretnine', 'predmetnih nekretnina', $_grantee_authority);
					}
				}
				unset($_grantee_authority);   // always unset when looping by reference!
			}
		}

		$data['consent_form_validity_period'] = $builder->get('consent_form_validity_period', false);
		$data['consent_form_validity_period_until_date'] = $builder->get('consent_form_validity_period_until_date');
		$data['consent_form_validity_period_custom'] = $builder->get('consent_form_validity_period_custom');

		// final provisions
        $data["consent_form_date"] = $builder->get("consent_form_date");
        $data["consent_form_place"] = $builder->get("consent_form_place");
        $data["custom_provisions"] = $builder->get("custom_provisions", false);

		// parties
		$data['parties'] = $builder->getParties()->where('side', 'left')->values()->toArray();

    @endphp

    @include($builder->render_view, ['builder' => $builder, 'data' => $data])

@endsection
