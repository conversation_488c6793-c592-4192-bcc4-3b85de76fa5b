<li>
    @if($is_full_disposal)
        @if($grantee_has_full_realestate_ownership)
            vlasnički dio 1/1
        @else
            suvlasnički dio {!! !empty($data['grantee_realestate_ownership_ratio_numerator']) ? $data['grantee_realestate_ownership_ratio_numerator'] : $builder::$placeholder !!}/{!! !empty($data['grantee_realestate_ownership_ratio_denominator']) ? $data['grantee_realestate_ownership_ratio_denominator'] : $builder::$placeholder !!}
        @endif
    @else
        suvlasnički dio
        @if($grantee_has_full_realestate_ownership)
            {!! !empty($data['partial_disposal_numerator']) ? $data['partial_disposal_numerator'] : $builder::$placeholder !!}/{!! !empty($data['partial_disposal_denominator']) ? $data['partial_disposal_denominator'] : $builder::$placeholder !!}
        @elseif(!empty($data['partial_disposal_numerator']) && !empty($data['partial_disposal_denominator']) && !empty($data['grantee_realestate_ownership_ratio_numerator']) && !empty($data['grantee_realestate_ownership_ratio_denominator']))
            {!! StringHelper::calculateOwnershipRatio($data['partial_disposal_numerator'], $data['partial_disposal_denominator'], $data['grantee_realestate_ownership_ratio_numerator'], $data['grantee_realestate_ownership_ratio_denominator'] ) !!}
        @else
            {!! $builder::$placeholder !!}/{!! $builder::$placeholder !!}
        @endif
    @endif

    @if($data['realestate_type'] == 'particular')

        nekretnine upisane u zemljišnoj knjizi koju vodi {!! $data['type_particular_municipal_court'] ?: $builder::$placeholder !!}, {!! $data['type_particular_land_registry'] ?: $builder::$placeholder !!},
        katastarska općina {!! $data['type_particular_cadastral_municipality'] ?: $builder::$placeholder !!}, u zemljišnoknjižni uložak {!! $data['type_particular_land_registry_folio_number'] ?: $builder::$placeholder !!},
        k. č. br. {!! $data['type_particular_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($data['type_particular_possession_area'], $data['type_particular_possession_area_type']) !!}, oznake zemljišta {!! $data['type_particular_possession_identification'] ?: $builder::$placeholder !!}@if(!empty($data['type_particular_additional_land_plots']) && count($data['type_particular_additional_land_plots']) == 1) i @else, @endif
        @if(!empty($data['type_particular_additional_land_plots']))
            @foreach($data['type_particular_additional_land_plots'] as $_alp_i => $_type_particular_additional_land_plot)
                k. č. br. {!! $_type_particular_additional_land_plot['type_particular_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($_type_particular_additional_land_plot['type_particular_possession_area'], $_type_particular_additional_land_plot['type_particular_possession_area_type'] ?? null) !!}, oznake zemljišta {!! $_type_particular_additional_land_plot['type_particular_possession_identification'] ?: $builder::$placeholder !!}@if($_alp_i == (count($data['type_particular_additional_land_plots'])-2)) i @else, @endif
            @endforeach
        @endif
        {!! $data['type_particular_ownership_sheet_data'] ?: $builder::$placeholder !!}

    @elseif($data['realestate_type'] == 'flat')

        nekretnine upisane u knjizi položenih ugovora {!! $data['type_flat_deposited_contracts_book_name'] ?: $builder::$placeholder !!} koju vodi
        {!! $data['type_flat_municipal_court'] ?: $builder::$placeholder !!}, {!! $data['type_flat_land_registry'] ?: $builder::$placeholder !!},
        poduložak {!! $data['type_flat_subfolio_number'] ?: $builder::$placeholder !!}, @if($data['type_flat_folio_number'])zemljišnoknjižni uložak {!! $data['type_flat_folio_number'] !!},@endif
        {!! $data['type_flat_possession_sheet_section_one_data'] ?: $builder::$placeholder !!},
        {!! $data['type_flat_possession_sheet_section_two_data'] ?: $builder::$placeholder !!}

    @elseif($data['realestate_type'] == 'other')

        @if(empty($data['type_other_additional_land_plots']))

            nekretnine upisane u zemljišnoj knjizi koju vodi {!! $data['type_other_municipal_court'] ?: $builder::$placeholder !!}, {!! $data['type_other_land_registry'] ?: $builder::$placeholder !!},
            katastarska općina {!! $data['type_other_cadastral_municipality'] ?: $builder::$placeholder !!}, u zemljišnoknjižni uložak {!! $data['type_other_land_registry_folio_number'] ?: $builder::$placeholder !!},
            k. č. br. {!! $data['type_other_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($data['type_other_possession_area'], $data['type_other_possession_area_type']) !!}, oznake zemljišta {!! $data['type_other_possession_identification'] ?: $builder::$placeholder !!}
            @if(!empty($data['type_other_additional_possession_area_and_identification']))
                (koju čine: @foreach($data['type_other_additional_possession_area_and_identification'] as $_i => $_additional) {!! $_additional['identification'] ?: $builder::$placeholder !!} površine {!! StringHelper::getPossessionAreaString($_additional['area'], $_additional['area_type'] ?? null) !!}@if($_i < (count($data['type_other_additional_possession_area_and_identification'])-1))@if(($_i+2) == count($data['type_other_additional_possession_area_and_identification'])) i @else, @endif @else{{ ')' }} @endif @endforeach
            @endif
        @else

            nekretnine upisane u zemljišnoj knjizi koju vodi {!! $data['type_other_municipal_court'] ?: $builder::$placeholder !!}, {!! $data['type_other_land_registry'] ?: $builder::$placeholder !!},
            katastarska općina {!! $data['type_other_cadastral_municipality'] ?: $builder::$placeholder !!}, u zemljišnoknjižni uložak {!! $data['type_other_land_registry_folio_number'] ?: $builder::$placeholder !!}, kako slijedi:
            <ul>
                <li>
                    k. č. br. {!! $data['type_other_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($data['type_other_possession_area'], $data['type_other_possession_area_type']) !!}, oznake zemljišta {!! $data['type_other_possession_identification'] ?: $builder::$placeholder !!}
                    @if(!empty($data['type_other_additional_possession_area_and_identification']))
                        {{ StringHelper::getAdditionalLandAreaAndIdentificationPrefix($data['type_other_additional_possession_area_and_identification']) }} @foreach($data['type_other_additional_possession_area_and_identification'] as $_i => $_additional) {!! $_additional['identification'] ?: $builder::$placeholder !!} površine {!! StringHelper::getPossessionAreaString($_additional['area'], $_additional['area_type'] ?? null) !!}@if($_i < (count($data['type_other_additional_possession_area_and_identification'])-1))@if(($_i+2) == count($data['type_other_additional_possession_area_and_identification'])) i @else, @endif @else{{ ')' }}@endif @endforeach
                    @endif
                </li>
                @foreach($data['type_other_additional_land_plots'] as $_type_other_additional_land_plot)
                    <li>
                        k. č. br. {!! $_type_other_additional_land_plot['type_other_possession_number'] ?: $builder::$placeholder !!}, površine {!! StringHelper::getPossessionAreaString($_type_other_additional_land_plot['type_other_possession_area'], $_type_other_additional_land_plot['type_other_possession_area_type'] ?? null) !!}, oznake zemljišta {!! $_type_other_additional_land_plot['type_other_possession_identification'] ?: $builder::$placeholder !!}
                        @if(!empty($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification']))
                            {{ StringHelper::getAdditionalLandAreaAndIdentificationPrefix($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification']) }} @foreach($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'] as $_i => $_additional) {!! $_additional['identification'] ?: $builder::$placeholder !!} površine {!! StringHelper::getPossessionAreaString($_additional['area'], $_additional['area_type'] ?? null) !!}@if($_i < (count($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'])-1))@if(($_i+2) == count($_type_other_additional_land_plot['type_other_additional_possession_area_and_identification'])) i @else, @endif @else{{ ')' }}@endif @endforeach
                        @endif
                    </li>
                @endforeach
            </ul>
        @endif

    @endif
</li>