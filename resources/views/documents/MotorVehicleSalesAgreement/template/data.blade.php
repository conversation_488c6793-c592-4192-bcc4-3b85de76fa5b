@extends($builder->getLayout())
@section('content')
@php

$data["seller_name"] = $builder->get("seller_name");
$data["seller_address"] = $builder->get("seller_address");
$data["seller_city"] = $builder->get("seller_city");
$data["seller_postal_code"] = $builder->get("seller_postal_code");
$data["seller_country"] = $builder->get("seller_country");
$data["seller_oib"] = $builder->get("seller_oib");

$data['seller_cosigner_exists'] = $builder->get('seller_cosigner_exists', false);
$data['any_seller_cosigner_exists'] = $data['seller_cosigner_exists'];

$seller_cosigner_type = $builder->get("seller_cosigner_type", false);

$data["seller_cosigner_type"] = "Bračni drug";
if($seller_cosigner_type == 1){
    $data["seller_cosigner_type"] = "Izvan<PERSON>čni drug";
}
elseif($seller_cosigner_type == 2){
    $data["seller_cosigner_type"] = "Životni partner";
}

$data["seller_cosigner_name"] = $builder->get("seller_cosigner_name");
$data["seller_cosigner_address"] = $builder->get("seller_cosigner_address");
$data["seller_cosigner_city"] = $builder->get("seller_cosigner_city");
$data["seller_cosigner_postal_code"] = $builder->get("seller_cosigner_postal_code");
$data["seller_cosigner_country"] = $builder->get("seller_cosigner_country");
$data["seller_cosigner_oib"] = $builder->get("seller_cosigner_oib");

$data["additional_sellers"] = $builder->get("additional_sellers", false);
$data["authorized_seller_persons"] = $builder->get("authorized_seller_persons", false);
$data["authorized_additional_seller_persons"] = $builder->get("authorized_additional_seller_persons", false);

// standardize array keys for parties
if(!empty($data['authorized_seller_persons'])){
    $data['authorized_seller_persons'] = array_values($data['authorized_seller_persons']);

    // default case
    foreach($data['authorized_seller_persons'] as &$_authorized_seller_person){
        if(empty($_authorized_seller_person['name'])) $_authorized_seller_person['name'] = $builder->get('placeholder');
        if(empty($_authorized_seller_person['role'])) $_authorized_seller_person['role'] = $builder->get('placeholder');

        $_authorized_seller_person['has_entered_address'] = !empty($_authorized_seller_person['address'])
        || !empty($_authorized_seller_person['city'])
        || !empty($_authorized_seller_person['postal_code']);
    }

	// always unset when looping by reference!
	unset($_authorized_seller_person);
}

$_new_i = 0;
if(!empty($data['additional_sellers'])){

    foreach($data['additional_sellers'] as $_i => $_additional_seller){

            if($_new_i != $_i){
                $data['additional_sellers'][$_new_i] =  $data['additional_sellers'][$_i];
                unset($data['additional_sellers'][$_i]);
            }

          if(!empty($_additional_seller['seller_cosigner_exists'])){

            $data['any_seller_cosigner_exists'] = true;

            $seller_cosigner_type = $_additional_seller['seller_cosigner_type'];

            $data['additional_sellers'][$_new_i]['seller_cosigner_type'] = "Bračni drug";

            if($seller_cosigner_type == 1){
                $data['additional_sellers'][$_new_i]['seller_cosigner_type'] = "Izvanbračni drug";
            }
            elseif($seller_cosigner_type == 2){
                $data['additional_sellers'][$_new_i]['seller_cosigner_type'] = "Životni partner";
            }

        }

        if(isset($data['authorized_additional_seller_persons'][$_i])){

            if($_new_i != $_i){
                $data['authorized_additional_seller_persons'][$_new_i] = $data['authorized_additional_seller_persons'][$_i];
                unset($data['authorized_additional_seller_persons'][$_i]);
            }

            $_new_j = 0;

            foreach($data['authorized_additional_seller_persons'][$_new_i] as $_j => $_person){

				$data['authorized_additional_seller_persons'][$_new_i][$_j]['has_entered_address'] = !empty($_person['address'])
                || !empty($_person['city'])
                || !empty($_person['postal_code']);

                if($_new_j != $_j){
                    $data['authorized_additional_seller_persons'][$_new_i][$_new_j] = $data['authorized_additional_seller_persons'][$_new_i][$_j];
                    unset($data['authorized_additional_seller_persons'][$_new_i][$_j]);
                }

                $_new_j++;
            }
        }
        $_new_i++;
    }
}

$data["buyer_name"] = $builder->get("buyer_name");
$data["buyer_address"] = $builder->get("buyer_address");
$data["buyer_city"] = $builder->get("buyer_city");
$data["buyer_postal_code"] = $builder->get("buyer_postal_code");
$data["buyer_country"] = $builder->get("buyer_country");
$data["buyer_oib"] = $builder->get("buyer_oib");

$data["authorized_buyer_persons"] = $builder->get("authorized_buyer_persons", false);
$data["additional_buyers"] = $builder->get("additional_buyers", false);
$data["authorized_additional_buyer_persons"] = $builder->get("authorized_additional_buyer_persons", false);

// standardize array keys for parties
if(!empty($data['authorized_buyer_persons'])){
    $data['authorized_buyer_persons'] = array_values($data['authorized_buyer_persons']);

    // default case
    foreach($data['authorized_buyer_persons'] as &$_authorized_buyer_person){
        if(empty($_authorized_buyer_person['name'])) $_authorized_buyer_person['name'] = $builder->get('placeholder');
        if(empty($_authorized_buyer_person['role'])) $_authorized_buyer_person['role'] = $builder->get('placeholder');

        $_authorized_buyer_person['has_entered_address'] = !empty($_authorized_buyer_person['address'])
        || !empty($_authorized_buyer_person['city'])
        || !empty($_authorized_buyer_person['postal_code']);
    }

	// always unset when looping by reference!
	unset($_authorized_buyer_person);
}

$_new_i = 0;
if(!empty($data['additional_buyers'])){

    foreach($data['additional_buyers'] as $_i => $_additional_buyer){

        if($_new_i != $_i){
            $data['additional_buyers'][$_new_i] =  $data['additional_buyers'][$_i];
            unset($data['additional_buyers'][$_i]);
        }

        if(isset($data['authorized_additional_buyer_persons'][$_i])){

            if($_new_i != $_i){
                $data['authorized_additional_buyer_persons'][$_new_i] = $data['authorized_additional_buyer_persons'][$_i];
                unset($data['authorized_additional_buyer_persons'][$_i]);
            }

            $_new_j = 0;

            foreach($data['authorized_additional_buyer_persons'][$_new_i] as $_j => $_person){

				$data['authorized_additional_buyer_persons'][$_new_i][$_j]['has_entered_address'] = !empty($_person['address'])
                || !empty($_person['city'])
                || !empty($_person['postal_code']);

                if($_new_j != $_j){
                    $data['authorized_additional_buyer_persons'][$_new_i][$_new_j] = $data['authorized_additional_buyer_persons'][$_new_i][$_j];
                    unset($data['authorized_additional_buyer_persons'][$_new_i][$_j]);
                }

                $_new_j++;
            }
        }
        $_new_i++;
    }
}



$data["registration"] = $builder->get("registration");
$data["vehicle_type"] = $builder->get("vehicle_type");
$data["brand"] = $builder->get("brand");
$data["model"] = $builder->get("model");
$data["model_2"] = $builder->get("model_2");
$data["color"] = $builder->get("color");
$data["vehicle_identification_number"] = $builder->get("vehicle_identification_number");
$data["body_type"] = $builder->get("body_type");
$data["manufactured_in_country"] = $builder->get("manufactured_in_country");
$data["manufactured_year"] = $builder->get("manufactured_year");
$data["manufacturer"] = $builder->get("manufacturer");
$data["first_registration_date"] = $builder->get("first_registration_date");
$data["main_purpose"] = $builder->get("main_purpose");
$data["engine_type"] = $builder->get("engine_type");
$data["engine_power"] = $builder->get("engine_power");
$data["engine_volume"] = $builder->get("engine_volume");
$data["total_price"] = $builder->get("total_price");
$data["price_payment_type"] = $builder->get("price_payment_type", false) ?: "in_full";
$data["price_payment_time"] = $builder->get("price_payment_time", false) ?: "when_contract_signed_date";
$data["price_payment_paid_on_date"] = $builder->get("price_payment_paid_on_date");
$data["price_payment_paid_by_latest_date"] = $builder->get("price_payment_paid_by_latest_date");
$data["price_payment_installments"] = $builder->get("price_payment_installments", false) ?: [];
$data["payment_method"] = $builder->get("payment_method", false) ?: "cash";
$data["payment_method_iban"] = $builder->get("payment_method_iban");
$data["ownership_transfer_expenses_person"] = $builder->get("ownership_transfer_expenses_person");
$data["seller_accepts_responsibility_for_defects"] = $builder->get("seller_accepts_responsibility_for_defects", false);
$data["ownership_transfer_time"] = $builder->get("ownership_transfer_time", false);
$data["ownership_transfer_time_custom"] = $builder->get("ownership_transfer_time_custom");
$data["contract_copy_count"] = $builder->get("contract_copy_count");
$data["seller_contract_copy_count"] = $builder->get("seller_contract_copy_count");
$data["buyer_contract_copy_count"] = $builder->get("buyer_contract_copy_count");
$data["contract_date"] = $builder->get("contract_date");
$data["contract_place"] = $builder->get("contract_place");
$data["custom_provisions"] = $builder->get("custom_provisions", false);


@endphp

@include($builder->render_view, ['builder' => $builder, 'data' => $data])

@endsection
