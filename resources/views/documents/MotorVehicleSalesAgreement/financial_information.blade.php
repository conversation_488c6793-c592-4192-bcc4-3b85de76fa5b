@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    <PERSON><PERSON><PERSON><PERSON>, tro<PERSON><PERSON>i i porezi
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fText($model, 'total_price', null, '1. Upiši ukupnu cijenu po kojoj prodavatelj prodaje, a kupac kupuje vozilo', ['placeholder' => 'Npr: 8.000,00', 'data-currency' => 'EUR'], '€') }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{ Form::fLabel($model, 'price_payment_type', '2. <PERSON><PERSON><PERSON><PERSON> li kupac prodavatelju ukupnu cijenu odjednom ili u dijelovima?') }}
                            {{ Form::fRadio($model, 'price_payment_type', 'in_full', 'Odjednom', ['checked' => !isset($model->price_payment_type), 'id' => 'price_payment_type_full_radio']) }}
                            {{ Form::fRadio($model, 'price_payment_type', 'installments', 'U dijelovima', ['id' => 'price_payment_type_installments_radio']) }}
                        </div>
                    </div>

                    <div class="dynamic" id="price_payment_full_container"
                         style="@if(isset($model->price_payment_type) && $model->price_payment_type == 'installments') display:none; @endif">
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fLabel($model, 'price_payment_time', '<span class="dot"></span> Kada će kupac prodavatelju platiti ukupnu cijenu?') }}
                                {{ Form::fRadio($model, 'price_payment_time', 'when_contract_signed_date', 'Kupac će prodavatelju platiti ukupnu cijenu u trenutku potpisa ovog ugovora ', ['checked' => !isset($model->price_payment_time)]) }}
                                <div class="form-check mb-1 mt-1 radio-input">
                                    <label class="form-check-label" style="width:100%;">
                                        {{ Form::radio('price_payment_time', 'paid_on_date', null, ['class' => 'form-check-input']) }}
                                        Kupac je prodavatelju već platio ukupnu cijenu dana
                                        <br/>
                                        {{ Form::fText($model, 'price_payment_paid_on_date', null, null, ['class' => 'form-control', 'placeholder' => 'Npr: 15. 12. 2018.', 'data-datepicker' => 1, 'autocomplete' => 'off']) }}
                                    </label>
                                </div>
                                <div class="form-check radio-input">
                                    <label class="form-check-label" style="width:100%;">
                                        {{ Form::radio('price_payment_time', 'paid_by_latest_date', null, ['class' => 'form-check-input']) }}
                                        Kupac će prodavatelju platiti ukupnu cijenu najkasnije dana
                                        <br/>
                                        {{ Form::fText($model, 'price_payment_paid_by_latest_date', null, null, ['class' => 'form-control', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dynamic" id="price_payment_installments_container"
                         style="@if(!isset($model->price_payment_type) || $model->price_payment_type == 'in_full') display:none; @endif">

                        <hr/>

                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fDropdown($model, 'price_payment_installments_number', null, array_combine(range(2, 10), range(2, 10)), '<span class="dot"></span> U koliko dijelova će kupac prodavatelju platiti ukupnu cijenu?', ['id' => 'price_payment_installments_number']) }}
                            </div>
                        </div>

                        <div id="price_installment_template" style="display:none;">
                            <div class="card mb-4">
                                <div class="card-header">
                                    {INDEX}. dio cijene
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="form-group col-lg-12">
                                            {{ Form::fText($model, 'price_payment_installments[{KEY}][amount]', null, 'Iznos', ['placeholder' => 'Npr: 3.000,00', 'data-currency' => 'EUR'], '€') }}
                                        </div>
                                        <div class="form-group col-lg-12">
                                            <hr/>
                                            <label>Kada će kupac prodavatelju {INDEX}. dio cijene?
                                            </label>
                                            {{ Form::fRadio($model, 'price_payment_installments[{KEY}][time]', 'when_contract_signed_date', 'Kupac će prodavatelju platiti {INDEX}. dio cijene u trenutku potpisa ovog ugovora ', ['checked' => true]) }}
                                            <div class="form-check mb-1 mt-1 radio-input">
                                                <label class="form-check-label" style="width:100%;">
                                                    {{ Form::radio('price_payment_installments[{KEY}][time]', 'paid_on_date', null, ['class' => 'form-check-input']) }}
                                                    Kupac je prodavatelju već platio {INDEX}. dio cijene dana
                                                    <br/>
                                                    {{ Form::fText($model, 'price_payment_installments[{KEY}][paid_on_date]', null, null, ['class' => 'form-control', 'placeholder' => 'Npr: 15. 12. 2018.', 'data-datepicker' => 1, 'autocomplete' => 'off']) }}
                                                </label>
                                            </div>
                                            <div class="form-check radio-input">
                                                <label class="form-check-label" style="width:100%;">
                                                    {{ Form::radio('price_payment_installments[{KEY}][time]', 'paid_by_latest_date', null, ['class' => 'form-check-input']) }}
                                                    Kupac će prodavatelju platiti {INDEX}. dio cijene najkasnije dana
                                                    <br/>
                                                    {{ Form::fText($model, 'price_payment_installments[{KEY}][paid_by_latest_date]', null, null, ['class' => 'form-control', 'autocomplete' => 'off', 'data-datepicker' => 1,  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="price_payment_installments">
                            @if(empty($model->price_payment_installments))
                                <div class="card mb-4">
                                    <div class="card-header">
                                        1. dio cijene
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fText($model, 'price_payment_installments[0][amount]', null, 'Iznos', ['placeholder' => 'Npr: 3.000,00', 'data-currency' => 'EUR'], '€') }}
                                            </div>
                                            <div class="form-group col-lg-12">
                                                <hr/>
                                                <label>Kada će kupac prodavatelju platiti 1. dio cijene?
                                                </label>
                                                {{ Form::fRadio($model, 'price_payment_installments[0][time]', 'when_contract_signed_date', 'Kupac će prodavatelju platiti 1. dio cijene u trenutku potpisa ovog ugovora ', ['checked' => true]) }}
                                                <div class="form-check mb-1 mt-1 radio-input">
                                                    <label class="form-check-label" style="width:100%;">
                                                        {{ Form::radio('price_payment_installments[0][time]', 'paid_on_date', null, ['class' => 'form-check-input']) }}
                                                        Kupac je prodavatelju već platio 1. dio cijene dana
                                                        <br/>
                                                        {{ Form::fText($model, 'price_payment_installments[0][paid_on_date]', null, null, ['class' => 'form-control', 'placeholder' => 'Npr: 15. 12. 2018.', 'data-datepicker' => 1, 'autocomplete' => 'off']) }}
                                                    </label>
                                                </div>
                                                <div class="form-check radio-input">
                                                    <label class="form-check-label" style="width:100%;">
                                                        {{ Form::radio('price_payment_installments[0][time]', 'paid_by_latest_date', null, ['class' => 'form-check-input']) }}
                                                        Kupac će prodavatelju platiti 1. dio cijene najkasnije dana
                                                        <br/>
                                                        {{ Form::fText($model, 'price_payment_installments[0][paid_by_latest_date]', null, null, ['class' => 'form-control', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mb-4">
                                    <div class="card-header">
                                        2. dio cijene
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fText($model, 'price_payment_installments[1][amount]', null, 'Iznos', ['placeholder' => 'Npr: 3.000,00', 'data-currency' => 'EUR'], '€') }}
                                            </div>
                                            <div class="form-group col-lg-12">
                                                <hr/>
                                                <label>Kada će kupac prodavatelju platiti 2. dio cijene?
                                                </label>
                                                {{ Form::fRadio($model, 'price_payment_installments[1][time]', 'when_contract_signed_date', 'Kupac će prodavatelju platiti 2. dio cijene u trenutku potpisa ovog ugovora ', ['checked' => true]) }}
                                                <div class="form-check mb-1 mt-1 radio-input">
                                                    <label class="form-check-label" style="width:100%;">
                                                        {{ Form::radio('price_payment_installments[1][time]', 'paid_on_date', null, ['class' => 'form-check-input']) }}
                                                        Kupac je prodavatelju već platio 2. dio cijene dana
                                                        <br/>
                                                        {{ Form::fText($model, 'price_payment_installments[1][paid_on_date]', null, null, ['class' => 'form-control', 'placeholder' => 'Npr: 15. 12. 2018.', 'data-datepicker' => 1, 'autocomplete' => 'off']) }}
                                                    </label>
                                                </div>
                                                <div class="form-check radio-input">
                                                    <label class="form-check-label" style="width:100%;">
                                                        {{ Form::radio('price_payment_installments[1][time]', 'paid_by_latest_date', null, ['class' => 'form-check-input']) }}
                                                        Kupac će prodavatelju platiti 2. dio cijene najkasnije dana
                                                        <br/>
                                                        {{ Form::fText($model, 'price_payment_installments[1][paid_by_latest_date]', null, null, ['class' => 'form-control', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @else
                                @foreach($model->price_payment_installments as $_key => $_installment)
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            {{$_key+1}}. dio cijene
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fText($model, "price_payment_installments[$_key][amount]", null, 'Iznos', ['placeholder' => 'Npr: 3.000,00', 'data-currency' => 'EUR'], '€') }}
                                                </div>
                                                <div class="form-group col-lg-12">
                                                    <hr/>
                                                    <label>Kada će kupac prodavatelju platiti {{$_key+1}}. dio cijene?
                                                    </label>
                                                    {{ Form::fRadio($model, "price_payment_installments[$_key][time]", 'when_contract_signed_date', 'Kupac će prodavatelju platiti 2. dio cijene u trenutku potpisa ovog ugovora ', ['checked' => true]) }}
                                                    <div class="form-check mb-1 mt-1 radio-input">
                                                        <label class="form-check-label" style="width:100%;">
                                                            {{ Form::radio("price_payment_installments[$_key][time]", 'paid_on_date', null, ['class' => 'form-check-input']) }}
                                                            Kupac je prodavatelju već platio {{$_key+1}}. dio cijene dana
                                                            <br/>
                                                            {{ Form::fText($model, "price_payment_installments[$_key][paid_on_date]", null, null, ['class' => 'form-control', 'placeholder' => 'Npr: 15. 12. 2018.', 'data-datepicker' => 1, 'autocomplete' => 'off']) }}
                                                        </label>
                                                    </div>
                                                    <div class="form-check radio-input">
                                                        <label class="form-check-label" style="width:100%;">
                                                            {{ Form::radio("price_payment_installments[$_key][time]", 'paid_by_latest_date', null, ['class' => 'form-check-input']) }}
                                                            Kupac će prodavatelju platiti {{$_key+1}}. dio cijene najkasnije dana
                                                            <br/>
                                                            {{ Form::fText($model, "price_payment_installments[$_key][paid_by_latest_date]", null, null, ['class' => 'form-control', 'data-datepicker' => 1, 'autocomplete' => 'off',  'placeholder' => 'Npr: '.\Carbon\Carbon::now()->addDays(5)->format('j. n. Y.')]) }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                        </div>

                    </div>

                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            <label>3. Na koji način će kupac prodavatelju izvršiti plaćanje cijene?
                            </label>
                            {{ Form::fRadio($model, 'payment_method', 'cash', 'U gotovini', ['checked' => !isset($model->payment_method)]) }}
                            <div class="form-check radio-input">
                                <label class="form-check-label" style="width:100%;">
                                    {{ Form::radio("payment_method", 'iban', null, ['class' => 'form-check-input']) }}
                                    Na IBAN
                                    <br/>
                                    {{ Form::fText($model, "payment_method_iban", null, null, ['class' => 'form-control', 'placeholder' => 'Npr: ***************** ']) }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-12">
                            <label>4. Tko snosi poreze, pristojbe i ostale troškove vezane uz ovaj ugovor i prijepis motornog vozila s prodavatelja na kupca?
                            </label>
                            {{ Form::fRadio($model, 'ownership_transfer_expenses_person', 'buyer', 'Kupac', ['checked' => !isset($model->ownership_transfer_expenses_person)]) }}
                            {{ Form::fRadio($model, 'ownership_transfer_expenses_person', 'seller', 'Prodavatelj') }}
                        </div>
                    </div>


                </div>
            </div>

        </div>

    </div>


    {{ Form::close() }}
@endsection
