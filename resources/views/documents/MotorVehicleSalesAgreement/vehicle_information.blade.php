@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o motornom vozilu
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'registration', null, 'Registracijska oznaka vozila', ['placeholder' => 'Npr: ZG 543-AA', 'oninput' => 'this.value = this.value.toUpperCase()'])  }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'vehicle_type', null, 'Vrsta vozila', ['placeholder' => 'Npr: osobni automobil'])  }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'brand', null, 'Marka vozila', ['placeholder' => 'Npr: Peugeot'])  }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'model', null, 'Tip vozila', ['placeholder' => 'Npr: 308'])  }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'model_2', null, 'Model vozila', ['placeholder' => 'Npr: 1.6 HDI-SPORTIUM'])  }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'color', null, 'Boja vozila', ['placeholder' => 'Npr: crna - s efektom'])  }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'vehicle_identification_number', null, 'Broj šasije', ['placeholder' => 'Npr: VF11C9HP0CS011111', 'oninput' => 'this.value = this.value.toUpperCase()'])  }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'body_type', null, 'Oblik karoserije', ['placeholder' => 'Npr: zatvoreni'])  }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'manufactured_in_country', null, 'Država proizvodnje', ['placeholder' => 'Npr: Francuska'])  }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'manufacturer', null, 'Proizvođač', ['placeholder' => 'Npr: Peugeot'])  }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{ Form::fDropdown($model, "manufactured_year", null, array_combine(range(date("Y"), 1900), range(date("Y"), 1900)), 'Godina proizvodnje', ['placeholder' => 'Odaberi...']) }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'first_registration_date', null, 'Datum prve registracije', ['data-datepicker' => 1, 'autocomplete' => 'off', 'placeholder' => 'Npr: 28. 12. 2012.'])  }}
                        </div>
                    </div>
                    <hr/>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'main_purpose', null, 'Osnovna namjena', ['placeholder' => 'Npr: za osobne potrebe'])  }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'engine_type', null, 'Vrsta motora', ['placeholder' => 'Npr: diesel'])  }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'engine_power', null, 'Snaga motora u kW', ['placeholder' => 'Npr: 68'], 'kW')  }}
                        </div>
                        <div class="form-group col-lg-6">
                            {{  Form::fText($model, 'engine_volume', null, 'Radni obujam motora u cm3', ['placeholder' => 'Npr: 1.560'], 'cm3')  }}
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>


    {{ Form::close() }}
@endsection
