@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o naručitelju
                </div>
                <div class="card-body" id="client_content">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'client_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: ACME d.o.o.']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'client_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'client_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'client_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'client_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'client_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: ***********']) }}
                            </div>
                        </div>
                        <hr/>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fLabel($model, 'client_is_business', '<span class="dot"></span> Je li naručitelj poslovni subjekt?', 'Označi "Ne" samo ako je naručitelj fizička osoba i to fizička osoba koja ne obavlja djelatnost kroz obrt ili slobodno zanimanje. U svim ostalim slučajevima (npr. ako je naručitelj trgovačko društvo, udruga, državno tijelo, obrt, fizička osoba koja obavlja slobodno zanimanje, itd.) označi "Da".') }}
                                {{ Form::fRadio($model, 'client_is_business', 1, 'Da', ['checked' => !isset($model->client_is_business)]) }}
                                {{ Form::fRadio($model, 'client_is_business', 0, 'Ne')}}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun naručitelja', ['id' => 'authorized_client_persons_exist', 'checked' => !$model->is_edit || !empty($model->authorized_client_persons)], 'Ovu opciju označi ako je naručitelj pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun naručitelja u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je naručitelj fizička osoba (obrt, samostalna djelatnost, itd.), ali je naručitelj ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                            </div>
                        </div>

                    </div>

                    <div id="authorized_client_persons_container"
                         class="dynamic {{ $model->is_edit && empty($model->authorized_client_persons) ? 'd-none' : '' }}">
                        <div id="authorized_client_persons">
                            @if(!empty($model->authorized_client_persons))
                                @foreach($model->authorized_client_persons as $i => $_authorized_client_person)
                                    <div class="authorized_client_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_client_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[$i][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa naručitelja') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[$i][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[$i][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[$i][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[$i][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_client_persons[$i][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: ***********']) }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                            @if(empty($model->authorized_client_persons))
                                    <div class="authorized_client_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_client_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[0][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa naručitelja') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[0][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[0][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[0][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[0][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_client_persons[0][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">

                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_client_persons[0][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: ***********']) }}
                                            </div>
                                        </div>
                                    </div>
                            @endif
                            <div id="authorized_client_person_template" class="d-none">
                                <div class="authorized_client_person_content maps_autofill_container">
                                    <hr/>
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <strong><a class="btn btn-danger btn-sm float-right remove_authorized_client_person"><i
                                                            class="fa fa-trash"></i> Ukloni</a></strong>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_client_persons[{INDEX}][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa naručitelja') }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_client_persons[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_client_persons[{INDEX}][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_client_persons[{INDEX}][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_client_persons[{INDEX}][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_client_persons[{INDEX}][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "authorized_client_persons[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: ***********']) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <a class="btn btn-info" id="add_authorized_client_person">+ Dodaj zastupnika</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4" id="author_content">
                <div class="card-header">
                    Upiši podatke o autoru
                    <span data-toggle="tooltip"
                          data-original-title="Prema Zakonu o autorskom pravu i srodnim pravima, autor je fizička osoba koja je stvorila autorsko djelo.">
                        <i class="fa fa-info-circle"></i>
                    </span>
                </div>
                <div class="card-body">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'author_name', null, 'Ime i prezime', ['placeholder' => 'Npr: Stanko Zorić']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'author_address', null, 'Adresa',
                                ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'author_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'author_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'author_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'author_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: ***********']) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>


    </div>

    {{ Form::close() }}
@endsection
