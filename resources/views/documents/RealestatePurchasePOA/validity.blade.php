@extends('layouts.document.master')
@section('content')
    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class='row'>
        <div class='col'>
            <div class='card mb-4'>
                <div class='card-header'>
                    Va<PERSON><PERSON>je punomoći
                </div>
                <div class='card-body'>
                    <div class='row'>
                        <div class='form-group col-lg-12'>
                            {{ Form::fLabel($model, 'poa_validity_period', '1. Na koji rok se daje punomoć?', 'Prema članku 316. stavku 1. Zakona o obveznim odnosima, opunomoćitelj može po svojoj volji suziti ili opozvati punomoć, iako se ugovorom odrekao toga prava. Prema članku 318. Zakona o obveznim odnosima, punomoć prestaje prestankom pravne osobe kao opunomoćenika ako zakonom nije drukčije određeno; smrću opunomoćenika; i prestankom pravne osobe, odnosno smrću osobe koja ju je dala, osim ako se započeti posao ne može prekinuti bez štete za pravne sljednike ili ako punomoć vrijedi i za slučaj smrti davatelja bilo po njegovoj volji, bilo s obzirom na pravnu narav posla.') }}
                            {{ Form::fRadio($model, 'poa_validity_period', 'until_revoked', 'Do opoziva', ['checked' => !isset($model->poa_validity_period) || $model->poa_validity_period == 'until_revoked'] ) }}
                            {{ Form::fRadio($model, 'poa_validity_period', 'until_end', 'Do okončanja postupaka poduzetih na temelju ovlaštenja navedenih u punomoći') }}

                            <div class="form-check mt-2">
                                <label class="form-check-label full-width">
                                    {{ Form::radio('poa_validity_period', 'until_date', null, ['class' => 'form-check-input', 'id' => 'poa_validity_period_until_date_radio'])  }}
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Do dana</span>
                                        </div>
                                        {{
	                                        Form::text(
                                            'poa_validity_period_until_date',
                                            null,
                                            [
                                                'class' => 'radio_input_text form-control',
                                                'data-datepicker' => 1,
                                                'autocomplete' => 'off',
                                                'id' => 'poa_validity_period_until_date',
                                                'placeholder' => 'Npr: 31. 12. '.date('Y').'.'
                                            ])
                                        }}
                                    </div>
                                </label>
                            </div>

                            <div class="form-check mt-2">
                                <label class="form-check-label full-width">
                                    {{ Form::radio('poa_validity_period', 'custom', null, ['class' => 'form-check-input', 'id' => 'poa_validity_period_custom_radio'])  }}
                                    {{
                                        Form::text(
                                        'poa_validity_period_custom',
                                        null,
                                        [
                                            'class' => 'form-control radio_input_text',
                                            'id' => 'poa_validity_period_custom',
                                            'placeholder' => 'Npr: dvije godine od dana izdavanja'
                                        ])
                                    }}
                                </label>
                            </div>
						</div>
					</div>
				</div>
            </div>

        </div>

    </div>
    {{ Form::close() }}
@endsection
