@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}

    <div class="row main-container">
        <div class="col">

            @include('documents.RealestatePurchasePOA.partials.type_other_additional_possession_area_and_identification_template')
            @include('documents.RealestatePurchasePOA.partials.type_particular_additional_land_plot_template')
            @include('documents.RealestatePurchasePOA.partials.type_other_additional_land_plot_template')
            @include('documents.RealestatePurchasePOA.partials.realestate_template')

            <div class="card mb-4">
                <div class="card-header">
                    Je li nekretnina koja je predmet kupoprodaje već odabrana?
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-lg-12">
                            {{  Form::fRadio($model, 'realestate_is_chosen', 1, 'Nekretnina je već odabrana', ['id' => 'realestate_is_chosen', 'checked' => !isset($model->realestate_is_chosen)])  }}
                            {{  Form::fRadio($model, 'realestate_is_chosen', 0, 'Nekretnina još nije odabrana', ['id' => 'realestate_is_not_chosen'])  }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="realestate_content">
                    <div class="card-header">
                        Nekretnina <span class="realestate_index">@if(!empty($model->additional_realestates))
                                1 @endif</span>
                    </div>
                    <div class="card-body">
                        <div class="realestate_is_chosen_container" style="@if(isset($model->realestate_is_chosen) && !$model->realestate_is_chosen)display: none;@endif">

                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{  Form::fLabel($model, 'realestate_type', '1. Koja vrsta nekretnine je predmet kupoprodaje?')  }}
                                    {{  Form::fRadio($model, 'realestate_type', 'particular', 'Posebni dio nekretnine (etažno vlasništvo) upisan u glavnu zemljišnu knjigu (npr. stan, poslovni prostor, parkirno mjesto i slično)', ['class' => 'form-check-input realestate_type_particular', 'checked' => !isset($model->realestate_type)])  }}
                                    {{  Form::fRadio($model, 'realestate_type', 'flat', 'Stan upisan u knjigu položenih ugovora', ['class' => 'form-check-input realestate_type_flat'])  }}
                                    {{  Form::fRadio($model, 'realestate_type', 'other', 'Druga vrsta nekretnine upisana u glavnu zemljišnu knjigu (npr. zgrada, kuća, livada, šuma i slično)', ['class' => 'form-check-input realestate_type_other'])  }}
                                </div>
                            </div>

                            <div class="realestate_type_particular_container"
                                 style="@if(isset($model->realestate_type) && $model->realestate_type != 'particular') display:none; @endif">
                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fLabel($model, 'type_particular_municipal_court', '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi zemljišnu knjigu u koju je upisana nekretnina', 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/2.jpeg') }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fDropdown($model, 'type_particular_municipal_court', null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fDropdown($model, 'type_particular_land_registry', null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'] + (!empty($model->type_particular_land_registry) ? [$model->type_particular_land_registry => $model->type_particular_land_registry] : []), 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                                    </div>
                                </div>
                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fText($model, 'type_particular_cadastral_municipality', null, '3. Upiši oznaku i naziv katastarske općine u kojoj se nekretnina nalazi', ['placeholder' => 'Npr: 999901, GRAD ZAGREB'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/3.jpeg') }}
                                    </div>
                                </div>
                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fText($model, 'type_particular_land_registry_folio_number', null, '4. Upiši broj zemljišnoknjižnog uloška u koji su upisani podaci vezani uz nekretninu', ['placeholder' => 'Npr: 12345'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/4.jpeg') }}
                                    </div>
                                </div>

                                <hr/>
                                <div class="mb-3">
                                    <div class="card">
                                        <div class="card-header">
                                            Katastarska čestica
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fLabel($model, 'type_particular_possession_sheet_data', 'Upiši podatke iz posjedovnice (lista A) koji se odnose na katastarsku česticu odnosno zemljište') }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, 'type_particular_possession_number', null, 'Broj zemljišta/katastarske čestice', ['placeholder' => 'Npr: 1234/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5A.jpeg') }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fDropdownText($model ,'Površina zemljišta', 'type_particular_possession_area', null, 'type_particular_possession_area_type', null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5C.jpeg', 'Pazi da ovdje ne upišeš površinu posebnog dijela nekretnine, nego površinu cijele katastarske čestice! Primjerice, ako se radi o kupnji stana u etažiranoj stambenoj zgradi, ovdje ne upisuješ površinu stana, nego površinu cjelokupne katastarske čestice na kojoj je etažirana stambena zgrada izgrađena.') }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    {{ Form::fTextArea($model, 'type_particular_possession_identification', null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5B.jpeg') }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="type_particular_additional_land_plot_container">
                                    @if(!empty($model->type_particular_additional_land_plots))
                                        @foreach($model->type_particular_additional_land_plots as $_lp_i => $_land_plot)
                                            <div class="type_particular_additional_land_plot_content">

                                                <div class="mb-3">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            Katastarska čestica
                                                            <strong><a class="btn btn-danger btn-sm float-right remove_type_particular_additional_land_plot"><i class="fa fa-trash"></i> Ukloni</a></strong>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="form-group col-lg-12">
                                                                    {{ Form::fLabel($model, "type_particular_additional_land_plots[$_lp_i][type_particular_possession_sheet_data]", 'Upiši podatke iz posjedovnice (lista A) koji se odnose na katastarsku česticu odnosno zemljište') }}
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="form-group col-lg-6">
                                                                    {{ Form::fText($model, "type_particular_additional_land_plots[$_lp_i][type_particular_possession_number]", null, 'Broj zemljišta/katastarske čestice', ['placeholder' => 'Npr: 1234/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5A.jpeg') }}
                                                                </div>
                                                                <div class="form-group col-lg-6">
                                                                    {{ Form::fDropdownText($model ,'Površina zemljišta', "type_particular_additional_land_plots[$_lp_i][type_particular_possession_area]", null, "type_particular_additional_land_plots[$_lp_i][type_particular_possession_area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5C.jpeg', 'Pazi da ovdje ne upišeš površinu posebnog dijela nekretnine, nego površinu cijele katastarske čestice! Primjerice, ako se radi o kupnji stana u etažiranoj stambenoj zgradi, ovdje ne upisuješ površinu stana, nego površinu cjelokupne katastarske čestice na kojoj je etažirana stambena zgrada izgrađena.') }}
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    {{ Form::fTextArea($model, "type_particular_additional_land_plots[$_lp_i][type_particular_possession_identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5B.jpeg') }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        @endforeach
                                    @endif
                                </div>

                                <a data-field-name="type_particular_additional_land_plots" class="btn btn-info btn-block add_type_particular_additional_land_plot">Dodaj katastarsku česticu</a>
                                <div class="button-helper text-center">
                                    <a href="/documents/RealestatePurchasePOA/tooltips/other/help-2.jpeg" data-caption='Ponekad je u istom zemljišnoknjižnom ulošku upisano više zemljišta odnosno katastarskih čestica. Ako je više katastarskih čestica iz istog zemljišnoknjižnog uloška predmet punomoći, za upis tih katastarskih čestica u ugovor klikni na "Dodaj katastarsku česticu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                </div>

                                <hr/>
                                <div class="row">
                                    <div class="col-lg-12 form-group">
                                        {{ Form::fTextArea($model, 'type_particular_ownership_sheet_data', null, '5. Upiši podatke iz vlastovnice (lista B) koji se odnose na posebni dio nekretnine (etažno vlasništvo)', ['placeholder' => 'Npr: 11. Suvlasnički dio: 111/11111 ETAŽNO VLASNIŠTVO (E-11), stan broj 11 - STAMBENA JEDINICA "D" (u elaboratu označen ružičastom bojom) - trosobni stan u prizemlju (sa stubišta ulaz desno) ukupne neto korisne površine 111,11 čm'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/6.jpeg', 'Ovdje upisuješ samo oznaku i opis posebnog dijela nekretnine (etaže) iz izvatka iz zemljišne knjige! Pazi da ovdje ne upišeš podatke o vlasniku (ili suvlasnicima) posebnog dijela nekretnine. Za upis podataka o vlasniku (ili suvlasnicima) predviđena su posebna polja na kraju ovog koraka upitnika.') }}
                                    </div>
                                </div>
                            </div>

                            <div class="realestate_type_flat_container" style="@if(!isset($model->realestate_type) || $model->realestate_type != 'flat') display:none; @endif">
                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fLabel($model, 'type_flat_municipal_court', '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi knjigu položenih ugovora u koju je upisan stan', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/2.jpeg') }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fDropdown($model, 'type_flat_municipal_court', null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fDropdown($model, 'type_flat_land_registry', null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'] + (!empty($model->type_flat_land_registry) ? [$model->type_flat_land_registry => $model->type_flat_land_registry] : []), 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                                    </div>
                                </div>

                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fText($model, 'type_flat_deposited_contracts_book_name', null, '3. Upiši naziv knjige položenih ugovora u kojoj je upisan stan', ['placeholder' => 'Npr: Vrapče staro'], 'Knjiga PU', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/3.jpeg') }}
                                    </div>
                                </div>

                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fLabel($model, 'type_flat_folio_number', '4. Upiši broj poduloška i zk uloška u koji su upisani podaci vezani uz stan', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/4.jpeg') }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'type_flat_subfolio_number', null, 'Broj poduloška', ['placeholder' => 'Npr: 11']) }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'type_flat_folio_number', null, 'Broj zk uloška', ['placeholder' => 'Npr: 12345']) }}
                                    </div>
                                </div>


                                <hr/>
                                <div class="row">
                                    <div class="col-lg-12">
                                        {{ Form::fTextArea($model, 'type_flat_possession_sheet_section_one_data', null, '5. Upiši podatke iz prvog odjeljka posjedovnice (lista A) koji se odnose stambenu zgradu u kojoj se nalazi stan', ['placeholder' => 'Npr: Stambena zgrada Riječka ulica 1, Rijeka, sagrađena na čest. br. 1111/11, po novoj izmjeri čest. br. 1234/1 k.o. PLASE'], 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/5.jpeg') }}
                                    </div>
                                </div>

                                <hr/>
                                <div class="row">
                                    <div class="col-lg-12">
                                        {{ Form::fTextArea($model, 'type_flat_possession_sheet_section_two_data', null, '6. Upiši podatke iz drugog odjeljka posjedovnice (lista A) koji se odnose na stan', ['placeholder' => 'Npr: stan na II (drugom) katu desno, koji se sastoji od dvije sobe i sporednih prostorija u površini od 61,11'], 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/6.jpeg') }}
                                    </div>
                                </div>
                            </div>

                            <div class="realestate_type_other_container" style="@if(!isset($model->realestate_type) || $model->realestate_type != 'other') display:none; @endif">
                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fLabel($model, 'type_other_municipal_court', '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi zemljišnu knjigu u koju je upisana nekretnina', 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/2.jpeg') }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        {{ Form::fDropdown($model, 'type_other_municipal_court', null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fDropdown($model, 'type_other_land_registry', null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'] + (!empty($model->type_other_land_registry) ? [$model->type_other_land_registry => $model->type_other_land_registry] : []), 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                                    </div>
                                </div>
                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fText($model, 'type_other_cadastral_municipality', null, '3. Upiši oznaku i naziv katastarske općine u kojoj se nekretnina nalazi', ['placeholder' => 'Npr: 999901, GRAD ZAGREB'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/3.jpeg') }}
                                    </div>
                                </div>
                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fText($model, 'type_other_land_registry_folio_number', null, '4. Upiši broj zemljišnoknjižnog uloška u koji su upisani podaci vezani uz nekretninu', ['placeholder' => 'Npr: 12345'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/4.jpeg') }}
                                    </div>
                                </div>


                                <hr/>

                                <div class="mb-3">
                                    <div class="card">
                                        <div class="card-header">
                                            Katastarska čestica
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fText($model, 'type_other_possession_number', null, 'Upiši podatke iz posjedovnice (lista A) koji se odnose na broj katastarske čestice odnosno zemljište', ['placeholder' => 'Npr: 1111/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5A.jpeg') }}
                                                </div>
                                            </div>
                                            <hr/>
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    {{ Form::fDropdownText($model ,'Površina zemljišta', "type_other_possession_area", null, "type_other_possession_area_type", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5C.jpeg') }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    {{ Form::fTextArea($model, 'type_other_possession_identification', null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5B.jpeg') }}
                                                </div>
                                            </div>

                                            <div class="type_other_additional_possession_area_and_identification_container">
                                                @if(!empty($model->type_other_additional_possession_area_and_identification))
                                                    @foreach($model->type_other_additional_possession_area_and_identification as $_additional_index => $_additional)
                                                        <div class="type_other_additional_possession_area_and_identification_content">
                                                            <hr/>
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    <strong><a class="btn btn-danger btn-sm float-right remove_type_other_additional_possession_area_and_identification"><i class="fa fa-trash"></i> Ukloni</a></strong>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="form-group col-lg-12">
                                                                    {{ Form::fDropdownText($model ,'Površina zemljišta', "type_other_additional_possession_area_and_identification[$_additional_index][area]", null, "type_other_additional_possession_area_and_identification[$_additional_index][area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5C.jpeg') }}
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    {{ Form::fTextArea($model, "type_other_additional_possession_area_and_identification[$_additional_index][identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5B.jpeg') }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                @endif
                                            </div>

                                            <hr/>
                                            <div class="row mt-3">
                                                <div class="col-lg-12">
                                                    <a data-field-name="type_other_additional_possession_area_and_identification"  class="btn btn-default add_type_other_additional_possession_area_and_identification">+ Dodaj oznaku i površinu </a>

                                                    <div class="button-helper">
                                                        <a href="/documents/RealestatePurchasePOA/tooltips/other/help-1.jpeg" data-caption='Ponekad su u izvatku iz zemljišne knjige ispod retka koji se odnosi na oznaku cijelog zemljišta i njegovu površinu (obično prvi redak stupaca "Oznaka zemljišta" i "Površina" ispisan masnim slovima), upisani i dodatni retci u kojima se upisuju oznake sastavnih dijelova cijelog zemljišta i upisuje površina tih sastavnih dijelova. Za upis tih dodatnih redaka u ugovor, klikni na "Dodaj oznaku i površinu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                                    </div>

                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                                <div class="type_other_additional_land_plot_container">
                                    @if(!empty($model->type_other_additional_land_plots))
                                        @foreach($model->type_other_additional_land_plots as $_lp_i => $_land_plot)
                                            <div class="type_other_additional_land_plot_content">

                                                <div class="mb-3">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            Katastarska čestica
                                                            <strong><a class="btn btn-danger btn-sm float-right remove_type_other_additional_land_plot"><i class="fa fa-trash"></i> Ukloni</a></strong>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="form-group col-lg-12">
                                                                    {{ Form::fText($model, "type_other_additional_land_plots[$_lp_i][type_other_possession_number]", null, 'Upiši podatke iz posjedovnice (lista A) koji se odnose na broj katastarske čestice odnosno zemljište', ['placeholder' => 'Npr: 1111/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5A.jpeg') }}
                                                                </div>
                                                            </div>
                                                            <hr/>
                                                            <div class="row">
                                                                <div class="form-group col-lg-12">
                                                                    {{ Form::fDropdownText($model ,'Površina zemljišta', "type_other_additional_land_plots[$_lp_i][type_other_possession_area]", null, "type_other_additional_land_plots[$_lp_i][type_other_possession_area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5C.jpeg') }}
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    {{ Form::fTextArea($model, "type_other_additional_land_plots[$_lp_i][type_other_possession_identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5B.jpeg') }}
                                                                </div>
                                                            </div>

                                                            <div class="type_other_additional_possession_area_and_identification_container">
                                                                @if(!empty($model->type_other_additional_land_plots[$_lp_i]['type_other_additional_possession_area_and_identification']))
                                                                    @foreach($model->type_other_additional_land_plots[$_lp_i]['type_other_additional_possession_area_and_identification'] as $_additional_index => $_additional)
                                                                        <div class="type_other_additional_possession_area_and_identification_content">
                                                                            <hr/>
                                                                            <div class="row">
                                                                                <div class="col-lg-12">
                                                                                    <strong><a class="btn btn-danger btn-sm float-right remove_type_other_additional_possession_area_and_identification"><i class="fa fa-trash"></i> Ukloni</a></strong>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row">
                                                                                <div class="form-group col-lg-12">
                                                                                    {{ Form::fDropdownText($model ,'Površina zemljišta', "type_other_additional_land_plots[$_lp_i][type_other_additional_possession_area_and_identification][$_additional_index][area]", null, "type_other_additional_land_plots[$_lp_i][type_other_additional_possession_area_and_identification][$_additional_index][area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5C.jpeg') }}
                                                                                </div>
                                                                            </div>
                                                                            <div class="row">
                                                                                <div class="col-lg-12">
                                                                                    {{ Form::fTextArea($model, "type_other_additional_land_plots[$_lp_i][type_other_additional_possession_area_and_identification][$_additional_index][identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5B.jpeg') }}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    @endforeach
                                                                @endif
                                                            </div>

                                                            <hr/>
                                                            <div class="row mt-3">
                                                                <div class="col-lg-12">
                                                                    <a data-field-name="type_other_additional_land_plots[{{$_lp_i}}][type_other_additional_possession_area_and_identification]"  class="btn btn-default add_type_other_additional_possession_area_and_identification">+ Dodaj oznaku i površinu </a>

                                                                    <div class="button-helper">
                                                                        <a href="/documents/RealestatePurchasePOA/tooltips/other/help-1.jpeg" data-caption='Ponekad su u izvatku iz zemljišne knjige ispod retka koji se odnosi na oznaku cijelog zemljišta i njegovu površinu (obično prvi redak stupaca "Oznaka zemljišta" i "Površina" ispisan masnim slovima), upisani i dodatni retci u kojima se upisuju oznake sastavnih dijelova cijelog zemljišta i upisuje površina tih sastavnih dijelova. Za upis tih dodatnih redaka u ugovor, klikni na "Dodaj oznaku i površinu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        @endforeach
                                    @endif
                                </div>

                                <a data-field-name="type_other_additional_land_plots" class="btn btn-info btn-block add_type_other_additional_land_plot">Dodaj katastarsku česticu</a>
                                <div class="button-helper text-center">
                                    <a href="/documents/RealestatePurchasePOA/tooltips/other/help-2.jpeg" data-caption='Ponekad je u istom zemljišnoknjižnom ulošku upisano više zemljišta odnosno katastarskih čestica. Ako je više katastarskih čestica iz istog zemljišnoknjižnog uloška predmet punomoći, za upis tih katastarskih čestica u ugovor klikni na "Dodaj katastarsku česticu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                </div>
                            </div>

                            <div class="seller_shares_container">
                                <hr/>
                                <div class="row seller_share">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Je li prodavatelj vlasnik vlasničkog dijela 1/1 ili suvlasničkog dijela nekretnine?') }}
                                        {{ Form::fRadio($model, 'seller_has_full_realestate_ownership', 1, 'Vlasničkog dijela 1/1', ['checked' => !isset($model->seller_has_full_realestate_ownership), 'class' => 'form-check-input seller_has_full_realestate_ownership'], ' ', null, ' ') }}
                                        <div class="radio-input">
                                            {{ Form::fRadio($model, 'seller_has_full_realestate_ownership', 0, 'Suvlasničkog dijela:', ['class' => 'form-check-input seller_has_not_full_realestate_ownership'], ' ', null, ' ') }}
                                            {{ Form::number("seller_realestate_ownership_ratio_numerator", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                            /
                                            {{ Form::number("seller_realestate_ownership_ratio_denominator", null, ['placeholder' => 'Npr: 2', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr/>
                            <div class="row" data-buyer="0">
                                <div class="form-group col-lg-12">
                                    {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Odnosi li se ova punomoć na kupnju <span class="realestate_ownership_label_genitiv">'.(!isset($model->seller_has_full_realestate_ownership ) || $model->seller_has_full_realestate_ownership !== "0" ? 'vlasničkog' : 'suvlasničkog').'</span> dijela prodavatelja u cijelosti ili djelomično?') }}
                                    {{ Form::fRadio($model, 'is_full_sale', 1, 'U cijelosti', ['checked' => !isset($model->is_full_sale)]) }}
                                    <div class="radio-input">
                                        {{ Form::fRadio($model, 'is_full_sale', 0, 'Djelomično i to:', ['class' => 'form-check-input is_partial_sale_radio']) }}
                                        {{ Form::number("partial_sale_numerator", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        /
                                        {{ Form::number("partial_sale_denominator", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                        dijela koji je u vlasništvu prodavatelja
                                    </div>
                                </div>
                            </div>

                        </div>



                        <div class="realestate_is_not_chosen_container" style="@if(!isset($model->realestate_is_chosen) || $model->realestate_is_chosen)display:none;@endif">
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    {{  Form::fLabel($model, 'realestate_has_specific_features', '1. Je li opunomoćenik za opunomoćitelja ovlašten odabrati bilo koju nekretninu po svom izboru ili nekretninu s točno određenim značajkama?')  }}
                                    {{  Form::fRadio($model, 'realestate_has_specific_features', 0, 'Opunomoćenik može odabrati bilo koju nekretninu po svom izboru', ['checked' => !isset($model->realestate_has_specific_features), 'class' => 'form-check-input realestate_has_non_specific_features'])  }}
                                    {{  Form::fRadio($model, 'realestate_has_specific_features', 1, 'Opunomoćenik može odabrati nekretninu s točno određenim značajkama', ['class' => 'form-check-input realestate_has_specific_features'])  }}
                                </div>
                            </div>

                            <div class="realestate_specific_features_container" style="@if(!isset($model->realestate_has_specific_features) || !$model->realestate_has_specific_features) display:none; @endif">

                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fLabel($model, 'realestate_type_feature', '2. Odaberi vrstu nekretnine') }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'stan', 'Stan', ['checked' => !isset($model->realestate_type_feature)] ) }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'apartman', 'Apartman') }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'građevinsko zemljište', 'Građevinsko zemljište') }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'poljoprivredno zemljište', 'Poljoprivredno zemljište') }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'šuma', 'Šuma') }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'poslovni prostor', 'Poslovni prostor') }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'garaža', 'Garaža') }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'parkirno mjesto', 'Parkirno mjesto') }}
                                        {{ Form::fRadio($model, 'realestate_type_feature', 'custom', Form::text('custom_realestate_type_feature', null, ['class' => 'radio_input_text', 'placeholder' => 'Npr: vila']))}}
                                    </div>
                                </div>

                                <hr/>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        {{ Form::fText($model, 'realestate_location_feature', null, '3. Upiši lokaciju nekretnine', ['placeholder' => 'Npr: Istarska županija', 'data-address' => true]) }}
                                    </div>
                                </div>

                                <hr/>
                                <div class="row">
                                    <div class="form-group mb-0 col-lg-12">
                                        {{  Form::fLabel($model, 'realestate_surface_area_feature', '4. Kolika je površina nekretnine?')  }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'realestate_surface_area_feature_from', null, null, ['placeholder' => 'Npr: 100'], ['prepend' => 'Od', 'append' => 'm2']) }}
                                    </div>

                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'realestate_surface_area_feature_to', null, null, ['placeholder' => 'Npr: 500'], ['prepend' => 'Do', 'append' => 'm2']) }}
                                    </div>
                                </div>

                                <hr/>
                                <div class="row">
                                    <div class="form-group mb-0 col-lg-12">
                                        {{  Form::fLabel($model, 'realestate_price_range_feature', '5. Koji je cjenovni raspon nekretnine?')  }}
                                    </div>
                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'realestate_price_range_feature_from', null, null, ['placeholder' => 'Npr: 10.000,00', 'data-currency' => 'eur'], ['prepend' => 'Od', 'append' => 'EUR']) }}
                                    </div>

                                    <div class="form-group col-lg-6">
                                        {{ Form::fText($model, 'realestate_price_range_feature_to', null, null, ['placeholder' => 'Npr: 100.000,00', 'data-currency' => 'eur'], ['prepend' => 'Do', 'append' => 'EUR']) }}
                                    </div>
                                </div>

                                <hr/>
                                <div class="row">
                                    <div class="form-group mb-0 col-lg-12">
                                        {{ Form::fTextArea($model, 'realestate_additional_description_feature', null, '6. Dodatni opis nekretnine <small>(opcionalno)</small>', ['rows' => 4, 'placeholder' => 'Unesi dodatni opis...']) }}
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div id="realestates_container">

                @if(!empty($model->additional_realestates))

                    @php $i = 1; @endphp

                    @foreach($model->additional_realestates as $_index => $_additional_realestate)
                        @php $i++; @endphp
                        <div class="realestate_content">
                            <div class="card mb-4">
                                <div class="card-header">
                                    Nekretnina <span class="realestate_index">{{ $i }}</span>
                                    <strong><a class="btn btn-danger btn-sm float-right remove_realestate"><i
                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                </div>
                                <div class="card-body">

                                    <div class="row">
                                        <div class="form-group col-lg-12">
                                            {{  Form::fLabel($model, "additional_realestates[$_index][realestate_type]", '1. Koja vrsta nekretnine je predmet kupoprodaje?')  }}
                                            {{  Form::fRadio($model, "additional_realestates[$_index][realestate_type]", 'particular', 'Posebni dio nekretnine (etažno vlasništvo) upisan u glavnu zemljišnu knjigu (npr. stan, poslovni prostor, parkirno mjesto i slično)', ['class' => 'form-check-input realestate_type_particular', 'checked' => !isset($model->additional_realestates[$_index]["realestate_type"])])  }}
                                            {{  Form::fRadio($model, "additional_realestates[$_index][realestate_type]", 'flat', 'Stan upisan u knjigu položenih ugovora', ['class' => 'form-check-input realestate_type_flat'])  }}
                                            {{  Form::fRadio($model, "additional_realestates[$_index][realestate_type]", 'other', 'Druga vrsta nekretnine upisana u glavnu zemljišnu knjigu (npr. zgrada, kuća, livada, šuma i slično)', ['class' => 'form-check-input realestate_type_other'])  }}
                                        </div>
                                    </div>

                                    <div class="realestate_type_particular_container" style="@if($model->additional_realestates[$_index]["realestate_type"] != 'particular') display:none; @endif">
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fLabel($model, "additional_realestates[$_index][type_particular_municipal_court]", '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi zemljišnu knjigu u koju je upisana nekretnina', 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/2.jpeg') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fDropdown($model, "additional_realestates[$_index][type_particular_municipal_court]", null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fDropdown($model, "additional_realestates[$_index][type_particular_land_registry]", null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'] + (!empty($model->additional_realestates[$_index]['type_particular_land_registry']) ? [$model->additional_realestates[$_index]['type_particular_land_registry'] => $model->additional_realestates[$_index]['type_particular_land_registry']] : []), 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                                            </div>
                                        </div>
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fText($model, "additional_realestates[$_index][type_particular_cadastral_municipality]", null, '3. Upiši oznaku i naziv katastarske općine u kojoj se nekretnina nalazi', ['placeholder' => 'Npr: 999901, GRAD ZAGREB'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/3.jpeg') }}
                                            </div>
                                        </div>
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fText($model, "additional_realestates[$_index][type_particular_land_registry_folio_number]", null, '4. Upiši broj zemljišnoknjižnog uloška u koji su upisani podaci vezani uz nekretninu', ['placeholder' => 'Npr: 12345'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/4.jpeg') }}
                                            </div>
                                        </div>

                                        <hr/>

                                        <div class="mb-3">
                                            <div class="card">
                                                <div class="card-header">
                                                    Katastarska čestica
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            {{ Form::fLabel($model, "additional_realestates[$_index][type_particular_possession_sheet_data]", 'Upiši podatke iz posjedovnice (lista A) koji se odnose na katastarsku česticu odnosno zemljište') }}
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            {{ Form::fText($model, "additional_realestates[$_index][type_particular_possession_number]", null, 'Broj zemljišta/katastarske čestice', ['placeholder' => 'Npr: 1234/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5A.jpeg') }}
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            {{ Form::fDropdownText($model ,'Površina zemljišta', "additional_realestates[$_index][type_particular_possession_area]", null, "additional_realestates[$_index][type_particular_possession_area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5C.jpeg', 'Pazi da ovdje ne upišeš površinu posebnog dijela nekretnine, nego površinu cijele katastarske čestice! Primjerice, ako se radi o kupnji stana u etažiranoj stambenoj zgradi, ovdje ne upisuješ površinu stana, nego površinu cjelokupne katastarske čestice na kojoj je etažirana stambena zgrada izgrađena.') }}
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            {{ Form::fTextArea($model, "additional_realestates[$_index][type_particular_possession_identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5B.jpeg') }}
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                        <div class="type_particular_additional_land_plot_container">
                                            @if(!empty($model->additional_realestates[$_index]['type_particular_additional_land_plots']))
                                                @foreach($model->additional_realestates[$_index]['type_particular_additional_land_plots'] as $_lp_i => $_land_plot)
                                                    <div class="type_particular_additional_land_plot_content">

                                                        <div class="mb-3">
                                                            <div class="card">
                                                                <div class="card-header">
                                                                    Katastarska čestica
                                                                    <strong><a class="btn btn-danger btn-sm float-right remove_type_particular_additional_land_plot"><i class="fa fa-trash"></i> Ukloni</a></strong>
                                                                </div>
                                                                <div class="card-body">
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-12">
                                                                            {{ Form::fLabel($model, "additional_realestates[$_index][type_particular_additional_land_plots][$_lp_i][type_particular_possession_sheet_data]", 'Upiši podatke iz posjedovnice (lista A) koji se odnose na katastarsku česticu odnosno zemljište') }}
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-6">
                                                                            {{ Form::fText($model, "additional_realestates[$_index][type_particular_additional_land_plots][$_lp_i][type_particular_possession_number]", null, 'Broj zemljišta/katastarske čestice', ['placeholder' => 'Npr: 1234/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5A.jpeg') }}
                                                                        </div>
                                                                        <div class="form-group col-lg-6">
                                                                            {{ Form::fDropdownText($model ,'Površina zemljišta', "additional_realestates[$_index][type_particular_additional_land_plots][$_lp_i][type_particular_possession_area]", null, "additional_realestates[$_index][type_particular_additional_land_plots][$_lp_i][type_particular_possession_area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5C.jpeg', 'Pazi da ovdje ne upišeš površinu posebnog dijela nekretnine, nego površinu cijele katastarske čestice! Primjerice, ako se radi o kupnji stana u etažiranoj stambenoj zgradi, ovdje ne upisuješ površinu stana, nego površinu cjelokupne katastarske čestice na kojoj je etažirana stambena zgrada izgrađena.') }}
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-12">
                                                                            {{ Form::fTextArea($model, "additional_realestates[$_index][type_particular_additional_land_plots][$_lp_i][type_particular_possession_identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/5B.jpeg') }}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>

                                        <a data-field-name="{{ "additional_realestates[$_index][type_particular_additional_land_plots]" }}" class="btn btn-info btn-block add_type_particular_additional_land_plot">Dodaj katastarsku česticu</a>
                                        <div class="button-helper text-center">
                                            <a href="/documents/RealestatePurchasePOA/tooltips/other/help-2.jpeg" data-caption='Ponekad je u istom zemljišnoknjižnom ulošku upisano više zemljišta odnosno katastarskih čestica. Ako je više katastarskih čestica iz istog zemljišnoknjižnog uloška predmet punomoći, za upis tih katastarskih čestica u ugovor klikni na "Dodaj katastarsku česticu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                        </div>

                                        <hr/>

                                        <div class="row">
                                            <div class="col-lg-12 form-group">
                                                {{ Form::fTextArea($model, "additional_realestates[$_index][type_particular_ownership_sheet_data]", null, '5. Upiši podatke iz vlastovnice (lista B) koji se odnose na posebni dio nekretnine (etažno vlasništvo)', ['placeholder' => 'Npr: 11. Suvlasnički dio: 111/11111 ETAŽNO VLASNIŠTVO (E-11), stan broj 11 - STAMBENA JEDINICA "D" (u elaboratu označen ružičastom bojom) - trosobni stan u prizemlju (sa stubišta ulaz desno) ukupne neto korisne površine 111,11 čm'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/particular/6.jpeg', 'Ovdje upisuješ samo oznaku i opis posebnog dijela nekretnine (etaže) iz izvatka iz zemljišne knjige! Pazi da ovdje ne upišeš podatke o vlasniku (ili suvlasnicima) posebnog dijela nekretnine. Za upis podataka o vlasniku (ili suvlasnicima) predviđena su posebna polja na kraju ovog koraka upitnika.') }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="realestate_type_flat_container" style="@if($model->additional_realestates[$_index]["realestate_type"] != 'flat') display:none; @endif">

                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fLabel($model, "additional_realestates[$_index][type_flat_municipal_court]", '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi knjigu položenih ugovora u koju je upisan stan', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/2.jpeg') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fDropdown($model, "additional_realestates[$_index][type_flat_municipal_court]", null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fDropdown($model, "additional_realestates[$_index][type_flat_land_registry]", null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'] + (!empty($model->additional_realestates[$_index]['type_flat_land_registry']) ? [$model->additional_realestates[$_index]['type_flat_land_registry'] => $model->additional_realestates[$_index]['type_flat_land_registry']] : []), 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                                            </div>
                                        </div>

                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fText($model, "additional_realestates[$_index][type_flat_deposited_contracts_book_name]", null, '3. Upiši naziv knjige položenih ugovora u kojoj je upisan stan', ['placeholder' => 'Npr: Vrapče staro'], 'Knjiga PU', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/3.jpeg') }}
                                            </div>
                                        </div>

                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fLabel($model, "additional_realestates[$_index][type_flat_folio_number]", '4. Upiši broj poduloška i zk uloška u koji su upisani podaci vezani uz stan', 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/4.jpeg') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_realestates[$_index][type_flat_subfolio_number]", null, 'Broj poduloška', ['placeholder' => 'Npr: 11']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "additional_realestates[$_index][type_flat_folio_number]", null, 'Broj zk uloška', ['placeholder' => 'Npr: 12345']) }}
                                            </div>
                                        </div>


                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                {{ Form::fTextArea($model, "additional_realestates[$_index][type_flat_possession_sheet_section_one_data]", null, '5. Upiši podatke iz prvog odjeljka posjedovnice (lista A) koji se odnose stambenu zgradu u kojoj se nalazi stan', ['placeholder' => 'Npr: Stambena zgrada Riječka ulica 1, Rijeka, sagrađena na čest. br. 1111/11, po novoj izmjeri čest. br. 1234/1 k.o. PLASE'], 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/5.jpeg') }}
                                            </div>
                                        </div>

                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                {{ Form::fTextArea($model, "additional_realestates[$_index][type_flat_possession_sheet_section_two_data]", null, '6. Upiši podatke iz drugog odjeljka posjedovnice (lista A) koji se odnose na stan', ['placeholder' => 'Npr: stan na II (drugom) katu desno, koji se sastoji od dvije sobe i sporednih prostorija u površini od 61,11'], 'Ovaj podatak možeš prepisati iz izvatka iz knjige položenih ugovora za stan. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/flat/6.jpeg') }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="realestate_type_other_container" style="@if($model->additional_realestates[$_index]["realestate_type"] != 'other') display:none; @endif">
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fLabel($model, "additional_realestates[$_index][type_other_municipal_court]", '2. Upiši općinski sud i zemljišnoknjižni odjel koji vodi zemljišnu knjigu u koju je upisana nekretnina', 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/2.jpeg') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fDropdown($model, "additional_realestates[$_index][type_other_municipal_court]", null, [null=>null, 'Općinski sud u Bjelovaru' => 'Općinski sud u Bjelovaru', 'Općinski sud u Crikvenici' => 'Općinski sud u Crikvenici', 'Općinski sud u Čakovcu' => 'Općinski sud u Čakovcu', 'Općinski sud u Dubrovniku' => 'Općinski sud u Dubrovniku', 'Općinski sud u Đakovu' => 'Općinski sud u Đakovu', 'Općinski sud u Gospiću' => 'Općinski sud u Gospiću', 'Općinski sud u Karlovcu' => 'Općinski sud u Karlovcu', 'Općinski sud u Koprivnici' => 'Općinski sud u Koprivnici', 'Općinski sud u Kutini' => 'Općinski sud u Kutini', 'Općinski sud u Makarskoj' => 'Općinski sud u Makarskoj', 'Općinski sud u Metkoviću' => 'Općinski sud u Metkoviću', 'Općinski sud u Novom Zagrebu' => 'Općinski sud u Novom Zagrebu', 'Općinski sud u Osijeku' => 'Općinski sud u Osijeku', 'Općinski sud u Pazinu' => 'Općinski sud u Pazinu', 'Općinski sud u Požegi' => 'Općinski sud u Požegi', 'Općinski sud u Puli-Pola' => 'Općinski sud u Puli-Pola', 'Općinski sud u Rijeci' => 'Općinski sud u Rijeci', 'Općinski sud u Sesvetama' => 'Općinski sud u Sesvetama', 'Općinski sud u Sisku' => 'Općinski sud u Sisku', 'Općinski sud u Slavonskom Brodu' => 'Općinski sud u Slavonskom Brodu', 'Općinski sud u Splitu' => 'Općinski sud u Splitu', 'Općinski sud u Šibeniku' => 'Općinski sud u Šibeniku', 'Općinski sud u Varaždinu' => 'Općinski sud u Varaždinu', 'Općinski sud u Velikoj Gorici' => 'Općinski sud u Velikoj Gorici', 'Općinski sud u Vinkovcima' => 'Općinski sud u Vinkovcima', 'Općinski sud u Virovitici' => 'Općinski sud u Virovitici', 'Općinski sud u Vukovaru' => 'Općinski sud u Vukovaru', 'Općinski sud u Zadru' => 'Općinski sud u Zadru', 'Općinski građanski sud u Zagrebu' => 'Općinski građanski sud u Zagrebu', 'Općinski sud u Zlataru' => 'Općinski sud u Zlataru'], 'Općinski sud', ['class' => 'form-control court-select2']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fDropdown($model, "additional_realestates[$_index][type_other_land_registry]", null, [null=>null, 'Zemljišnoknjižni odjel Beli Manastir' => 'Zemljišnoknjižni odjel Beli Manastir', 'Zemljišnoknjižni odjel Benkovac' => 'Zemljišnoknjižni odjel Benkovac', 'Zemljišnoknjižni odjel Biograd na Moru' => 'Zemljišnoknjižni odjel Biograd na Moru', 'Zemljišnoknjižni odjel Bjelovar' => 'Zemljišnoknjižni odjel Bjelovar', 'Zemljišnoknjižni odjel Blato' => 'Zemljišnoknjižni odjel Blato', 'Zemljišnoknjižni odjel Buje-Buie' => 'Zemljišnoknjižni odjel Buje-Buie', 'Zemljišnoknjižni odjel Buzet' => 'Zemljišnoknjižni odjel Buzet', 'Zemljišnoknjižni odjel Crikvenica' => 'Zemljišnoknjižni odjel Crikvenica', 'Zemljišnoknjižni odjel Čabar' => 'Zemljišnoknjižni odjel Čabar', 'Zemljišnoknjižni odjel Čakovec' => 'Zemljišnoknjižni odjel Čakovec', 'Zemljišnoknjižni odjel Čazma' => 'Zemljišnoknjižni odjel Čazma', 'Zemljišnoknjižni odjel Daruvar' => 'Zemljišnoknjižni odjel Daruvar', 'Zemljišnoknjižni odjel Delnice' => 'Zemljišnoknjižni odjel Delnice', 'Zemljišnoknjižni odjel Donja Stubica' => 'Zemljišnoknjižni odjel Donja Stubica', 'Zemljišnoknjižni odjel Donji Lapac' => 'Zemljišnoknjižni odjel Donji Lapac', 'Zemljišnoknjižni odjel Donji Miholjac' => 'Zemljišnoknjižni odjel Donji Miholjac', 'Zemljišnoknjižni odjel Drniš' => 'Zemljišnoknjižni odjel Drniš', 'Zemljišnoknjižni odjel Dubrovnik' => 'Zemljišnoknjižni odjel Dubrovnik', 'Zemljišnoknjižni odjel Dugo Selo' => 'Zemljišnoknjižni odjel Dugo Selo', 'Zemljišnoknjižni odjel Dvor' => 'Zemljišnoknjižni odjel Dvor', 'Zemljišnoknjižni odjel Đakovo' => 'Zemljišnoknjižni odjel Đakovo', 'Zemljišnoknjižni odjel Đurđevac' => 'Zemljišnoknjižni odjel Đurđevac', 'Zemljišnoknjižni odjel Garešnica' => 'Zemljišnoknjižni odjel Garešnica', 'Zemljišnoknjižni odjel Glina' => 'Zemljišnoknjižni odjel Glina', 'Zemljišnoknjižni odjel Gospić' => 'Zemljišnoknjižni odjel Gospić', 'Zemljišnoknjižni odjel Gračac' => 'Zemljišnoknjižni odjel Gračac', 'Zemljišnoknjižni odjel Gvozd' => 'Zemljišnoknjižni odjel Gvozd', 'Zemljišnoknjižni odjel Hrvatska Kostajnica' => 'Zemljišnoknjižni odjel Hrvatska Kostajnica', 'Zemljišnoknjižni odjel Ilok' => 'Zemljišnoknjižni odjel Ilok', 'Zemljišnoknjižni odjel Imotski' => 'Zemljišnoknjižni odjel Imotski', 'Zemljišnoknjižni odjel Ivanec' => 'Zemljišnoknjižni odjel Ivanec', 'Zemljišnoknjižni odjel Ivanić Grad' => 'Zemljišnoknjižni odjel Ivanić Grad', 'Zemljišnoknjižni odjel Jastrebarsko' => 'Zemljišnoknjižni odjel Jastrebarsko', 'Zemljišnoknjižni odjel Karlovac' => 'Zemljišnoknjižni odjel Karlovac', 'Zemljišnoknjižni odjel Kaštel Lukšić' => 'Zemljišnoknjižni odjel Kaštel Lukšić', 'Zemljišnoknjižni odjel Klanjec' => 'Zemljišnoknjižni odjel Klanjec', 'Zemljišnoknjižni odjel Knin' => 'Zemljišnoknjižni odjel Knin', 'Zemljišnoknjižni odjel Koprivnica' => 'Zemljišnoknjižni odjel Koprivnica', 'Zemljišnoknjižni odjel Korčula' => 'Zemljišnoknjižni odjel Korčula', 'Zemljišnoknjižni odjel Korenica' => 'Zemljišnoknjižni odjel Korenica', 'Zemljišnoknjižni odjel Krapina' => 'Zemljišnoknjižni odjel Krapina', 'Zemljišnoknjižni odjel Križevci' => 'Zemljišnoknjižni odjel Križevci', 'Zemljišnoknjižni odjel Krk' => 'Zemljišnoknjižni odjel Krk', 'Zemljišnoknjižni odjel Kutina' => 'Zemljišnoknjižni odjel Kutina', 'Zemljišnoknjižni odjel Labin' => 'Zemljišnoknjižni odjel Labin', 'Zemljišnoknjižni odjel Ludbreg' => 'Zemljišnoknjižni odjel Ludbreg', 'Zemljišnoknjižni odjel Makarska' => 'Zemljišnoknjižni odjel Makarska', 'Zemljišnoknjižni odjel Mali Lošinj' => 'Zemljišnoknjižni odjel Mali Lošinj', 'Zemljišnoknjižni odjel Metković' => 'Zemljišnoknjižni odjel Metković', 'Zemljišnoknjižni odjel Našice' => 'Zemljišnoknjižni odjel Našice', 'Zemljišnoknjižni odjel Nova Gradiška' => 'Zemljišnoknjižni odjel Nova Gradiška', 'Zemljišnoknjižni odjel Novi Marof' => 'Zemljišnoknjižni odjel Novi Marof', 'Zemljišnoknjižni odjel Novi Vinodolski' => 'Zemljišnoknjižni odjel Novi Vinodolski', 'Zemljišnoknjižni odjel Novi Zagreb' => 'Zemljišnoknjižni odjel Novi Zagreb', 'Zemljišnoknjižni odjel Novska' => 'Zemljišnoknjižni odjel Novska', 'Zemljišnoknjižni odjel Obrovac' => 'Zemljišnoknjižni odjel Obrovac', 'Zemljišnoknjižni odjel Ogulin' => 'Zemljišnoknjižni odjel Ogulin', 'Zemljišnoknjižni odjel Omiš' => 'Zemljišnoknjižni odjel Omiš', 'Zemljišnoknjižni odjel Opatija' => 'Zemljišnoknjižni odjel Opatija', 'Zemljišnoknjižni odjel Orahovica' => 'Zemljišnoknjižni odjel Orahovica', 'Zemljišnoknjižni odjel Osijek' => 'Zemljišnoknjižni odjel Osijek', 'Zemljišnoknjižni odjel Otočac' => 'Zemljišnoknjižni odjel Otočac', 'Zemljišnoknjižni odjel Otok' => 'Zemljišnoknjižni odjel Otok', 'Zemljišnoknjižni odjel Ozalj' => 'Zemljišnoknjižni odjel Ozalj', 'Zemljišnoknjižni odjel Pag' => 'Zemljišnoknjižni odjel Pag', 'Zemljišnoknjižni odjel Pakrac' => 'Zemljišnoknjižni odjel Pakrac', 'Zemljišnoknjižni odjel Pazin' => 'Zemljišnoknjižni odjel Pazin', 'Zemljišnoknjižni odjel Petrinja' => 'Zemljišnoknjižni odjel Petrinja', 'Zemljišnoknjižni odjel Pitomača' => 'Zemljišnoknjižni odjel Pitomača', 'Zemljišnoknjižni odjel Ploče' => 'Zemljišnoknjižni odjel Ploče', 'Zemljišnoknjižni odjel Poreč-Parenzo' => 'Zemljišnoknjižni odjel Poreč-Parenzo', 'Zemljišnoknjižni odjel Požega' => 'Zemljišnoknjižni odjel Požega', 'Zemljišnoknjižni odjel Pregrada' => 'Zemljišnoknjižni odjel Pregrada', 'Zemljišnoknjižni odjel Prelog' => 'Zemljišnoknjižni odjel Prelog', 'Zemljišnoknjižni odjel Pula' => 'Zemljišnoknjižni odjel Pula', 'Zemljišnoknjižni odjel Rab' => 'Zemljišnoknjižni odjel Rab', 'Zemljišnoknjižni odjel Rijeka' => 'Zemljišnoknjižni odjel Rijeka', 'Zemljišnoknjižni odjel Rovinj-Rovigno' => 'Zemljišnoknjižni odjel Rovinj-Rovigno', 'Zemljišnoknjižni odjel Samobor' => 'Zemljišnoknjižni odjel Samobor', 'Zemljišnoknjižni odjel Senj' => 'Zemljišnoknjižni odjel Senj', 'Zemljišnoknjižni odjel Sesvete' => 'Zemljišnoknjižni odjel Sesvete', 'Zemljišnoknjižni odjel Sinj' => 'Zemljišnoknjižni odjel Sinj', 'Zemljišnoknjižni odjel Sisak' => 'Zemljišnoknjižni odjel Sisak', 'Zemljišnoknjižni odjel Slatina' => 'Zemljišnoknjižni odjel Slatina', 'Zemljišnoknjižni odjel Slavonski Brod' => 'Zemljišnoknjižni odjel Slavonski Brod', 'Zemljišnoknjižni odjel Slunj' => 'Zemljišnoknjižni odjel Slunj', 'Zemljišnoknjižni odjel Solin' => 'Zemljišnoknjižni odjel Solin', 'Zemljišnoknjižni odjel Split' => 'Zemljišnoknjižni odjel Split', 'Zemljišnoknjižni odjel Stari Grad' => 'Zemljišnoknjižni odjel Stari Grad', 'Zemljišnoknjižni odjel Supetar' => 'Zemljišnoknjižni odjel Supetar', 'Zemljišnoknjižni odjel Sveti Ivan Zelina' => 'Zemljišnoknjižni odjel Sveti Ivan Zelina', 'Zemljišnoknjižni odjel Šibenik' => 'Zemljišnoknjižni odjel Šibenik', 'Zemljišnoknjižni odjel Tisno' => 'Zemljišnoknjižni odjel Tisno', 'Zemljišnoknjižni odjel Trogir' => 'Zemljišnoknjižni odjel Trogir', 'Zemljišnoknjižni odjel Valpovo' => 'Zemljišnoknjižni odjel Valpovo', 'Zemljišnoknjižni odjel Varaždin' => 'Zemljišnoknjižni odjel Varaždin', 'Zemljišnoknjižni odjel Velika Gorica' => 'Zemljišnoknjižni odjel Velika Gorica', 'Zemljišnoknjižni odjel Vinkovci' => 'Zemljišnoknjižni odjel Vinkovci', 'Zemljišnoknjižni odjel Virovitica' => 'Zemljišnoknjižni odjel Virovitica', 'Zemljišnoknjižni odjel Vojnić' => 'Zemljišnoknjižni odjel Vojnić', 'Zemljišnoknjižni odjel Vrbovec' => 'Zemljišnoknjižni odjel Vrbovec', 'Zemljišnoknjižni odjel Vrbovsko' => 'Zemljišnoknjižni odjel Vrbovsko', 'Zemljišnoknjižni odjel Vukovar' => 'Zemljišnoknjižni odjel Vukovar', 'Zemljišnoknjižni odjel Zabok' => 'Zemljišnoknjižni odjel Zabok', 'Zemljišnoknjižni odjel Zadar' => 'Zemljišnoknjižni odjel Zadar', 'Zemljišnoknjižni odjel Zagreb' => 'Zemljišnoknjižni odjel Zagreb', 'Zemljišnoknjižni odjel Zaprešić' => 'Zemljišnoknjižni odjel Zaprešić', 'Zemljišnoknjižni odjel Zlatar' => 'Zemljišnoknjižni odjel Zlatar', 'Zemljišnoknjižni odjel Županja' => 'Zemljišnoknjižni odjel Županja'] + (!empty($model->additional_realestates[$_index]['type_other_land_registry']) ? [$model->additional_realestates[$_index]['type_other_land_registry'] => $model->additional_realestates[$_index]['type_other_land_registry']] : []), 'Zemljišnoknjižni odjel', ['class' => 'form-control landRegistry-select2']) }}
                                            </div>
                                        </div>
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fText($model, "additional_realestates[$_index][type_other_cadastral_municipality]", null, '3. Upiši oznaku i naziv katastarske općine u kojoj se nekretnina nalazi', ['placeholder' => 'Npr: 999901, GRAD ZAGREB'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/3.jpeg') }}
                                            </div>
                                        </div>
                                        <hr/>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fText($model, "additional_realestates[$_index][type_other_land_registry_folio_number]", null, '4. Upiši broj zemljišnoknjižnog uloška u koji su upisani podaci vezani uz nekretninu', ['placeholder' => 'Npr: 12345'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/4.jpeg') }}
                                            </div>
                                        </div>

                                        <hr/>

                                        <div class="mb-3">
                                            <div class="card">
                                                <div class="card-header">
                                                    Katastarska čestica
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            {{ Form::fText($model, "additional_realestates[$_index][type_other_possession_number]", null, 'Upiši podatke iz posjedovnice (lista A) koji se odnose na broj katastarske čestice odnosno zemljište', ['placeholder' => 'Npr: 1111/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5A.jpeg') }}
                                                        </div>
                                                    </div>
                                                    <hr/>
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            {{ Form::fDropdownText($model ,'Površina zemljišta', "additional_realestates[$_index][type_other_possession_area]", null, "additional_realestates[$_index][type_other_possession_area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5C.jpeg') }}
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            {{ Form::fTextArea($model, "additional_realestates[$_index][type_other_possession_identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5B.jpeg') }}
                                                        </div>
                                                    </div>

                                                    <div class="type_other_additional_possession_area_and_identification_container">
                                                        @if(!empty($model->additional_realestates[$_index]["type_other_additional_possession_area_and_identification"]))
                                                            @foreach($model->additional_realestates[$_index]["type_other_additional_possession_area_and_identification"] as $_additional_index => $_additional)
                                                                <div class="type_other_additional_possession_area_and_identification_content">
                                                                    <hr/>
                                                                    <div class="row">
                                                                        <div class="col-lg-12">
                                                                            <strong><a class="btn btn-danger btn-sm float-right remove_type_other_additional_possession_area_and_identification"><i class="fa fa-trash"></i> Ukloni</a></strong>
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-12">
                                                                            {{ Form::fDropdownText($model ,'Površina zemljišta', "additional_realestates[$_index][type_other_additional_possession_area_and_identification][$_additional_index][area]", null, "additional_realestates[$_index][type_other_additional_possession_area_and_identification][$_additional_index][area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5C.jpeg') }}
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-12">
                                                                            {{ Form::fTextArea($model, "additional_realestates[$_index][type_other_additional_possession_area_and_identification][$_additional_index][identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5B.jpeg') }}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </div>

                                                    <hr/>
                                                    <div class="row mt-3">
                                                        <div class="col-lg-12">
                                                            <a data-field-name="additional_realestates[{{$_index}}][type_other_additional_possession_area_and_identification]"  class="btn btn-default add_type_other_additional_possession_area_and_identification">+ Dodaj oznaku i površinu </a>

                                                            <div class="button-helper">
                                                                <a href="/documents/RealestatePurchasePOA/tooltips/other/help-1.jpeg" data-caption='Ponekad su u izvatku iz zemljišne knjige ispod retka koji se odnosi na oznaku cijelog zemljišta i njegovu površinu (obično prvi redak stupaca "Oznaka zemljišta" i "Površina" ispisan masnim slovima), upisani i dodatni retci u kojima se upisuju oznake sastavnih dijelova cijelog zemljišta i upisuje površina tih sastavnih dijelova. Za upis tih dodatnih redaka u ugovor, klikni na "Dodaj oznaku i površinu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                        <div class="type_other_additional_land_plot_container">
                                            @if(!empty($model->additional_realestates[$_index]['type_other_additional_land_plots']))
                                                @foreach($model->additional_realestates[$_index]['type_other_additional_land_plots'] as $_lp_i => $_land_plot)
                                                    <div class="type_other_additional_land_plot_content">

                                                        <div class="mb-3">
                                                            <div class="card">
                                                                <div class="card-header">
                                                                    Katastarska čestica
                                                                    <strong><a class="btn btn-danger btn-sm float-right remove_type_other_additional_land_plot"><i class="fa fa-trash"></i> Ukloni</a></strong>
                                                                </div>
                                                                <div class="card-body">
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-12">
                                                                            {{ Form::fText($model, "additional_realestates[$_index][type_other_additional_land_plots][$_lp_i][type_other_possession_number]", null, 'Upiši podatke iz posjedovnice (lista A) koji se odnose na broj katastarske čestice odnosno zemljište', ['placeholder' => 'Npr: 1111/1'], null, 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5A.jpeg') }}
                                                                        </div>
                                                                    </div>
                                                                    <hr/>
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-12">
                                                                            {{ Form::fDropdownText($model ,'Površina zemljišta', "additional_realestates[$_index][type_other_additional_land_plots][$_lp_i][type_other_possession_area]", null, "additional_realestates[$_index][type_other_additional_land_plots][$_lp_i][type_other_possession_area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5C.jpeg') }}
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-12">
                                                                            {{ Form::fTextArea($model, "additional_realestates[$_index][type_other_additional_land_plots][$_lp_i][type_other_possession_identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5B.jpeg') }}
                                                                        </div>
                                                                    </div>

                                                                    <div class="type_other_additional_possession_area_and_identification_container">
                                                                        @if(!empty($model->additional_realestates[$_index]['type_other_additional_land_plots'][$_lp_i]['type_other_additional_possession_area_and_identification']))
                                                                            @foreach($model->additional_realestates[$_index]['type_other_additional_land_plots'][$_lp_i]['type_other_additional_possession_area_and_identification'] as $_additional_index => $_additional)
                                                                                <div class="type_other_additional_possession_area_and_identification_content">
                                                                                    <hr/>
                                                                                    <div class="row">
                                                                                        <div class="col-lg-12">
                                                                                            <strong><a class="btn btn-danger btn-sm float-right remove_type_other_additional_possession_area_and_identification"><i class="fa fa-trash"></i> Ukloni</a></strong>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="row">
                                                                                        <div class="form-group col-lg-12">
                                                                                            {{ Form::fDropdownText($model ,'Površina zemljišta', "additional_realestates[$_index][type_other_additional_land_plots][$_lp_i][type_other_additional_possession_area_and_identification][$_additional_index][area]", null, "additional_realestates[$_index][type_other_additional_land_plots][$_lp_i][type_other_additional_possession_area_and_identification][$_additional_index][area_type]", null, ["m2" => "m2", "čvh" => "čvh", "jutro" => "jutro"], ['placeholder' => 'Npr: 1100'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5C.jpeg') }}
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="row">
                                                                                        <div class="col-lg-12">
                                                                                            {{ Form::fTextArea($model, "additional_realestates[$_index][type_other_additional_land_plots][$_lp_i][type_other_additional_possession_area_and_identification][$_additional_index][identification]", null, 'Oznaka zemljišta', ['rows' => 2, 'placeholder' => 'Npr: KUĆA BROJ 1 I DVORIŠTE U RIJEČKOJ ULICI'], 'Ovaj podatak možeš prepisati iz izvatka iz zemljišne knjige za nekretninu. Na slici je označeno mjesto gdje bi se taj podatak trebao nalaziti na izvatku.', null, '/documents/RealestatePurchasePOA/tooltips/other/5B.jpeg') }}
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            @endforeach
                                                                        @endif
                                                                    </div>

                                                                    <hr/>
                                                                    <div class="row mt-3">
                                                                        <div class="col-lg-12">
                                                                            <a data-field-name="additional_realestates[{{$_index}}][type_other_additional_land_plots][{{$_lp_i}}][type_other_additional_possession_area_and_identification]"  class="btn btn-default add_type_other_additional_possession_area_and_identification">+ Dodaj oznaku i površinu </a>

                                                                            <div class="button-helper">
                                                                                <a href="/documents/RealestatePurchasePOA/tooltips/other/help-1.jpeg" data-caption='Ponekad su u izvatku iz zemljišne knjige ispod retka koji se odnosi na oznaku cijelog zemljišta i njegovu površinu (obično prvi redak stupaca "Oznaka zemljišta" i "Površina" ispisan masnim slovima), upisani i dodatni retci u kojima se upisuju oznake sastavnih dijelova cijelog zemljišta i upisuje površina tih sastavnih dijelova. Za upis tih dodatnih redaka u ugovor, klikni na "Dodaj oznaku i površinu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>

                                        <a data-field-name="{{ "additional_realestates[$_index][type_other_additional_land_plots]" }}" class="btn btn-info btn-block add_type_other_additional_land_plot">Dodaj katastarsku česticu</a>
                                        <div class="button-helper text-center">
                                            <a href="/documents/RealestatePurchasePOA/tooltips/other/help-2.jpeg" data-caption='Ponekad je u istom zemljišnoknjižnom ulošku upisano više zemljišta odnosno katastarskih čestica. Ako je više katastarskih čestica iz istog zemljišnoknjižnog uloška predmet punomoći, za upis tih katastarskih čestica u ugovor klikni na "Dodaj katastarsku česticu".' class="fancybox"><i class="fa fa-question-circle"></i> Objašnjenje</a>
                                        </div>
                                    </div>

                                    <div class="seller_shares_container">
                                        <hr/>
                                        <div class="row seller_share">
                                            <div class="form-group col-lg-12">
                                                {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Je li prodavatelj vlasnik vlasničkog dijela 1/1 ili suvlasničkog dijela nekretnine?') }}
                                                {{ Form::fRadio($model, "additional_realestates[$_index][seller_has_full_realestate_ownership]", 1, 'Vlasničkog dijela 1/1', ['checked' => !isset($model->additional_realestates[$_index]["seller_has_full_realestate_ownership"]), 'class' => 'form-check-input seller_has_full_realestate_ownership'], ' ', null, ' ') }}
                                                <div class="radio-input">
                                                    {{ Form::fRadio($model, "additional_realestates[$_index][seller_has_full_realestate_ownership]", 0, 'Suvlasničkog dijela:', ['class' => 'form-check-input seller_has_not_full_realestate_ownership'], ' ', null, ' ') }}
                                                    {{ Form::number("additional_realestates[$_index][seller_realestate_ownership_ratio_numerator]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                                    /
                                                    {{ Form::number("additional_realestates[$_index][seller_realestate_ownership_ratio_denominator]", null, ['placeholder' => 'Npr: 2', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr/>
                                    <div class="row" data-buyer="0">
                                        <div class="form-group col-lg-12">
                                            {{ Form::fLabel($model, '', '<span class="dynamic_index"></span>' .'. Odnosi li se ova punomoć na kupnju <span class="realestate_ownership_label_genitiv">'.(!isset($model->additional_realestates[$_index]["seller_has_full_realestate_ownership"]) || $model->additional_realestates[$_index]["seller_has_full_realestate_ownership"] !== "0" ? 'vlasničkog' : 'suvlasničkog').'</span> dijela prodavatelja u cijelosti ili djelomično?') }}
                                            {{ Form::fRadio($model, "additional_realestates[$_index][is_full_sale]", 1, 'U cijelosti', ['checked' => !isset($model->additional_realestates[$_index]['is_full_sale'])]) }}
                                            <div class="radio-input">
                                                {{ Form::fRadio($model, "additional_realestates[$_index][is_full_sale]", 0, 'Djelomično i to:', ['class' => 'form-check-input is_partial_sale_radio']) }}
                                                {{ Form::number("additional_realestates[$_index][partial_sale_numerator]", null, ['placeholder' => 'Npr: 1', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                                /
                                                {{ Form::number("additional_realestates[$_index][partial_sale_denominator]", null, ['placeholder' => 'Npr: 4', 'style' => 'width:100px; display: inline-block; text-align:center;', 'class' => 'form-control', 'min' => 1]) }}
                                                dijela koji je u vlasništvu prodavatelja
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endif

            </div>

            <div class="row" id="add_realestate_block" style="@if(isset($model->realestate_is_chosen) && !$model->realestate_is_chosen)display:none;@endif">
                <div class="form-group col-lg-12">

                                <span data-toggle="tooltip"
                                      data-original-title='Ako ovom punomoći želiš dati ovlaštenje opunomoćeniku za kupnju više od jedne nekretnine, klikom na "Dodaj nekretninu" u ugovor možeš dodati podatke i o drugim nekretninama koje su predmet kupoprodaje.'>
                        <a class="btn btn-info btn-block" id="add_realestate">+ Dodaj nekretninu     <i
                                    class="fa fa-info-circle px-lg-0 px-2"></i> </a>
                    </span>

                </div>
            </div>

        </div>

    </div>

    {{ Form::close() }}

@endsection
