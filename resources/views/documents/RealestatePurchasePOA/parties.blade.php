@extends('layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o opunomoćitelju
                </div>
                <div class="card-body" id="principal_content">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'principal_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: <PERSON><PERSON>']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'principal_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'principal_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'principal_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'principal_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'principal_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>

                    </div>

                    <div class="principal_representatives">

                        <div class="row">
                            <div class="form-group col-lg-12 pt-2">
                                {{ Form::fCheckbox($model, '', 1, 'Opunomoćitelj je pravna osoba i ima zakonskog zastupnika ili drugu fizičku osobu koja daje punomoć u ime i za račun opunomoćitelja', ['class' => 'principal_representatives_exist form-check-input', 'checked' => !empty($model->principal_representatives)]) }}
                            </div>
                        </div>

                        <div class="principal_representatives_container dynamic"
                             style="@if(empty($model->principal_representatives)) display:none; @endif">

                            @if(!empty($model->principal_representatives))
                                @foreach($model->principal_representatives as $i => $_authorized_person)
                                    <div class="principal_representative_content">
                                        <div class="maps_autofill_container">
                                            <hr/>
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <strong><a class="btn btn-danger btn-sm float-right remove_principal_representative"><i
                                                                    class="fa fa-trash"></i> Ukloni</a></strong>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "principal_representatives[$i][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa opunomoćitelja') }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "principal_representatives[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "principal_representatives[$i][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "principal_representatives[$i][city]", null, 'Grad/mjesto', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "principal_representatives[$i][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                                </div>
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fText($model, "principal_representatives[$i][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="form-group col-lg-6">
                                                    {{ Form::fNumber($model, "principal_representatives[$i][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach

                            @endif

                        </div>

                        <div class="row add_principal_representative_container"
                             style="@if(empty($model->principal_representatives)) display:none; @endif">
                            <div class="form-group col-lg-12">
                                <a data-id="principal_representatives" class="btn btn-info add_principal_representative">+ Dodaj
                                    zastupnika</a>
                            </div>
                        </div>

                    </div>

                </div>
            </div>

            <div id="principal_representative_template" class="d-none">
                <div class="principal_representative_content">
                    <hr/>
                    <div class="row">
                        <div class="col-lg-12">
                            <strong><a class="btn btn-danger btn-sm float-right remove_principal_representative"><i
                                            class="fa fa-trash"></i> Ukloni</a></strong>
                        </div>
                    </div>
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, "{ID}[{INDEX}][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa opunomoćitelja') }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, "{ID}[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, "{ID}[{INDEX}][address]", null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, "{ID}[{INDEX}][city]", null, 'Grad/mjesto', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, "{ID}[{INDEX}][postal_code]", null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, "{ID}[{INDEX}][country]", null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, "{ID}[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o opunomoćeniku
                </div>
                <div class="card-body" id="agent_content">
                    <div class="maps_autofill_container">

                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'agent_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: Iva Ivić']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'agent_address', null, 'Adresa',
                                ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'agent_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'agent_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'agent_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'agent_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                {{ Form::fText($model, 'agent_principal_relationship', null, 'U kakvom je opunomoćenik odnosu s opunomoćiteljem? <small>(opcionalno)</small>', ['placeholder' => 'Npr: sestra']) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>


    </div>

    {{ Form::close() }}
@endsection

