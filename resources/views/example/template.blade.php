@if(!empty($parties))
    <div class="editable-segment" data-type="parties">
        {!! StringHelper::wordpressContent($parties) !!}
    </div>
@endif

@if($title)
    <h2 class="text-center editable-segment @if($type === 'statement' && !empty($body)) mb-0 @else mb-5 mt-5 @endif" data-type="title">
        {!! mb_strtoupper($title, 'UTF-8') !!}
    </h2>
@endif

@if($type === 'statement' && !empty($body))
    <div class="editable-segment" data-type="body">
        {!! StringHelper::wordpressContent($body) !!}

        @if(!empty($signees))
                @foreach($signees as $_signee)
                    <table style="table-layout: fixed; width: 100%; overflow: wrap;" autosize="1">
                        <tr class="avoid-page-break">
                            <td style="vertical-align:top; width: 50%;">
                                @if(!empty($_signee['left']))
                                    <div style="margin:0; padding: 0;">
                                        <br/><br/><br/><br/><br/><br/><br/><br/>
                                    </div>
                                    <span class="signature-line">_____________________________________________</span><br>
                                    <small>{!! nl2br($_signee['left']) !!}</small>
                                    <br/>
                                @endif
                            </td>
                            <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                                @if(!empty($_signee['right']))
                                    <div style="margin:0; padding: 0;">
                                        <br/><br/><br/><br/><br/><br/><br/><br/>
                                    </div>
                                    <span class="signature-line">_____________________________________________</span><br>
                                    <small>{!! nl2br($_signee['right']) !!}</small>
                                    <br/>
                                @endif
                            </td>
                        </tr>
                    </table>
                @endforeach
        @endif
    </div>
@endif

@if($type === 'contract' && !empty($articles))
    @foreach($articles as $_i => $_article)
        @if($_i == (count($articles)-1) && !empty($signees))
            <div class="editable-segment" data-type="article" data-description="{!! !empty($_article['description']) ?  StringHelper::wordpressContent($_article['description']) : '' !!}">
                <p class="article-header">Članak {{ $_i+1 }}. {!! $_article['title'] !!}</p>
                <div class="article-body">
                    <div class="stick-to-header">
                        {!!
                            StringHelper::enumerateParagraphs(
                                StringHelper::wordpressContent($_article['body']),
                                $_i+1
                            )
                        !!}

                        @foreach($signees as $_signee_i => $_signee)
                            <table style="table-layout: fixed; width: 100%; overflow: wrap;" autosize="1">
                                <tr class="avoid-page-break">
                                    <td style="vertical-align:top; width: 50%;">
                                        @if(!empty($_signee['left']))
                                            <div style="margin:0; padding: 0;">
                                                <br/><br/><br/><br/><br/><br/><br/><br/>
                                            </div>
                                            <span class="signature-line">_____________________________________________</span><br>
                                            <small>{!! nl2br($_signee['left']) !!}</small>
                                            <br/>
                                        @endif
                                    </td>
                                    <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                                        @if(!empty($_signee['right']))
                                            <div style="margin:0; padding: 0;">
                                                <br/><br/><br/><br/><br/><br/><br/><br/>
                                            </div>
                                            <span class="signature-line">_____________________________________________</span><br>
                                            <small>{!! nl2br($_signee['right']) !!}</small>
                                            <br/>
                                        @endif
                                    </td>
                                </tr>
                            </table>

                @if($_signee_i == 0)
                    </div>
                @endif

                    @endforeach
                </div>
            </div>
        @else
            <div class="editable-segment" data-type="article" data-description="{!! !empty($_article['description']) ?  StringHelper::wordpressContent($_article['description'])  : '' !!}">
                <p class="article-header">Članak {{ $_i+1 }}. {!! $_article['title'] !!}</p>
                <div class="article-body">
                    {!!
                          StringHelper::enumerateParagraphs(
                              StringHelper::wordpressContent($_article['body']),
                              $_i+1
                          )
                    !!}
                </div>
            </div>
        @endif
    @endforeach
@endif

@if(!empty($attachments) || !empty($recipients) || !empty($legal_remedy))
    <div id="supplementaries-container" class="mt-5">
        @if(!empty($attachments))
            <div id="attachments-container">
                <div class="@if(!empty($recipients) || !empty($legal_remedy)) mb-5 @endif">
                    <p><strong>U privitku:</strong></p>
                    <ul>
                        @foreach($attachments as $_attachment)
                            <li><span class="attachment">{!! $_attachment['attachment'] !!}</span></li>
                        @endforeach
                    </ul>
                </div>
            </div>
        @endif

        @if(!empty($recipients))
            <div id="recipients-container">
                <div class="@if(!empty($legal_remedy)) mb-5 @endif">
                    <p><strong>Dostaviti:</strong></p>
                    <ul>
                        @foreach($recipients as $_recipient)
                            <li><span class="recipient">{!! $_recipient['recipient'] !!}</span></li>
                        @endforeach
                    </ul>
                </div>
            </div>
        @endif

        @if(!empty($legal_remedy))
            <div id="legal-remedy-container">
                <p><strong>Uputa o pravnom lijeku:</strong></p>
                <div id="legal-remedy">{!! nl2br($legal_remedy) !!}</div>
            </div>
        @endif
    </div>
@endif