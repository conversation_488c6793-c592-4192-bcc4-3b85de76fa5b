<div id="signatures-segment" class="editable-segment" data-type="article">
    <p class="article-header">
        <PERSON><PERSON>k {{ $builder->getArticleIndex($dynamic_index++, true) }}. Potpisi
    </p>

    <div class="article-body">

        <div class="stick-to-header">
            <p>
                {{ $builder->getCurrentArticleIndex() }}.1.
                {{ $text }}
            </p>

            <p style="padding-top:10px;">
                {!! $data['contract_place']  !!}, {!! StringHelper::dateToText($data['contract_date'], $builder::$placeholder) !!} godine.
            </p>

            @if(count($left_parties) + count($right_parties))
                @for($i = 0; $i < (count($left_parties) + count($right_parties)); $i++)

                    @if(!empty($left_parties[$i]) && !empty($right_parties[$i]))

                        @if($i==0)

                            <table style="table-layout: fixed; width: 100%;">
                                <tr class="avoid-page-break">
                                    <td style="vertical-align:top; width: 50%;">
                                        <div style="margin:0; padding: 0;">
                                            <img alt="signature" src="{L{{ $i }}}">
                                        </div>
                                        <span class="signature-line">_____________________________________________</span><br>
                                        <small>{!! $builder->getPartyLabel($left_parties[$i], $default_party_label_left) !!}</small>
                                        <br/>
                                    </td>
                                    <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                                        <div style="margin:0; padding: 0;">
                                            <img alt="signature" src="{R{{ $i }}}">
                                        </div>
                                        <span class="signature-line">_____________________________________________</span><br>
                                        <small>{!! $builder->getPartyLabel($right_parties[$i], $default_party_label_right) !!}</small>
                                        <br/>
                                    </td>
                                </tr>
                            </table>
        </div>
                        @else
                            <table style="table-layout: fixed; width: 100%;">
                                <tr class="avoid-page-break">
                                    <td style="vertical-align:top; width: 50%;">
                                        <div style="margin:0; padding: 0;">
                                            <img alt="signature" src="{L{{ $i }}}">
                                        </div>
                                        <span class="signature-line">_____________________________________________</span><br>
                                        <small>{!! $builder->getPartyLabel($left_parties[$i], $default_party_label_left) !!}</small>
                                        <br/>
                                    </td>
                                    <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                                        <div style="margin:0; padding: 0;">
                                            <img alt="signature" src="{R{{ $i }}}">
                                        </div>
                                        <span class="signature-line">_____________________________________________</span><br>
                                        <small>{!! $builder->getPartyLabel($right_parties[$i], $default_party_label_right) !!}</small>
                                        <br/>
                                    </td>
                                </tr>
                            </table>
                        @endif

                    @else
                        @if($i==0)

                            @if(!empty($left_parties[$i]))
                                <table style="table-layout: fixed; width: 100%;">
                                    <tr class="avoid-page-break">
                                        <td style="vertical-align:top; width: 50%;">
                                            <div style="margin:0; padding: 0;">
                                                <img alt="signature" src="{L{{ $i }}}">
                                            </div>
                                            <span class="signature-line">_____________________________________________</span><br>
                                            <small>{!! $builder->getPartyLabel($left_parties[$i], $default_party_label_left) !!}</small>
                                            <br/>
                                        </td>
                                        <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                                        </td>
                                    </tr>
                                </table>
                            @endif

                            @if(!empty($right_parties[$i]))
                                <table style="table-layout: fixed; width: 100%;">
                                    <tr class="avoid-page-break">
                                        <td style="vertical-align:top; width: 50%;"></td>
                                        <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                                            <div style="margin:0; padding: 0;">
                                                <img alt="signature" src="{R{{ $i }}}">
                                            </div>
                                            <span class="signature-line">_____________________________________________</span><br>
                                            <small>{!! $builder->getPartyLabel($right_parties[$i], $default_party_label_right) !!}</small>
                                            <br/>
                                        </td>
                                    </tr>
                                </table>
                            @endif
        </div>
                        @else
                            @if(!empty($left_parties[$i]))
                                <table style="table-layout: fixed; width: 100%;">
                                    <tr class="avoid-page-break">
                                        <td style="vertical-align:top; width: 50%;">
                                            <div style="margin:0; padding: 0;">
                                                <img alt="signature" src="{L{{ $i }}}">
                                            </div>
                                            <span class="signature-line">_____________________________________________</span><br>
                                            <small>{!! $builder->getPartyLabel($left_parties[$i], $default_party_label_left) !!}</small>
                                            <br/>
                                        </td>
                                        <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                                        </td>
                                    </tr>
                                </table>
                            @endif

                            @if(!empty($right_parties[$i]))
                                <table style="table-layout: fixed; width: 100%;">
                                    <tr class="avoid-page-break">
                                        <td style="vertical-align:top; width: 50%;"></td>
                                        <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                                            <div style="margin:0; padding: 0;">
                                                <img alt="signature" src="{R{{ $i }}}">
                                            </div>
                                            <span class="signature-line">_____________________________________________</span><br>
                                            <small>{!! $builder->getPartyLabel($right_parties[$i], $default_party_label_right) !!}</small>
                                            <br/>
                                        </td>
                                    </tr>
                                </table>
                            @endif
                        @endif
                    @endif
                @endfor
            @else

                <table style="table-layout: fixed; width: 100%;">
                    <tr class="avoid-page-break">
                        <td style="vertical-align:top; width: 50%;">
                            <div style="margin:0; padding: 0;">
                                <img alt="signature" src="{L0}">
                            </div>
                            <span class="signature-line">_____________________________________________</span><br>
                            <small>{!! $default_party_label_left ?? "" !!}</small>
                            <br/>
                        </td>
                        <td style="vertical-align:top; padding-left: 5%; width: 50%;">
                            <div style="margin:0; padding: 0;">
                                <img alt="signature" src="{R0}">
                            </div>
                            <span class="signature-line">_____________________________________________</span><br>
                            <small>{!! $default_party_label_right ?? "" !!}</small>
                            <br/>
                        </td>
                    </tr>
                </table>
        </div>
            @endif
    </div>
</div>
