<nav class="fixed-top navbar navbar-expand-lg navbar-dark bg-dark" style="min-height: 65px;">
    <div class="container">
        <a class="navbar-brand" href="{{ URL::route('home') }}">Pravomat.hr</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

            <div class="row w-100">
                <div id="navbarNavDropdown" class="navbar-collapse collapse" style="padding-top:5px;">
                    <div class="col-lg-5 pr-lg-0 pl-lg-5">
                        <ul class="navbar-nav d-none d-lg-block">
                            <form class="w-100 mb-0">
                                <input id="search-document-placeholder" type="text" class="form-control" placeholder="Pretraži...">
                                <div id="search-document-container" style="display: none;">
                                    <select class="" id="search-document"></select>
                                </div>

                            </form>
                        </ul>
                    </div>
                    <div class="col-lg-7">
                        <ul class="navbar-nav float-lg-right">
                            @auth
                                <li class="divider-vertical"></li>
                                <li class="nav-item"><a class="nav-link {{ Route::is('wordpress.documents') ? 'active' : '' }}" href="{{ route('wordpress.documents') }}">{{ __('Obrasci') }}</a></li>
                                <li class="divider-vertical"></li>
                                <li class="nav-item"><a class="nav-link {{ Route::is('wordpress.examples') ? 'active' : '' }}" href="{{ route('wordpress.examples') }}">{{ __('Primjeri') }}</a></li>
                                <li class="divider-vertical"></li>
                                <li class="nav-item pr-lg-2"><a class="nav-link {{ Route::is('translation.custom.checkout') ? 'active' : '' }}" href="{{ route('translation.custom.checkout') }}">{{ __('Prijevod') }} <small><sup class="badge badge-success badge-pill">NOVO</sup></small></a></li>
                                {{--<li class="divider-vertical"></li>--}}
                                <li class="nav-item"><a class="nav-link {{ Route::is('contact') ? 'active' : '' }}" href="{{ route('contact') }}">{{ __('Kontakt') }}</a></li>
                                <li class="divider-vertical"></li>

                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle {{ (Route::is('user.dashboard') || Route::is('email.preferences.show') || Route::is('documents')) ? 'active' : '' }}" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        Moj račun
                                    </a>
                                    <div class="dropdown-menu" id="user-menu">
                                        <a class="dropdown-item" href="{{ route('user.dashboard') }}">{{ __('Moj profil') }}</a>
                                        <a class="dropdown-item" href="{{ route('documents') }}">{{ __('Moji dokumenti') }}</a>
                                        <a class="dropdown-item" href="{{ URL::signedRoute('email.preferences.show', ['email' => Auth::user()->email]) }}">{{ __('Postavke obavijesti') }}</a>
                                        @if(Auth::user()->isAdmin())
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item" href="{{ route('admin.dashboard') }}">{{ __('Admin sučelje') }}</a>
                                        @endif
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">{{ __('Odjava') }}</a>
                                    </div>
                                </li>

                                <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                    @csrf
                                </form>
                            @else
                                <li class="divider-vertical"></li>
                                <li class="nav-item"><a class="nav-link {{ Route::is('wordpress.documents') ? 'active' : '' }}" href="{{ route('wordpress.documents') }}">{{ __('Obrasci') }}</a></li>
                                <li class="divider-vertical"></li>
                                <li class="nav-item"><a class="nav-link {{ Route::is('wordpress.examples') ? 'active' : '' }}" href="{{ route('wordpress.examples') }}">{{ __('Primjeri') }}</a></li>
                                <li class="divider-vertical"></li>
                                <li class="nav-item pr-lg-2"><a class="nav-link {{ Route::is('translation.custom.checkout') ? 'active' : '' }}" href="{{ route('translation.custom.checkout') }}">{{ __('Prijevod') }} <small><sup class="badge badge-success badge-pill">NOVO</sup></small></a></li>
                                {{--<li class="divider-vertical"></li>--}}
                                <li class="nav-item"><a class="nav-link {{ Route::is('contact') ? 'active' : '' }}" href="{{ route('contact') }}">{{ __('Kontakt') }}</a></li>
                                <li class="divider-vertical"></li>
                                <li class="nav-item mt-lg-0 mt-2">
                                    <a class="btn btn-secondary btn-block" href="{{ route('login') }}">Prijavi se</a>
                                </li>
                            @endauth
                        </ul>
                    </div>
                </div>
            </div>

    </div>

</nav>
