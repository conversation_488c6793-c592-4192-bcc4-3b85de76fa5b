@extends('layouts.common.master')
@section('title', 'Izradi račun')
@section('description', 'I<PERSON>radi besplatni račun na Pravomatu i preuzmi svoje izrađene dokumente u PDF formatu')
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center">{{ __('Novi korisnik') }}</div>

                    <div class="card-body px-lg-5">
                        <form method="POST" action="{{ route('register') }}" aria-label="{{ __('Register') }}">
                            @csrf
                            {!!  GoogleReCaptchaV3::renderField('register_id','register') !!}
                            <!-- Name Field -->
                            <div class="form-group">
                                <label for="name">{{ __('Ime i prezime') }}</label>
                                <input id="name" type="text"
                                       class="form-control{{ $errors->has('name') ? ' is-invalid' : '' }}"
                                       name="name" value="{{ old('name') }}" required autofocus>
                                @if ($errors->has('name'))
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $errors->first('name') }}</strong>
                                    </span>
                                @endif
                            </div>

                            <!-- Email Field -->
                            <div class="form-group">
                                <label for="email">{{ __('Adresa e-pošte') }}</label>
                                <input id="email" type="email"
                                       class="form-control{{ $errors->has('email') ? ' is-invalid' : '' }}"
                                       name="email" value="{{ old('email') }}" required>
                                @if ($errors->has('email'))
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $errors->first('email') }}</strong>
                                    </span>
                                @endif
                            </div>

                            <!-- Password Fields -->
                            <div class="form-group">
                                <label for="password">{{ __('Lozinka') }}</label>
                                <input id="password" type="password"
                                       class="form-control{{ $errors->has('password') ? ' is-invalid' : '' }}"
                                       name="password" required>
                                @if ($errors->has('password'))
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $errors->first('password') }}</strong>
                                    </span>
                                @endif
                            </div>

                            <div class="form-group">
                                <label for="password-confirm">{{ __('Ponovi lozinku') }}</label>
                                <input id="password-confirm" type="password" class="form-control"
                                       name="password_confirmation" required>
                            </div>

                            <!-- Other Fields -->
                            <div class="form-group">
                                <div class="form-check">
                                    <input name="tos" required class="form-check-input" type="checkbox" value="" id="tos">
                                    <label class="form-check-label" for="tos">
                                        Potvrđujem da sam pročitao i da prihvaćam <a target="_blank" href="/pravne-napomene/uvjeti-koristenja">Uvjete korištenja</a> čiji sastavni dio su <a target="_blank" href="/pravne-napomene/ugovor-o-obradi-podataka">Ugovor o obradi podataka</a> i <a target="_blank" href="/pravne-napomene/pravila-o-privatnosti">Pravila o privatnosti.</a> Razumijem da je Pravomat isključivo računalni alat za samostalnu
                                        izradu pravnih dokumenata od strane korisnika i nije zamjena za
                                        odvjetničke usluge odnosno profesionalnu pravnu pomoć.
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="form-check">
                                    <input name="newsletter" class="form-check-input" type="checkbox" value="1" id="newsletter">
                                    <label class="form-check-label" for="newsletter">
                                        Želim od Pravomata povremeno primati poruke s novostima.
                                    </label>
                                </div>
                            </div>

                            <!-- Register Button -->
                            <div class="form-group text-center">
                                <button type="submit" class="btn btn-info btn-block">
                                    {{ __('Izradi račun') }}
                                </button>
                            </div>

                            <div class="form-group text-center mt-3">
                                <hr/>
                                ili
                                <div class="form-group mt-2">
                                    <a id="social-login" href="{{ route('auth.social.login', 'google') }}" class="btn btn-danger btn-block"><i class="fab fa-google"></i> Prijavi se s Google računom</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script type="text/javascript">
        $(function() {
            // on social login click, if tos is not checked, show alert
            $('#social-login').on('click', function(e) {
                const tosCheckbox = $('#tos').get(0);
                const newsletterCheckbox = $('#newsletter').get(0);
                const socialLoginLink = $(this);

                if (!tosCheckbox.checkValidity()) {
                    tosCheckbox.reportValidity();
                    e.preventDefault();
                }

                // append newsletter=1 parameter if the newsletter checkbox is checked
                if (newsletterCheckbox.checked) {
                    let currentHref = socialLoginLink.attr('href');
                    const url = new URL(currentHref, window.location.origin);
                    url.searchParams.set('newsletter', '1');
                    socialLoginLink.attr('href', url.toString());
                } else {
                    // else remove it
                    let currentHref = socialLoginLink.attr('href');
                    const url = new URL(currentHref, window.location.origin);
                    url.searchParams.delete('newsletter');
                    socialLoginLink.attr('href', url.toString());
                }
            });

        });
    </script>
@endpush