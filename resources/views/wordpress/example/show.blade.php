@extends('layouts.common.master')
@section('title', $example->post_title)
@section('description', $example->acf->excerpt)
@section('keywords', $example->acf->tags)

@push('styles')
    <style media="all">

        .article-header{
            font-weight: bold;
            margin-bottom: 10px!important;
            margin-top: 10px!important;
        }

        @media (max-width: 768px) {
            .signature-line {
                font-size: 0.3em!important;
            }

            .editable-segment{
                text-align: start;
                text-justify: auto;
            }
        }

        .signature-line {
            font-size: 0.85em;
        }

        .example-preview{
            border: 1px solid rgba(186, 186, 186, 0.32)
        }

        .editable-segment{
            padding: 20px;
        }

        #supplementaries-container{
            padding: 20px;
        }

    </style>
@endpush

@section('content')
    <div class="row">
        <div class="col">
            <div class="row">
                <div class="col-lg-12">
                    <h1 class="pb-4">{!! $example->post_title !!}</h1>
                    <div class="pb-4 d-xl-none d-lg-none">
                        <div class="card">
                            <div class="card-body">
                                <div class="nav flex-column">
                                    <div>
                                        <a class="nav-link btn btn-block btn-info" href="{{ route('wordpress.example.export', $example->ID) }}"><i class="fa fa-magic"></i> Uredi i preuzmi</a>
                                        @if($example->acf->related_examples->count())
                                            <a class="nav-link btn btn-block btn-secondary" href="{{ route('wordpress.examples', ['filter' => $example->ID]) }}">Slični primjeri</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if(!empty($intro))
                        <div class="alert alert-secondary example-intro" role="alert">
                            {!! StringHelper::wordpressContent($intro) !!}
                        </div>
                    @endif

                    @if($example->acf->related_document->count())
                        <div class="alert text-center">
                            Primjer Vam ne odgovara? Sastavite svoj dokument pomoću našeg <strong><a href="/{{ $example->acf->related_document->first()->slug }}">digitalnog obrasca.</a></strong>
                        </div>
                    @endif

                    <div class="site-content example-preview prevent-select mt-4 p-4 @guest fading-content @endguest">
                        @if(!empty($example->screenshot))
                            <img class="img-fluid" src="{{ $example->screenshot }}?id={{ microtime() }}" alt="{{ $example->post_title . '- screenshot' }}"/>
                        @else
                            <div id="example-content">
                                {!! $example->html !!}
                            </div>
                        @endif

                        @guest
                            <div class="fadeout"></div>
                        @endguest
                    </div>
                </div>
            </div>
            @guest
                <div class="signup-notice">
                    <a href="{{ route('redirect.to', ['url' => route('login'), 'intended' => \Illuminate\Support\Facades\URL::current()]) }}">Prijavite se</a>
                    ili
                    <a href="{{ route('register') }}">besplatno napravite račun</a> za pregled, izradu i preuzimanje dokumenta.
                </div>
            @endguest
        </div>
        <div class="bg-transparent col-lg-3 pl-lg-5">
            <div class="py-2 sticky-top sticky-offset">
                <div class="card">
                    <div class="card-header">
                        {!! $example->post_title !!}
                    </div>
                    <div class="card-body">
                        <div class="nav flex-column">
                            <div class="pb-2">
                                <a class="nav-link btn btn-block btn-info" href="{{ route('wordpress.example.export', $example->ID) }}"><i class="fa fa-magic"></i> Uredi i preuzmi</a>
                                @if($example->acf->related_examples->count())
                                    <a class="nav-link btn btn-block btn-secondary" href="{{ route('wordpress.examples', ['filter' => $example->ID]) }}">Slični primjeri</a>
                                @endif
                            </div>
                            <div class="document-details">
                                <span class="bold">Zadnja izmjena:</span> {{ $example->post_modified->format('d.m.Y.') }} <br/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
