@extends('layouts.tenant.master')

@section('title', 'Potvrda prijave')

@section('content')
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">{{ __('Potvrda prijave') }}</div>

                <div class="card-body p-4">
                    <p class="mb-3">{{ __('Poslali smo kod za potvrdu na Vašu e-mail adresu. Molimo unesite kod kako biste dovršili prijavu.') }}</p>

                    <form method="POST" action="{{ route('tenant.otp.verify') }}">
                        @csrf

                        <div class="form-group row">
                            <label for="otp" class="col-md-4 col-form-label text-md-right">{{ __('Kod za potvrdu') }}</label>

                            <div class="col-md-6">
                                <input id="otp" type="text" inputmode="numeric" pattern="[0-9]*" maxlength="6" class="form-control @error('otp') is-invalid @enderror" name="otp" required autocomplete="one-time-code">

                                @error('otp')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row mb-0">
                            <div class="col-md-6 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    {{ __('Potvrdi') }}
                                </button>
                            </div>
                        </div>
                    </form>
                    <div class="text-center mt-3">
                        <a href="{{ route('tenant.login') }}">{{ __('Natrag na prijavu') }}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection