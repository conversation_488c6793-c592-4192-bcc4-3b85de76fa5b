<div id="modal-feedback" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <form method="post" action="{{ route('feedback.post') }}">
                    <input id="feedback-view" type="hidden" name="view" value="{{ $view ?? '' }}">
                    <div class="form-group mt-4 text-center">
                        <div class="success-message pt-3 d-none">
                            <div class="alert alert-success">
                                Hvala na povratnoj informaciji!
                            </div>
                        </div>
                        <label style="font-size: 1.5em" class="bold heading">{{ $title }}</label>
                        <div class="rating">
                            <input type="radio" name="stars" value="5" id="5star"><label for="5star">☆</label>
                            <input type="radio" name="stars" value="4" id="4star"><label for="4star">☆</label>
                            <input type="radio" name="stars" value="3" id="3star"><label for="3star">☆</label>
                            <input type="radio" name="stars" value="2" id="2star"><label for="2star">☆</label>
                            <input type="radio" name="stars" value="1" id="1star"><label for="1star">☆</label>
                        </div>
                    </div>
                    <div class="form-group text-center">
                        <label class="bold">Kako možemo poboljšati Pravomat?</label>
                    </div>
                    <div class="form-group">
                        <textarea rows="4" placeholder="Unesi komentar..." name="message" class="form-control"></textarea>
                    </div>
                    <button type="button" data-dismiss="modal" class="btn btn-secondary btn-block d-none close-modal">Zatvori</button>
                    <button type="submit" class="btn btn-info btn-block">Pošalji</button>
                {{ Form::close() }}

            </div>

        </div>
    </div>
</div>

@push('scripts')
    <script type="text/javascript">
        $(function() {
            const feedbackModal = $('#modal-feedback');
            const feedbackModalForm = feedbackModal.find('form');

            feedbackModal.on('show.bs.modal', function (event) {
                $(this).find('.success-message').addClass('d-none');
                $(this).find('.close-modal').addClass('d-none');
                $(this).find('.heading').removeClass('d-none');
                $(this).find('textarea').attr('disabled', false);
                $(this).find(':submit').removeClass('d-none');
            });

            feedbackModalForm.submit(function(e){
                e.preventDefault();
                let view = $(this).find('#feedback-view').val();
                let url = $(this).attr('action');
                let message = $(this).find('textarea').val();
                let stars = $(this).find('input[name=stars]:checked').val();

                $.ajax({
                    type: "POST",
                    url: url,
                    data: {view: view, message: message, stars: stars, _token: $("[name='_token']").val()}
                });

                $(this).find('.success-message').removeClass('d-none');
                $(this).find('.close-modal').removeClass('d-none');
                $(this).find('.heading').addClass('d-none');
                $(this).find(':submit').addClass('d-none');
                $(this).find('textarea').attr('disabled', true);

            });
        });
    </script>
@endpush