@push('scripts')
    <script type="text/javascript">
        $(function() {

            let shouldShowTutorial = !isMobile();

            $('#documents-table').DataTable({
                pageLength: {{ auth()->user()->isAdmin() ? 6 : 8 }},
                searchDelay: 500,
                pagingType: isMobile() ? "simple" : "simple_numbers",
                language: {
                    url: "{{ env('APP_URL') }}/vendor/datatables/documents-croatian.json"
                },
                fixedHeader: true,
                serverSide: true,
                responsive: true,
                ajax: '{!! route('user.documents') !!}',
                columns: [
                    {data: 'id', name: 'documents.id', class: 'never'},
                    {data: 'title', name: 'title'},
                    {data: 'comment', name: 'comment', sortable: false},
                    {data: 'created_at', name: 'created_at'},
                    {
                        data: 'updated_at', name: 'updated_at', render: function(dateTime){
                            let date =  new Date(Date.parse(dateTime));

                            return date.toLocaleDateString("hr-HR", {
                                year: "numeric",
                                month: "2-digit",
                                day: "2-digit",
                            }).replace(/\s/g, '');
                        }
                    },
                    {
                        data: 'links', name: 'links', sortable: false, render: function(links){
                            return "<div class='documentDropdown dropdown text-center text-small-left'>\n" +
                                "  <button class='btn btn-info dropdown-toggle' type='button' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>\n" +
                                "    Odaberi\n" +
                                "  </button>\n" +
                                "  <div class='dropdown-menu'>\n" +
                                "    <a class='dropdown-item "+links['download']['disabled']+"' href='"+links['download']['url']+"'>Preuzmi</a>\n" +
                                "    <a class='dropdown-item "+links['edit']['disabled']+"' href='"+links['edit']['url']+"'>Izmijeni</a>\n" +
                                "    <a class='dropdown-item "+links['translate']['disabled']+"' href='"+links['translate']['url']+"'>Prevedi na engleski</a>\n" +
                                "    <a class='dropdown-item "+links['duplicate']['disabled']+"' href='"+links['duplicate']['url']+"'>Dupliciraj</a>\n" +
                                "    <a class='dropdown-item "+links['send']['disabled']+"' href='"+links['send']['url']+"'>Pošalji</a>\n" +
                                "    <a class='dropdown-item "+links['signatures']['disabled']+"' href='"+links['signatures']['url']+"'>e-Potpisi</a>\n" +
                                // (links['fork']['is_precontract'] ? "    <a class='dropdown-item "+links['fork']['disabled']+"' href='"+links['fork']['url']+"'>Generiraj ugovor</a>\n" : "") +
                                "    <a class='dropdown-item' data-toggle='modal' data-target='#modal-comment' data-id='"+links['comment']['id']+"' data-comment='"+links['comment']['comment']+"' href='#'>Uredi bilješku</a>\n" +
                                "    <a class='dropdown-item "+links['exportToEditor']['disabled']+"' href='"+links['exportToEditor']['url']+"'>Izvezi u uređivač</a>\n" +
                                "    <a data-message='Jeste li sigurni da želite izbrisati dokument?' class='dropdown-item please-confirm "+links['delete']['disabled']+"' href='"+links['delete']['url']+"'>Izbriši</a>\n" +
                                "  </div>\n" +
                                "</div>";
                        }
                    },
                ],
                bLengthChange: false,
                bInfo: false,
                bAutoWidth: false,
                columnDefs: [
                    {
                        targets: [5],
                        className: 'min-tablet',
                    },
                    {
                        targets: [2,3,4],
                        className: 'min-desktop',
                    },
                    {
                        targets: [0],
                        visible: false,
                    },
                    {
                        targets: [0,3,4,5],
                        searchable: false
                    },
                    {
                        targets: [0,1],
                        orderable: false
                    },
                    {
                        targets: [5],
                        width: "110px"
                    },
                ],
                order: [[4, 'desc']],
                drawCallback: (settings) => {

                    // hide pagination if only 1 page
                    let api = new $.fn.dataTable.Api(settings);
                    let pageInfo = api.page.info();

                    if (pageInfo.pages <= 1) {
                        $('.dataTables_paginate', api.table().container()).hide();
                    } else {
                        $('.dataTables_paginate', api.table().container()).show();
                    }

                    @if(!\Illuminate\Support\Facades\Auth::user()->options || !\Illuminate\Support\Facades\Auth::user()->options->is_document_tutorial_shown)
                    if(shouldShowTutorial && $('#documents-table').is(':visible') && $('.documentDropdown').first().length) {

                        $('#overlay').show();

                        $('.documentDropdown').first().css('z-index', 3).find('.dropdown-toggle').popover({
                            placement : 'left',
                            html : true,
                            title : 'Dokument je spremljen <a href="#" class="close hideTutorial" data-dismiss="alert">&times;</a>',
                            content : "Klikni ovdje ako želiš pregledati mogućnosti vezane uz dokument."
                        }).popover('show').on('shown.bs.popover', function (e) {

                            let context = $(this);

                            $('.hideTutorial').on('click', function(){
                                $('#overlay').hide();
                                context.popover('dispose');
                            })

                            shouldShowTutorial = false;

                            $.ajax({
                                url: '/user/documents/tutorial',
                                type: 'POST',
                                data: {
                                    _token: $("[name='_token']").val(),
                                }
                            });

                        }).on('click', function(e){
                            $(this).popover('dispose');
                            $('#overlay').hide();
                        });
                    }
                    @endif

                    // when dropdown open, attach popovers to options
                    $('.documentDropdown').each(function(index){
                        $(this).on('show.bs.dropdown', function () {

                            $(this).find('.dropdown-item').each(function(index){

                                let tooltip = "";

                                switch ($(this).text()) {
                                    case 'Izmijeni':
                                        tooltip = "Odaberi ovu opciju ako želiš izmijeniti ovaj dokument uređivanjem svojih odgovora i odabira u upitniku.";
                                        break;
                                    case 'Prevedi na engleski':
                                        tooltip = "Odaberi ovu opciju ako želiš zatražiti prijevod dokumenta na engleski jezik.";
                                        break;
                                    case 'Preuzmi':
                                        tooltip = "Odaberi ovu opciju ako želiš preuzeti ovaj dokument u PDF formatu.";
                                        break;
                                    case 'Dupliciraj':
                                        tooltip = "Odaberi ovu opciju ako želiš duplicirati ovaj dokument sa svim podacima radi lakšeg sastavljanja novog dokumenta iste vrste.";
                                        break;
                                    case 'Pošalji':
                                        tooltip = "Odaberi ovu opciju ako želiš poslati ovaj dokument u PDF formatu na jednu ili više adresa e-pošte.";
                                        break;
                                    case 'Izvezi u uređivač':
                                        tooltip = "Odaberi ovu opciju ako želiš izmijeniti ovaj dokument uređivanjem teksta u uređivaču.";
                                        break;
                                    case 'Preuzmi za solemnizaciju':
                                        tooltip = "Odaberi ovu opciju ako želiš preuzeti ovaj dokument u formatu prikladnom za solemnizaciju.";
                                        break;
                                    case 'e-Potpisi':
                                        tooltip = "Odaberi ovu opciju ako želiš jednostavnim elektroničkim potpisom potpisati ovaj dokument ili ga poslati na potpisivanje.";
                                        break;
                                    case 'Generiraj ugovor':
                                        tooltip = "Odaberi ovu opciju ako želiš na temelju ovog predugovora izraditi glavni ugovor";
                                        break;
                                    case 'Uredi bilješku':
                                        tooltip = 'Odaberi ovu opciju ako želiš u polje "Bilješka" u ovoj tablici dodati proizvoljnu napomenu.';
                                        break;
                                    case 'Izbriši':
                                        tooltip = "Odaberi ovu opciju ako želiš trajno izbrisati ovaj dokument.";
                                        break;
                                }

                                $(this).popover({
                                    animation: false,
                                    html : true,
                                    title: $(this).text(),
                                    content: tooltip,
                                    trigger: 'hover'

                                });

                                // show loader indicator for longer waiting periods
                                let checkingForDownload = null;

                                if($(this).text() === "Preuzmi") {

                                    $(this).off('click').on('click', function(e){

                                        // download response includes this cookie as an indicator for loader function
                                        Cookies.remove('download_started');

                                        let checkingCounter = 0,
                                            maxTries= 10;

                                        checkingForDownload = setInterval(function () {

                                            checkingCounter++;

                                            // if reached maxTries, or download_started cookie is set, exit
                                            if((checkingCounter > maxTries) || (typeof(Cookies.get('download_started')) !== 'undefined')) {
                                                $('#download-message').addClass('d-none');
                                                clearInterval(checkingForDownload);
                                            } else if ($('#download-message').hasClass('d-none')) {
                                                $('#download-message').removeClass('d-none'); // otherwise show loader
                                            }

                                        }, 500);
                                    })
                                }

                            });

                        });
                    })
                }

            });

            // comment modal
            const commentModal = $('#modal-comment');
            const commentModalForm = commentModal.find('form');

            commentModal.on('show.bs.modal', function (event) {
                // assign modal values
                let document = $(event.relatedTarget);

                $(this).find('form').attr('action', '/document/'+document.data('id')+'/comment');
                $(this).find('.modal-body textarea').val(document.data('comment'));
            });

            commentModalForm.submit(function(e){
                e.preventDefault();
                let url = $(this).attr('action');
                let comment = $(this).find('.modal-body textarea').val();

                $.ajax({
                    type: "POST",
                    url: url,
                    data: {comment: comment, _token: $("[name='_token']").val()},
                    success: () => {
                        $('#documents-table').DataTable().ajax.reload(null, false);
                        commentModal.modal('hide');
                    },
                });

            });
        });

        function isMobile() {
            return $(window).width() < 480;
        }
    </script>
@endpush