<!DOCTYPE html>
<html lang="en">
    <head>
        <title>{{ $invoice->name }}</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

        <style type="text/css" media="screen">
            html {
                font-family: sans-serif;
                line-height: 1.15;
                margin: 0;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                font-weight: 400;
                line-height: 1.5;
                color: #212529;
                text-align: left;
                background-color: #fff;
                font-size: 10px;
                margin: 36pt;
            }

            h4 {
                margin-top: 0;
                margin-bottom: 0.5rem;
            }

            p {
                margin-top: 0;
                margin-bottom: .75rem;
            }

            strong {
                font-weight: bolder;
            }

            img {
                vertical-align: middle;
                border-style: none;
            }

            table {
                border-collapse: collapse;
            }

            th {
                text-align: inherit;
            }

            h4, .h4 {
                margin-bottom: 0.5rem;
                font-weight: 500;
                line-height: 1.2;
            }

            h4, .h4 {
                font-size: 1.5rem;
            }

            .table {
                width: 100%;
                margin-bottom: 1rem;
                color: #212529;
            }

            .table th,
            .table td {
                padding: 0.75rem;
                vertical-align: top;
            }

            .table.table-items td {
                border-top: 1px solid #dee2e6;
            }

            .table thead th {
                vertical-align: bottom;
                border-bottom: 2px solid #dee2e6;
            }

            .mt-5 {
                margin-top: 3rem !important;
            }

            .pr-0,
            .px-0 {
                padding-right: 0 !important;
            }

            .pl-0,
            .px-0 {
                padding-left: 0 !important;
            }

            .text-right {
                text-align: right !important;
            }

            .text-center {
                text-align: center !important;
            }

            .text-uppercase {
                text-transform: uppercase !important;
            }
            * {
                font-family: "DejaVu Sans";
            }
            body, h1, h2, h3, h4, h5, h6, table, th, tr, td, p, div {
                line-height: 1.1;
            }
            .party-header {
                font-size: 1.5rem;
                font-weight: 400;
            }
            .total-amount {
                font-size: 12px;
                font-weight: 700;
            }
            .border-0 {
                border: none !important;
            }
            .cool-gray {
                color: #6B7280;
            }

            #fiscalization {
                margin-top: 50px;
            }

            #notice {
                padding-top: 50px;
            }
        </style>
    </head>

    <body>
        <table class="table">
            <tbody>
            <tr>
                <td class="border-0 pl-0" width="15%">
                    {{-- Header --}}
                    @if($invoice->logo)
                        <img src="{{ $invoice->getLogo() }}" alt="logo" height="75">
                    @endif
                </td>
                <td class="border-0 pl-0 cool-gray">
                    <div class="seller-name"><strong>{{ $invoice->seller->name }}</strong></div>
                    {{ __('invoices::invoice.address') }}: {{ $invoice->seller->address }}
                    <div>E-pošta: {{ $invoice->getCustomData()['support_email'] }}</div>
                    <div>OIB: 47272365387</div>
                    <div>MB: 1788507</div>
                </td>
                <td class="border-0 pl-0 cool-gray">
                    <div>PDV ID: HR47272365387</div>
                    <div>IBAN: *********************</div>
                    <div>MBS: 040189855 (Trgovački sud u Rijeci)</div>
                    <div>TK: 2.640,00 EUR (uplaćen u cijelosti)</div>
                    <div>Direktorica: Jasmina Mutabžija</div>
                </td>

            </tr>
        </table>

        <table class="table mt-5">
            <tbody>
                <tr>
                    <td class="border-0 pl-0" width="56%">
                        <h4 class="text-uppercase">
                            <strong>{{ $invoice->getCustomData()['receipt_title'] }}</strong>
                        </h4>
                        <br/>
                        @if(!$invoice->getCustomData()['is_payment_confirmation'])
                            <p>{{ __('invoices::invoice.receipt_serial') }}: <strong>{{ $invoice->getCustomData()['receipt_number'] }}</strong></p>
                            <p>{{ __('invoices::invoice.date') }}: <strong>{{ $invoice->getCustomData()['receipt_time'] }}</strong></p>
                            <p>{{ __('invoices::invoice.place') }}: <strong>{{ $invoice->getCustomData()['place'] }}</strong></p>
                            <p>{{ __('invoices::invoice.service_delivery_date') }}: <strong>{{ $invoice->getCustomData()['service_delivery_date'] }}</strong></p>
                            <p>Fakturirala: <strong>Jasmina Mutabžija</strong></p>
                        @else
                            <p>{{ __('invoices::invoice.payment_confirmation_serial') }}: <strong>{{ $invoice->getCustomData()['receipt_number'] }}</strong></p>
                            <p>{{ __('invoices::invoice.date') }}: <strong>{{ $invoice->getCustomData()['receipt_time'] }}</strong></p>
                            <p>{{ __('invoices::invoice.payment_method') }}: <strong>{{ $invoice->getCustomData()['payment_method'] }}</strong></p>
                        @endif
                    </td>
                    <td class="border-0 pl-0">
                        <div style="margin-top: 55px;">
                            <p class="order-number">
                                {{ __('invoices::invoice.order_number') }}: <strong>{{ $invoice->getCustomData()['order_number'] }}</strong>
                            </p>
                            @if($invoice->buyer->name)
                                <p class="buyer-name">
                                    {{ __('invoices::invoice.buyer') }}: {{ $invoice->buyer->name }}
                                </p>
                            @endif

                            @if($invoice->buyer->address)
                                <p class="buyer-address">
                                    {{ __('invoices::invoice.address') }}: {{ $invoice->buyer->address }}
                                </p>
                            @endif

                            @if($invoice->buyer->code)
                                <p class="buyer-code">
                                    {{ __('invoices::invoice.code') }}: {{ $invoice->buyer->code }}
                                </p>
                            @endif

                            @if($invoice->buyer->vat)
                                <p class="buyer-vat">
                                    {{ __('invoices::invoice.vat') }}: {{ $invoice->buyer->vat }}
                                </p>
                            @endif

                            @if($invoice->buyer->phone)
                                <p class="buyer-phone">
                                    {{ __('invoices::invoice.phone') }}: {{ $invoice->buyer->phone }}
                                </p>
                            @endif

                            @foreach($invoice->buyer->custom_fields as $key => $value)
                                <p class="buyer-custom-field">
                                    {{ ucfirst($key) }}: {{ $value }}
                                </p>
                            @endforeach
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>


        {{-- Table --}}
        <table class="table table-items">
            <thead>
                <tr>
                    <th scope="col" class="border-0 pl-0">{{ __('invoices::invoice.description') }}</th>
                    @if($invoice->hasItemUnits)
                        <th scope="col" class="text-center border-0">{{ __('invoices::invoice.units') }}</th>
                    @endif
                    <th scope="col" class="text-right border-0">{{ __('invoices::invoice.price') }}</th>
                    <th scope="col" width="75px" class="text-center border-0">{{ __('invoices::invoice.quantity') }}</th>
                    <th scope="col" class="text-right border-0 pr-0">{{ __('invoices::invoice.sub_total') }}</th>
                </tr>
            </thead>
            <tbody>
                {{-- Items --}}
                @foreach($invoice->items as $item)
                <tr>
                    <td class="pl-0">
                        {{ $item->title }}

                        @if($item->description)
                            <p class="cool-gray">{{ $item->description }}</p>
                        @endif
                    </td>
                    @if($invoice->hasItemUnits)
                        <td class="text-center">{{ $item->units }}</td>
                    @endif
                    <td class="text-right">
                        {{ $invoice->formatCurrency($item->price_per_unit) }}
                    </td>
                    <td class="text-center">{{ $item->quantity }}</td>
                    <td class="text-right pr-0">
                        {{ $invoice->formatCurrency($item->quantity * $item->price_per_unit) }}
                    </td>
                </tr>
                @endforeach
                {{-- Summary --}}
                <tr>
                    <td colspan="{{ 3 }}" class="border-0"></td>
                    <td class="text-right pl-0">{{ __('invoices::invoice.total_amount_net') }}</td>
                    <td class="text-right pr-0">
                        {{ $invoice->formatCurrency($invoice->getCustomData()['total_net']) }}
                    </td>
                </tr>
                <tr>
                    <td colspan="{{ 3 }}" class="border-0"></td>
                    <td class="text-right pl-0">{{ __('invoices::invoice.tax_rate') }}</td>
                    <td class="text-right pr-0">
                        {{ $invoice->getCustomData()['tax_rate'] }}
                    </td>
                </tr>
                <tr>
                    <td colspan="{{ 3 }}" class="border-0"></td>
                    <td class="text-right pl-0">{{ __('invoices::invoice.total_taxes') }}</td>
                    <td class="text-right pr-0">
                        {{ $invoice->formatCurrency($invoice->total_taxes) }}
                    </td>
                </tr>
                @if(false && $invoice->shipping_amount !== null)
                    <tr>
                        <td colspan="{{ 3 }}" class="border-0"></td>
                        <td class="text-right pl-0">{{ __('invoices::invoice.shipping') }}</td>
                        <td class="text-right pr-0">
                            {{ $invoice->formatCurrency($invoice->shipping_amount) }}
                        </td>
                    </tr>
                @endif
                <tr>
                    <td colspan="{{ 3 }}" class="border-0"></td>
                    <td class="text-right pl-0">{{ __('invoices::invoice.total_amount') }}</td>
                    <td class="text-right pr-0 total-amount">
                        {{ $invoice->formatCurrency($invoice->total_amount) }}
                    </td>
                </tr>
            </tbody>
        </table>

        @if(!$invoice->getCustomData()['is_payment_confirmation'] && !empty($invoice->getCustomData()['jir']) && !empty($invoice->getCustomData()['zki']))
            <div id="fiscalization">
                <p>{{ __('invoices::invoice.payment_method') }}: <strong>{{ $invoice->getCustomData()['payment_method'] }}</strong></p>
                <p>JIR: <strong>{{ $invoice->getCustomData()['jir'] }}</strong></p>
                <p>ZKI: <strong>{{ $invoice->getCustomData()['zki'] }}</strong></p>
                <img style="margin-left: -15px;" src="{{ $invoice->getCustomData()['qr'] }}" alt="qr">
            </div>
        @endif

        @if($invoice->getCustomData()['is_payment_confirmation'])
            <div id="notice">
                <strong>Napomena:</strong> Potvrđujemo da je Vaša kartica uspješno predautorizirana za navedeni iznos. Teretit ćemo karticu i izdati račun za terećeni iznos kada prijevod bude spreman za isporuku.
            </div>
        @endif

        <script type="text/php">
            if (isset($pdf) && $PAGE_COUNT > 1) {
                $text = "{{ __('invoices::invoice.page') }} {PAGE_NUM} / {PAGE_COUNT}";
                $size = 10;
                $font = $fontMetrics->getFont("Verdana");
                $width = $fontMetrics->get_text_width($text, $font, $size) / 2;
                $x = ($pdf->get_width() - $width);
                $y = $pdf->get_height() - 35;
                $pdf->page_text($x, $y, $text, $font, $size);
            }
        </script>
    </body>
</html>
