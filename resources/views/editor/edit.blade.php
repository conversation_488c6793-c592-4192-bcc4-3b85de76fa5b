@extends('layouts.editor.master')

@section('title', 'Uređ<PERSON>č')
@section('description', 'Besplatno izradi i preuzmi ugovor o radu, ugovor o najmu, kupoprodajni ugovor i ostale pravne dokumente u svega par minuta koristeći prilagodljive digitalne predloške.')

@section('content')

    <div id="alert-widget">
        <div class="alert alert-success alert-dismissible fade hide" role="alert">
            Uspješno ste izbrisali članak <a href="" id="restore-article">(poništi)</a>
            <button type="button" id="close-alert-widget" class="close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    </div>

    @if(auth()->user()->shouldShowEditorTutorial())
        <!-- Tutorial modal -->
        <div class="modal fade" id="tutorialModal" tabindex="-1" role="dialog" aria-labelledby="tutorialModalTitle"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="tutorialModalLongTitle">Dobro došli u uređivač!</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>
                            Ovdje možete <strong>detaljno uređivati dokumente</strong> koje ste izradili pomoću obrazaca
                            ili
                            primjere koje ste uvezli u uređivač. </p>

                        <p>Ako se radi o ugovoru, možete:</p>
                        <ul>
                            <li>mijenjati i dodavati podatke o ugovornim strankama</li>
                            <li>mijenjati naslov ugovora, naslove i tekst svih članaka</li>
                            <li>mijenjati redoslijed članaka</li>
                            <li>brisati postojeće članke</li>
                            <li>dodavati nove članke gdje god to želite</li>
                            <li>dodavati ili brisati potpisnike</li>
                        </ul>

                        <p>Ako se radi o punomoći, izjavi ili sličnom dokumentu, možete:</p>
                        <ul>
                            <li>mijenjati naziv dokumenta</li>
                            <li>mijenjati tekst dokumenta</li>
                            <li>dodavati ili brisati potpisnike</li>
                        </ul>

                        <p>
                            Kada budete potpuno zadovoljni tekstom dokumenta koji uređujete, možete spremiti promjene i
                            pomoću funkcije "Preuzmi" <strong>preuzeti svoj gotovi dokument u PDF-u.</strong>
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-info" data-dismiss="modal">Razumijem</button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Article modal -->
    <div class="modal fade" id="addArticleModal" tabindex="-1" role="dialog" aria-labelledby="addArticleModalTitle"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addArticleModalLabel">Dodaj novi članak</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="datatable-container table-responsive">
                        <table id="search-articles-table" class="table table-striped table-bordered table-hover" style="width:100%">
                            <thead>
                            <tr>
                                <th>Odaberi predložak</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="insert-empty-article" class="btn btn-info btn-block" data-dismiss="modal">
                        Umetni prazni članak
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Solemnization modal -->
    <div class="modal fade" id="solemnizationModal" tabindex="-1" role="dialog"
         aria-labelledby="solemnizationModalTitle"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tutorialModalLongTitle">Solemnizacija dokumenta</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Označavanjem kvačice prazna mjesta u tekstu popunit će se crtama, u skladu s člankom 43. stavkom
                        (2) Zakona o javnom bilježništvu.</p>
                    <p><strong>Potvrda (solemnizacija)</strong> je postupak u kojem privatna isprava dobiva snagu
                        javnobilježničkog akta. U tom postupku javni bilježnik, između ostalog, ispituje jesu li stranke
                        sposobne i ovlaštene za poduzimanje i sklapanje posla, objašnjava strankama smisao i posljedice
                        posla i uvjerava se u njihovu pravu i ozbiljnu volju.</p>
                    <p><strong>Taj postupak je skuplji i razlikuje se od postupka ovjere potpisa.</strong></p>
                    <p><strong>U pravilu pravne dokumente nije potrebno solemnizirati,</strong> no ponekad se za pojedine ugovore i druge
                        pravne dokumente traži javnobilježnička potvrda (solemnizacija). Takvi dokumenti su, primjerice,
                        ugovor o darovanju nekretnine bez stvarne predaje nekretnine u posjed obdareniku, ugovor o
                        doživotnom uzdržavanju i ugovori koji sadrže ovršnu klauzulu.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" data-dismiss="modal">Razumijem</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Save modal -->
    <div class="modal fade" id="saveChangesModal" tabindex="-1" role="dialog" aria-labelledby="saveChangesModalTitle"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"
                        id="saveChangesModalLongTitle">{{ !$draft->is_visible ? 'Spremi dokument' : 'Spremi promjene' }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>
                        Želite li spremiti promjene i nastaviti s radom ili želite spremiti dokument i završiti s radom?
                    </p>
                </div>
                <div class="modal-footer">
                    <a class="btn btn-default" id="save-and-continue">Spremi i nastavi</a>
                    <a class="btn btn-info" id="save-and-exit">Spremi i završi</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Spellcheck modal -->
    <div class="modal fade" id="spellcheckModal" tabindex="-1" role="dialog" aria-labelledby="spellcheckModalTitle"
         aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="spellcheckModalLongTitle">Provjera pravopisa</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">Pravopisne pogreške označene su crvenom bojom. Kliknite na pojedinu
                        pogrešku za više informacija i mogućnosti ispravka. Kada ste završili s ispravcima, kliknite na
                        "Spremi promjene".
                    </div>
                    <div id="spellchecked-content">

                    </div>
                </div>
                <div class="modal-footer">
                    <a class="btn btn-default" data-dismiss="modal">Odustani</a>
                    <a class="btn btn-info" id="apply-spellcheck">Spremi promjene</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Spellcheck no errors modal -->
    <div class="modal fade" id="spellcheckNoErrorsModal" tabindex="0" role="dialog"
         aria-labelledby="spellcheckNoErrorsModalTitle"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="spellcheckNoErrorsModalLongTitle">Provjera pravopisa</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Nemate pravopisnih pogrešaka.
                </div>
                <div class="modal-footer">
                    <a class="btn btn-info" data-dismiss="modal">U redu</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="row">
                <div class="col-lg-12">
                    <div id="editor">
                        @foreach($parser->getSegments() as $_segment)
                            <div class="segment-container mb-2 @if($draft->isStatement() && $_segment['type'] == 'title') mt-3 @endif">
                                @if($_segment['type'] == 'article')
                                    <div class="segment-toolbar text-right mb-1">
                                        @if($draft->isContract())
                                            <div class="segment-toolbar-options">
                                                <div style="display: inline-block">
                                                    <ul>
                                                        <li>
                                                            <a class="btn btn-xs move-down" title="Pomakni dolje"><i
                                                                        class="fa fa-arrow-down"></i></a>
                                                        </li>
                                                        <li>
                                                            <a class="btn btn-xs move-up" title="Pomakni gore"><i
                                                                        class="fa fa-arrow-up"></i></a>
                                                        </li>
                                                        <li>
                                                            <a class="btn btn-xs btn-danger delete-article"
                                                               title="Ukloni ovaj članak"><i
                                                                        class="fa fa-trash"></i></a>
                                                        </li>
                                                        <li>
                                                            <a class="btn btn-xs btn-info add-article"
                                                               title="Dodaj novi članak ispod"><i
                                                                        class="fa fa-plus"></i></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                                @if(!empty($_segment['description']))
                                                    <div style="display: inline-block; margin-left: 5px">
                                                        <span data-toggle="tooltip"
                                                              data-original-title="{!! $_segment['description'] !!}">
                                                            <i class="fa fa-info-circle"
                                                               style="vertical-align: middle;"></i>
                                                        </span>
                                                    </div>
                                                @endif
                                            </div>

                                        @endif
                                    </div>
                                @elseif($_segment['type'] == 'title')
                                    @if($draft->isContract())
                                        <div class="segment-toolbar text-right mb-1">
                                            <div>
                                                <ul class="segment-toolbar-options">
                                                    <li>
                                                        <a class="btn btn-xs btn-info add-article"
                                                           title="Dodaj novi članak ispod"><i
                                                                    class="fa fa-plus"></i></a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    @endif
                                @endif

                                {!! $_segment['html']  !!}
                            </div>
                        @endforeach
                    </div>

                    <nav id="editor-tabs" class="mt-4">
                        <div class="nav nav-tabs" id="nav-tab" role="tablist">
                            <a class="nav-item nav-link active" id="nav-signees-tab" data-toggle="tab"
                               href="#nav-signees" role="tab" aria-controls="nav-signees" aria-selected="true">Potpisnici</a>
                            <a class="nav-item nav-link" id="nav-attachments-tab" data-toggle="tab"
                               href="#nav-attachments" role="tab" aria-controls="nav-attachments" aria-selected="false">Priloženi
                                dokumenti <span id="attachments-count">{{ !empty($parser->getAttachments()) ? ('(' . count($parser->getAttachments()) . ')') : '' }}</span>
                            </a>
                            @if($draft->isStatement())
                                <a class="nav-item nav-link" id="nav-receipients-tab" data-toggle="tab"
                                   href="#nav-recipients" role="tab" aria-controls="nav-recipients" aria-selected="false">Dostaviti <span id="recipients-count">{{ !empty($parser->getRecipients()) ? ('(' . count($parser->getRecipients()) . ')') : '' }}</span>
                                </a>
                                <a class="nav-item nav-link" id="nav-receipients-tab" data-toggle="tab"
                                   href="#nav-legal-remedy" role="tab" aria-controls="nav-legal-remedy" aria-selected="false">Pravni lijek <span id="legal-remedy-count">{{ !empty($parser->getLegalRemedy()) ? ('(1)') : '' }}</span>
                                </a>
                            @endif
                        </div>
                    </nav>
                    <div class="tab-content" id="nav-tabContent">
                        <div class="tab-pane fade show active" id="nav-signees" role="tabpanel"
                             aria-labelledby="nav-signees-tab">
                            <div id="signees-container" class="mt-4">
                                <table id="signees-table" class="table">
                                    <thead>
                                    <tr>
                                        <th>Potpisnik <span data-toggle="tooltip"
                                                            data-original-title="U polje ispod možeš upisati tekst koji će se pojaviti ispod potpisne crte za svakog potpisnika."><i
                                                        class="fa fa-info-circle"></i></span></th>
                                        <th style="min-width:100px;">Strana <span data-toggle="tooltip"
                                                                                  data-original-title="Pomoću padajućeg izbornika ispod možeš odabrati s koje će strane na zadnjem listu dokumenta biti potpisna crta za svakog potpisnika."><i
                                                        class="fa fa-info-circle"></i></span></th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @if(!empty($parser->getSignees()))
                                        @foreach($parser->getSignees() as $_signee)
                                            <tr class="signee-data">
                                                <td>
                                                    <textarea placeholder="Upiši ime potpisnika..."
                                                              class="form-control signee-label"
                                                              type="text">{!! str_replace('<br>', "\r", $_signee['signee'] ); !!}</textarea>
                                                </td>
                                                <td>
                                                    <select class="form-control signee-side">
                                                        <option {{ $_signee['side'] == 'left' ? 'selected' : null }} value="left">
                                                            Lijeva
                                                        </option>
                                                        <option {{ $_signee['side'] == 'right' ? 'selected' : null }} value="right">
                                                            Desna
                                                        </option>
                                                    </select>
                                                </td>
                                                <td class="text-center">
                                                    <a class="btn btn-danger btn-sm remove-signee"
                                                       title="Ukloni potpisnika"><i class="fa fa-trash"></i></a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr class="signee-data">
                                            <td>
                                                <textarea placeholder="Upiši ime potpisnika..."
                                                          class="form-control signee-label" type="text"></textarea>
                                            </td>
                                            <td>
                                                <select class="form-control signee-side">
                                                    <option value="left">Lijeva</option>
                                                    <option value="right">Desna</option>
                                                </select>
                                            </td>
                                            <td class="text-center">
                                                <a class="btn btn-danger btn-sm remove-signee"
                                                   title="Ukloni potpisnika"><i class="fa fa-trash"></i></a>
                                            </td>
                                        </tr>
                                    @endif
                                    </tbody>
                                </table>
                                <a class="btn btn-info" id="add-signee">+ Dodaj potpisnika</a>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="nav-attachments" role="tabpanel"
                             aria-labelledby="nav-attachments-tab">
                            <div id="attachments-container" class="mt-4">
                                <table id="attachments-table" class="table">
                                    <thead>
                                    <tr>
                                        <th>Priloženi dokument <span data-toggle="tooltip"
                                                                      data-original-title="Ovdje možeš navesti sve dokumente koje prilažeš u privitku."><i
                                                        class="fa fa-info-circle"></i></span></th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @if(!empty($parser->getAttachments()))
                                        @foreach($parser->getAttachments() as $_attachment)
                                            <tr class="attachment-data">
                                                <td>
                                                    <input type="text" placeholder="Upiši naziv priloženog dokumenta..."
                                                           class="form-control attachment-label"
                                                           value="{!! $_attachment !!}"/>
                                                </td>
                                                <td class="text-center">
                                                    <a class="btn btn-danger btn-sm remove-attachment"
                                                       title="Ukloni prilog"><i class="fa fa-trash"></i></a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr class="attachment-data">
                                            <td>
                                                <input type="text" placeholder="Upiši naziv priloženog dokumenta..."
                                                       class="form-control attachment-label"/>
                                            </td>
                                            <td class="text-center">
                                                <a class="btn btn-danger btn-sm remove-attachment"
                                                   title="Ukloni prilog"><i class="fa fa-trash"></i></a>
                                            </td>
                                        </tr>
                                    @endif
                                    </tbody>
                                </table>
                                <a class="btn btn-info" id="add-attachment">+ Dodaj dokument</a>
                            </div>
                        </div>
                        @if($draft->isStatement())
                            <div class="tab-pane fade" id="nav-recipients" role="tabpanel"
                                 aria-labelledby="nav-recipients-tab">
                                <div id="recipients-container" class="mt-4">
                                    <table id="recipients-table" class="table">
                                        <thead>
                                        <tr>
                                            <th>Primatelj <span data-toggle="tooltip"
                                                                data-original-title="Ovdje možeš navesti sve primatelje za ovaj dokument."><i
                                                            class="fa fa-info-circle"></i></span></th>
                                            <th></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @if(!empty($parser->getRecipients()))
                                            @foreach($parser->getRecipients() as $_recipient)
                                                <tr class="recipient-data">
                                                    <td>
                                                        <input type="text" placeholder="Upiši naziv primatelja..."
                                                               class="form-control recipient-label"
                                                               value="{!! $_recipient !!}"/>
                                                    </td>
                                                    <td class="text-center">
                                                        <a class="btn btn-danger btn-sm remove-recipient"
                                                           title="Ukloni prilog"><i class="fa fa-trash"></i></a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr class="recipient-data">
                                                <td>
                                                    <input type="text" placeholder="Upiši naziv primatelja..."
                                                           class="form-control recipient-label"/>
                                                </td>
                                                <td class="text-center">
                                                    <a class="btn btn-danger btn-sm remove-recipient"
                                                       title="Ukloni prilog"><i class="fa fa-trash"></i></a>
                                                </td>
                                            </tr>
                                        @endif
                                        </tbody>
                                    </table>
                                    <a class="btn btn-info" id="add-recipient">+ Dodaj primatelja</a>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="nav-legal-remedy" role="tabpanel"
                                 aria-labelledby="nav-legal-remedy-tab">
                                <div id="legal-remedy-container" class="mt-4">
                                    <label for="legal-remedy">
                                        Pravni lijek <span data-toggle="tooltip" data-original-title="Ovdje možeš upisati uputu o pravnom lijeku za ovaj dokument."><i class="fa fa-info-circle"></i></span>
                                    </label>
                                    <div class="form-group">
                                        <textarea id="legal-remedy" rows="4" class="form-control remedy-data" placeholder="Upiši uputu o pravnom lijeku...">{!! $parser->getLegalRemedy() !!}</textarea>

                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>

                    <div class="pb-4 d-xl-none d-lg-none">
                        @if(!empty($widget))
                            <div class="card">
                                <div class="card-body">
                                    <div class="nav flex-column">
                                        <form class="pb-2" method="post"
                                              action="{{ route('editor.draft.save', $draft) }}">
                                            @csrf
                                            <input type="hidden" id="html" name="html"/>
                                            <a class="btn btn-block btn-info" data-toggle="modal"
                                               data-target="#saveChangesModal">{{ !$draft->is_visible ? 'Spremi dokument' : 'Spremi promjene' }}</a>
                                        </form>
                                        <a class="btn btn-block btn-secondary" href="{{ url()->previous() }}">Odbaci
                                            promjene</a>
                                        <div class="text-center">
                                            <div class="spinner-border spinner-border-sm text-info spellcheck-spinner d-none"
                                                 role="status">
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                            <button class="btn btn-link spellcheck"><small>Provjeri pravopis</small>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-transparent col-lg-3 pl-lg-5">
            <div class="py-3 sticky-top sticky-offset-100">
                <div class="card">
                    <div class="card-header">{{ $draft->title }}</div>
                    <div class="card-body">
                        <form id="save-document-form" class="pb-2" method="post"
                              action="{{ route('editor.draft.save', $draft) }}">
                            @csrf
                            <div class="nav flex-column mb-3">
                                <div class="form-check">
                                    <input @if($draft->render_type_id === \App\Models\DocumentDraft::$render_types['solemnization']) checked
                                           @endif name="solemnization" class="form-check-input" type="checkbox"
                                           id="solemnization">
                                    <label class="form-check-label" for="solemnization" style="font-size: .9rem;">Dokument
                                        je potrebno potvrditi (solemnizirati) po javnom bilježniku
                                        <a title="Klikni za pojašnjenje" id="show-solemnization-modal" href="#"><i
                                                    class="fa fa-question-circle"></i></a>
                                    </label>
                                </div>
                            </div>
                            <hr/>
                            <div class="nav flex-column">
                                <input type="hidden" id="html" name="html"/>
                                <a class="btn btn-block btn-info" data-toggle="modal"
                                   data-target="#saveChangesModal">{{ !$draft->is_visible ? 'Spremi dokument' : 'Spremi promjene' }}</a>
                                <a class="btn btn-block btn-secondary please-confirm" href="{{ url()->current() }}"
                                   data-message="Jeste li sigurni da želite odbaciti sve promjene?">Odbaci promjene</a>
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-info spellcheck-spinner d-none"
                                         role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <button type="button" class="btn btn-link spellcheck"><small>Provjeri
                                            pravopis</small></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function () {
            // open solemnization modal
            $("#show-solemnization-modal").click(function (e) {
                e.preventDefault();
                $("#solemnizationModal").modal("show");
                document.cookie = "solemnizationModalShown=true";
            });

            $("#solemnization").on("change", function () {
                if (this.checked) {
                    if (document.cookie.indexOf("solemnizationModalShown=true") === -1) {
                        $("#solemnizationModal").modal("show");
                        document.cookie = "solemnizationModalShown=true";
                    }
                }
            });
        });
    </script>
@endpush

@if($post = $draft->getPost())
    @push('scripts')
        <script>
            // articles search
            $(document).ready(function () {
                let table = $('#search-articles-table').DataTable({
                    pageLength: 6,
                    dom: 'Bfrtip',
                    order: [],
                    searchDelay: 500,
                    pagingType: $(window).width() < 480 ? "simple" : "simple_numbers",
                    language: {
                        url: "{{ env('APP_URL') }}/vendor/datatables/articles-croatian.json"
                    },
                    fixedHeader: true,
                    serverSide: true,
                    responsive: true,
                    ajax: '{!! route('editor.articles', $post) !!}',
                    columns: [
                        {
                            data: 'title',
                            name: 'title',
                            sortable: false,
                            orderable: false,
                            render: function (data, type, row) {
                                let descriptionBlock = '';
                                if (row.description && row.description.trim() !== '') {
                                    descriptionBlock = '<span data-toggle="tooltip" data-original-title="' + row.description + '">' +
                                        '<i class="fa fa-info-circle"></i>' +
                                        '</span>';
                                }

                                return '<div>' +
                                    '<i class="fas fa-chevron-circle-down details-control"></i>' +
                                    '<span class="ml-2 mr-1 ">' + data + '</span>' +
                                    descriptionBlock +
                                    '<div class="ml-lg-4 article-post-title">' + row.post_title + '</div>' +
                                    '</div>';
                            }
                        },
                        {
                            data: null,
                            name: 'action',
                            sortable: false,
                            searchable: false,
                            width: "110px",
                            render: function () {
                                return '<div class="text-center"><button class="btn btn-info">Odaberi</button></div>';
                            }
                        }
                    ],
                    bLengthChange: false,
                    bInfo: false,
                    bAutoWidth: false,
                    columnDefs: [
                        {
                            targets: [1],
                            searchable: false,
                            width: "110px"
                        }
                    ],
                    drawCallback: function (settings) {
                        $('#search-articles-table [data-toggle="tooltip"]').tooltip({html: true})
                    },
                });

                // handle row click event
                $('#search-articles-table tbody').on('click', 'tr', function (e) {
                    // if not expand icon or button, click expand icon
                    if (!$(e.target).hasClass('details-control') && !$(e.target).hasClass('btn')) {
                        $(this).find('.details-control').click();
                    }
                });

                // on submit form, disable button
                $('#save-document-form').on('submit', function (e) {
                    $('#save-and-exit').addClass('disabled');
                    $('#save-and-continue').addClass('disabled');
                })
            });
        </script>
    @endpush
@endif
