<?php

namespace App\Console\Commands;

use App\Helpers\ElasticHelper;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Stancl\Tenancy\Tenancy;

class ElasticSearchSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:sync {index?} {id?} {--full}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Syncs templates/wordpress models with Elastic Search';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle() {
        $index = $this->argument('index');
        $id = $this->argument('id');
		$full = $this->option('full'); // sync central + tenant applications

        // Check if both arguments are provided
        if ($index !== null && $id !== null) {
            $this->syncPost($index, $id);
        } else if ($index !== null && $id === null) {
            // If "index" is provided and "id" is not, return an error
            $this->error('The "id" argument must be provided if the "index" argument is present.');
	        return Command::FAILURE;
        } else {
            $this->syncAll($full);
        }

	    return Command::SUCCESS;
    }

    private function syncPost($index, $id): void {
	    $es = new ElasticHelper();
        $methodName = "update" . ucfirst($index);
        $es->$methodName($id);
    }

    private function syncAll($full = false): void {

		if($full) {
			$es = new ElasticHelper();
			$es->restartIndices();

			$es->importTemplates();

			// on central application only
			if(!tenancy()->initialized) {
				$es->importDocuments();
				$es->importExamples();
				$es->importArticles();
			}
			// tenant applications
			foreach(Tenant::all() as $tenant) {
				// switch application context
				tenancy()->initialize($tenant);

				$es = new ElasticHelper();
				$es->restartIndices();

				$es->importTemplates();

				tenancy()->end();
			}
		} else {
			// central or tenant application
			$es = new ElasticHelper();
			$es->restartIndices();

			$es->importTemplates();

			// on central application only
			if(!tenancy()->initialized) {
				$es->importDocuments();
				$es->importExamples();
				$es->importArticles();
			}
		}
    }
}
