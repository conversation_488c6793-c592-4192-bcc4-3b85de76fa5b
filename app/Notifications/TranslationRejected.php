<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\EmailPreference;
use App\Models\Translation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class TranslationRejected extends Notification implements ShouldQueue
{
    use Queueable;

	public Translation $translation;
	public string $reason;

    /**
     * Create a new notification instance.
     */
    public function __construct(Translation $translation, string $reason)
    {
		$this->translation = $translation;
		$this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
	public function via($notifiable): array
	{
		return ['mail'];
	}

	public function shouldSkip($notifiable)
	{
		return !$this->translation->user || !EmailPreference::isAllowedGeneralEmails($this->translation->user->email);
	}

    /**
     * Build Mail Message
     */
	public function toMail($notifiable)
	{
		if($this->shouldSkip($notifiable)) {
			throw new SkipNotificationException();
		}

		$message = (new MailMessage)
			->subject('Narudžba prijevoda je odbijena')
			->greeting('Pozdrav ' . $this->translation->user->name . ',');

		$lines = str($this->reason)
		            ->replaceMatches('/\r\n|\r|\n/', "\n") // unify all newline types to "\n"
		            ->explode("\n")                       // split into collection of lines
		            ->filter(fn($line) => trim($line) !== ''); // remove empty lines

		foreach ($lines as $line) {
			$message->line($line);
		}

		$message->action('Pregledaj narudžbu', route('translation.show', $this->translation));

		$message->viewData = ['email_preferences_link' => URL::signedRoute('email.preferences.show', ['email' => $this->translation->user->email])];

		return $message;
	}



	/**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
