<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\Translation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class TranslationRequested extends Notification implements ShouldQueue
{
    use Queueable;

	private Translation $translation;
	/**
	 * Create a new notification instance.
	 */
	public function __construct(Translation $translation)
	{
		$this->translation = $translation;
	}

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

	public function shouldSkip($notifiable)
	{
		return !$this->translation || !$this->translation->user;
	}

    /**
     * Build Mail Message
     */
    public function toMail($notifiable)
    {
	    if($this->shouldSkip($notifiable)) {
		    throw new SkipNotificationException();
	    }

        $description = ($this->translation->isCertified() ? 'ovjereni prijevod dokumenta' : 'obični prijevod dokumenta') . ' (engleski ↔ hrvatski)';

		$user = $this->translation->user ? ('Korisnik ' . $this->translation->user->name) : 'Korisnik';

	    $message = (new MailMessage)
					->subject("Nova narudžba - $description")
                    ->line($user . ' je naručio prijevod dokumenta.')
                    ->action('Pogledaj narudžbu', route('translation.show.admin', $this->translation))
                    ->salutation('none');

		return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
