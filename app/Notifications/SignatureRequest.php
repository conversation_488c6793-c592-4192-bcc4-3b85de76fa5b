<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\EmailPreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class SignatureRequest extends Notification implements ShouldQueue
{
    use Queueable;

    private \App\Models\SignatureRequest $request;
    private string $email;

	/**
	 * SignatureRequest constructor.
	 *
	 * @param \App\Models\SignatureRequest $request
	 * @param string $email
	 */
    public function __construct(\App\Models\SignatureRequest $request, string $email)
    {
        $this->request = $request;
	    $this->email = $email;
    }

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

    public function shouldSkip($notifiable)
    {
	    return !$this->request->document || !$this->request->document->user || !EmailPreference::isAllowedGeneralEmails($this->email);
    }

    /**
     * Build Mail Message
     *
     * @param  mixed  $notifiable
     */
    public function toMail($notifiable)
    {
	    if($this->shouldSkip($notifiable)) {
		    throw new SkipNotificationException();
	    }

        $content = "<p>". $this->request->document->user->name. " Vas poziva da preko aplikacije Pravomat.hr pregledate i potpišete dokument: ".$this->request->document->template->public_title.".</p>";

        $message = (new MailMessage)
            ->subject($this->request->document->user->name.' Vas poziva da pregledate i potpišete dokument: '. $this->request->document->template->public_title)
            ->greeting('Pozdrav '. $this->request->party->name.',')
            ->line($content)
            ->line("<p>Klikom na poveznicu ispod možete pregledati tekst dokumenta i potpisati ga jednostavnim elektroničkim potpisom.</p>")
            ->action('Pregledaj i potpiši dokument', $this->request->link)
            ->line('Poveznica će biti aktivna sljedećih '.$this->request::getExpirationDays().' dana ili kraće ako '.$this->request->document->user->name.' otkaže ovaj poziv prije nego što potpišete dokument.');

        $message->viewData = ['email_preferences_link' => URL::signedRoute('email.preferences.show', ['email' => $this->email])];

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
