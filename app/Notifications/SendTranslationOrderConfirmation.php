<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Helpers\StringHelper;
use App\Models\EmailPreference;
use App\Models\Translation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\URL;

class SendTranslationOrderConfirmation extends Notification implements ShouldQueue
{
	use Queueable;

	public Translation $translation;

	/**
	 * Create a new notification instance.
	 */
	public function __construct(Translation $translation)
	{
		$this->translation = $translation;
	}

	/**
	 * Get the notification's delivery channels.
	 *
	 * @return array<int, string>
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

	public function shouldSkip($notifiable)
	{
		return !$this->translation->user || !EmailPreference::isAllowedGeneralEmails($this->translation->user->email);
	}

	/**
	 * Build Mail Message
	 */
	public function toMail($notifiable)
	{
		if($this->shouldSkip($notifiable)) {
			throw new SkipNotificationException();
		}

		// Generate a signed URL for PDF download
		$url = URL::signedRoute('translation.order.confirmation.download', $this->translation);

		// for staging, make sure we include the htaccess data
		if(app()->environment('staging')) {
			$url = str('https://staging.pravomat.hr')->replace('https://staging.pravomat.hr', env('APP_URL'));
		}

		$response = Http::get($url);

		// Build the base mail message
		$message = (new MailMessage)
			->subject('Potvrda o narudžbi')
			->greeting('Pozdrav ' . $this->translation->user->name . ',')
			->line("U privitku se nalazi potvrda o narudžbi:")
			->line("<i>" . $this->translation->full_title . "</i>")
			->line('Ukupni iznos: ' . StringHelper::formatCurrency($this->translation->orderConfirmation->total) . ' EUR')
			->action('Pogledaj narudžbu', route('translation.show', $this->translation))
			->line('Potvrđujemo da je Vaša kartica uspješno predautorizirana za navedeni iznos. Teretit ćemo karticu i izdati račun za terećeni iznos kada prijevod bude spreman za isporuku.');

		if ($response->successful()) {
			$message->attachData(
				$response->body(),
				'Potvrda o narudžbi.pdf',
				['mime' => 'application/pdf']
			);
		} else {
			throw new \Exception('Failed to download PDF: ' . $response->status());
		}

		// Any additional view data
		$message->viewData = [
			'email_preferences_link' => URL::signedRoute('email.preferences.show', [
				'email' => $this->translation->user->email
			]),
		];

		return $message;
	}


	/**
	 * Get the array representation of the notification.
	 *
	 * @return array<string, mixed>
	 */
	public function toArray($notifiable): array
	{
		return [
			//
		];
	}
}
