<?php

namespace App\Listeners;

use App\Models\Translation;
use App\Models\User;
use App\Models\Document;
use App\Models\TranslationDocument;
use App\Models\Invoice;
use App\Notifications\TranslationRequested;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

/**
 * Class SaveGuestID
 * @package App\Listeners
 * Assign user_id to the created documents/translations while user was in guest mode
 */
class SaveGuestID {

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct() {
        //
    }

    /**
     * Assign user_id to the created documents/translations while user was in guest mode
     *
     * @param $event
     * @return void
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function handle($event) {

		// Skip this listener on tenant app
		if (tenancy()->initialized) {
			return;
		}

        $guest_id = request()->get('guest_id') ?: User::guestID();

        if ($guest_id && $event->user->hasVerifiedEmail()) {

            // Update documents
            $documents = Document::where('guest_id', $guest_id)
                ->whereNull('user_id')
                ->where('is_visible', true)
                ->update(['user_id' => $event->user->id]);

            if ($documents) {
                if ($documents > 1) {
                    Session::flash('success', 'Uspješno ste spremili dokumente.');
                } else {
                    Session::flash('success', 'Uspješno ste spremili dokument.');
                }
            }

	        // Get translations before updating them
	        $translations = Translation::where('guest_id', $guest_id)
	                                   ->whereNull('user_id')
	                                   ->get();

	        // Update translations
	        if ($translations->isNotEmpty()) {
		        Translation::where('guest_id', $guest_id)
		                   ->whereNull('user_id')
		                   ->update(['user_id' => $event->user->id]);

		        // Update related invoices
		        Invoice::join('translations', 'invoices.invoiceable_id', '=', 'translations.id')
		               ->where('invoices.invoiceable_type', Translation::class)
		               ->whereNull('invoices.user_id')
		               ->where('translations.guest_id', $guest_id)
		               ->update(['invoices.user_id' => $event->user->id]);

		        // Send notification for to admin each translation
		        foreach ($translations as $translation) {
			        $notification = new TranslationRequested($translation);
			        Notification::route('mail', User::getTranslationSupportEmail())->notify($notification);
		        }
	        }
        }
    }
}
