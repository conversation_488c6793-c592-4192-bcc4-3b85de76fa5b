<?php

namespace App\Models;

use App\Helpers\DocumentBuilder;
use App\Helpers\DocumentParser;
use App\Helpers\PDFHelper;
use App\Helpers\StringHelper;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Mtvs\EloquentHashids\HasHashid;
use Mtvs\EloquentHashids\HashidRouting;
use Spatie\Activitylog\Models\Activity;
use View;

class Document extends Model {

	protected $fillable = ['comment'];

	public static $render_types = [
		'normal' => 1,
		'solemnization' => 2,
	];

	use SoftDeletes;
	use Cachable;

	// php artisan modelCache:clear

	use HasHashid, HashidRouting;

	public function activities()
	{
		return $this->morphMany(Activity::class, 'subject');
	}

	public function user() {

		return $this->belongsTo(User::class);
	}

	public function template() {

		return $this->hasOne(DocumentTemplate::class, 'id', 'template_id');
	}

	public function content() {

		return $this->hasOne(DocumentContent::class, 'document_id');
	}

	public function parties() {

		return $this->hasMany(DocumentParty::class);
	}

	public function signeeParties() {

		return $this->hasMany(DocumentParty::class)->where('is_signee', '=', true);
	}

	public function drafts() {

		return $this->hasMany(DocumentDraft::class);
	}

	public function signedParties() {

		return $this->hasMany(DocumentParty::class)->where('signature', '!=', null);
	}

	public function isSigned() {

		return $this->signedParties->count();
	}

	public function sectionValues() {
		return $this->hasMany(DocumentTemplateSectionValue::class);
	}

	public function values() {
		$values = [];
		foreach($this->sectionValues as $_section_values) {
			$values[$_section_values->document_template_section_id] = json_decode($_section_values->data, true);
		}

		return $values;
	}

	public function editableDocuments() {

		return $this::whereDoesntHave('signedParties')->where('is_visible', '=', 1);
	}

	public function isEditable() {

		return $this->is_visible;
	}

	public static function create($template_id, $user_id, $attrs = []) {

		$document                       = new self();
		$document->template_id = $template_id;

		// if guest, save guest_id to document
		if ( ! $user_id) {
			$document->guest_id = User::guestID();
		} else {
			$document->user_id = $user_id;
		}

		// populate any additional attributes
		foreach ($attrs as $_attr_k => $_attr_v) {
			$document->{$_attr_k} = $_attr_v;
		}

		if ($document->save()) {
			// write template version id
			$document->version_id = $document->template->version_id;
			$document->save();

			return $document;
		}

		return null;
	}

	/* Gets field value, searching in all sections of the document */
	public function getValue($field) {

		foreach($this->values() as $_values) {
			if (array_key_exists($field, $_values)) {
				return $_values[$field];
			}
		}

		return null;
	}

	public function isOutdated() {

		return $this->version_id != $this->template->version_id;
	}

	public function isDownloadable() {

		return $this->is_visible && ($this->user == Auth::user());
	}

	public function saveHtml() {

		$cb = new DocumentBuilder($this);

		if ( ! $this->content()->exists()) {

			$document_content = new DocumentContent();
			$document_content->document_id = $this->id;
			$document_content->html = $cb->generateNewContent();

			return $document_content->save();
		} else {
			$this->content->html = $cb->generateNewContent();
			return $this->content->save();
		}

	}

	public function savePdf() {

		$cb = new DocumentBuilder($this);
		$template = $cb->loadTemplate();

		// save new pdf
		$result = Storage::put(
			'documents/' . $this->id . '/document.pdf',
			$template->output()
		);

		// delete (existing) image PDF version
		Storage::delete('documents/' . $this->id . '/document_image.pdf');

		return $result;
	}

	// dependencies: img2pdf (https://pypi.org/project/img2pdf/), ghostscript, qpdf
	public function saveImagePdf() {
		$document_directory = storage_path("app/documents/{$this->id}");
		$pdf_path = "$document_directory/document.pdf";
		$document_img_directory = "$document_directory/img";
		$img_pdf_path = "$document_directory/document_image.pdf";
		$temp_protected_pdf_path = "$document_directory/temp_protected.pdf";

		// Re-create document img directory
		\File::deleteDirectory($document_img_directory);
		\File::ensureDirectoryExists($document_img_directory);

		// Convert PDF to images using Ghostscript
		$command = "gs -o " . escapeshellarg("$document_img_directory/img_%03d.png") .
		           " -sDEVICE=png16m -sPAPERSIZE=a4 -r400 -dDownScaleFactor=2 " .
		           "-dTextAlphaBits=4 -dGraphicsAlphaBits=4 " . escapeshellarg($pdf_path);
		shell_exec($command);

		// Collect image paths
		$image_paths = array_map(function ($item) {
			return $item->getPathname();
		}, \File::files($document_img_directory));

		// Retry if no images yet
		if (empty($image_paths)) {
			sleep(2);
			$image_paths = array_map(function ($item) {
				return $item->getPathname();
			}, \File::files($document_img_directory));
		}

		if (!empty($image_paths)) {
			// Generate image-based PDF
			shell_exec("img2pdf --pagesize A4 " . implode(' ', array_map('escapeshellarg', $image_paths)) . " -o " . escapeshellarg($img_pdf_path));

			// Apply qpdf protection and overwrite original
			$qpdf_command = "qpdf --encrypt \"\" \"\" 256 --extract=n --print=full --modify=none -- " .
			                escapeshellarg($img_pdf_path) . " " . escapeshellarg($temp_protected_pdf_path);
			shell_exec($qpdf_command);

			// Gracefully retry to check if the file appears (handles delayed file creation)
			$max_attempts = 3;
			$attempt = 0;
			while ($attempt < $max_attempts && !\File::exists($temp_protected_pdf_path)) {
				usleep(500000); // 0.5 sec
				$attempt++;
			}

			// Check if qpdf output exists before moving
			if (\File::exists($temp_protected_pdf_path)) {
				\File::move($temp_protected_pdf_path, $img_pdf_path);
			} else {
				report(new \Exception("QPDF failed or delayed — file not created after retries: {$temp_protected_pdf_path}"));
			}

			return $img_pdf_path;
		} else {
			throw new \Exception('Image encoding failed');
		}
	}

	public function allPartiesSigned() {

		return $this->signedParties->count() == $this->parties->count();
	}

	public function createStorageDirectory() {

		// create directories (if first time)
		$directories = [
			storage_path('app/documents'),
			storage_path('app/documents/' . $this->id),
			storage_path('app/documents/' . $this->id) . '/img', // for assembling an image-only pdf
		];

		foreach ($directories as $_directory) {
			if ( ! \File::exists($_directory)) {
				\File::makeDirectory($_directory, 0755, true); // the third parameter set to true enables recursive creation
			}
		}

	}

	public function getPartiesString() {
		if ( ! $this->parties->isEmpty()) {

			$parties = [
				'left'  => [],
				'right' => []
			];

			foreach ($this->parties as $_party) {
				$_party_name = $_party->name;

				// if authorized person, extract company name
				if (stripos($_party->label, '<br>') !== false) {
					// get everything in parentheses
					if ($_parenthesis_text = StringHelper::extractTextFromParenthesis($_party->label)) {
						$_party_name = $_parenthesis_text;
					}
				}

				if ( ! empty($_party_name)) {
					array_push($parties[$_party->side], $_party_name);
				}

			}

			return implode(", ", array_merge(
				array_unique($parties['left']),
				array_unique($parties['right']),
			));
		}

		return null;
	}

	public function createDraft($is_visible = false) {

		// ensure content exists
		if ( ! $this->content()->exists()) {
			$this->saveHtml();
		}

		$content = (new DocumentParser(
			$this->content->html,
			$this->template->type_id
		))->getContent();

		$parties = $this->getPartiesString();

		return DocumentDraft::create([
			'user_id' => $this->user_id,
			'type_id' => $this->template->type_id,
			'render_type_id' => $this->render_type_id,
			'document_id' => $this->id,
			'title' => str($this->title)->append(!empty($parties) ? " ($parties)" : ""),
			'comment' => $this->comment,
			'html' => View::make('editor.show', ['content' => $content])->render(),
			'is_visible' => $is_visible
		]);

	}

	public function createTranslation($lang = 'en') {

		// ensure content exists
		if ( ! $this->content()->exists()) {
			$this->saveHtml();
		}

		// ensure pdf exists
		$pdf_path = 'documents/' . $this->id . '/document.pdf';

		if (Storage::missing($pdf_path)) {
			$this->savePdf();
		}

		// ensure image pdf exists
		if($this->shouldProtectCopyPasting()) {

			$image_pdf_path = 'documents/' . $this->id . '/document_image.pdf';

			if (Storage::missing($image_pdf_path)) {
				$this->saveImagePdf();
			}

		} else {
			$image_pdf_path = $pdf_path;
		}

		$parties = $this->getPartiesString();

        $title = str($this->title)->append(!empty($parties) ? " ($parties)" : "");

        // create translation
        $translation = Translation::create([
            'title' => $title,
            'user_id' => $this->user_id,
            'source_id' => Translation::$sources['pravomat'],
            'lang' => $lang
        ]);

		if($translation) {

			// log order history
			activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Narudžba stvorena.');

            // create document
            $document = TranslationDocument::create([
                'translation_id' => $translation->id,
                'title' => $title,
                'character_count' => PDFHelper::countCharacters(storage_path('app/' . $pdf_path)),
            ]);

			// copy PDF files
			$document->createStorageDirectory();

			Storage::copy($pdf_path, 'translations/' . $translation->id . '/documents/' . $document->id . '/input.pdf');
			Storage::copy($image_pdf_path, 'translations/' . $translation->id . '/documents/' . $document->id . '/input_image.pdf');

            // update input path
            $document->update([
                'input_path' => 'app/translations/' . $translation->id . '/documents/' . $document->id . '/input_image.pdf',
            ]);

            return $translation;
		}

        return null;
	}

	public function shouldProtectCopyPasting() {
		return !App::isLocal();
	}

}
