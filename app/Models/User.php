<?php

namespace App\Models;

use App\Notifications\ResetPassword;
use App\Notifications\VerifyEmail;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Cashier\Billable;
use Spatie\Activitylog\Models\Activity;

class User extends Authenticatable implements MustVerifyEmail {

	use Notifiable;
	use SoftDeletes;
	use Billable;

	/**
	 * The attributes that are mass assignable.
	 *
	 * @var array
	 */
	protected $fillable = [
		'name',
		'email',
		'password',
        'auth_id',
        'auth_type',
        'email_verified_at',
		'otp_code',
		'otp_expires_at',
	];

	/**
	 * The attributes that should be hidden for arrays.
	 *
	 * @var array
	 */
	protected $hidden = [
		'password',
		'remember_token',
		'otp_code',
	];

	/**
	 * The attributes that should be cast.
	 *
	 * @var array<string, string>
	 */
	protected $casts = [
		'email_verified_at' => 'datetime',
		'otp_expires_at' => 'datetime',
		'password' => 'hashed',
	];

	public function activities()
	{
		return $this->morphMany(Activity::class, 'subject');
	}

	public function sendPasswordResetNotification($token) {
		try{
			$this->notify(new ResetPassword($token));
		} catch(\Exception $e) {
			report($e);
		}
	}

	public function sendEmailVerificationNotification() {
		try {
			$this->notify(new VerifyEmail(self::guestID()));
		} catch(\Exception $e) {
			report($e);
		}

		activity()->performedOn($this)->log('Send verification email');
	}

	public function options() {
		return $this->hasOne('App\Models\UserOptions');
	}

	public function emailPreference() {
		return $this->hasOne('App\Models\EmailPreference', 'email', 'email');
	}

	public function documents() {
		return $this->hasMany('App\Models\Document')->where('is_visible', '=', true);
	}

	public function documentDrafts() {
		return $this->hasMany('App\Models\DocumentDraft')->where('is_visible', '=', true);
	}

	public function translations() {
        return $this->hasMany('App\Models\Translation');
	}

	public function receipts() {
		return $this->hasMany(Receipt::class);
	}

	public function feedbackRequest() {
		return $this->hasOne('App\Models\FeedbackRequest');
	}

	public function feedback() {
		return $this->hasMany('App\Models\Feedback');
	}

	public function isDocumentOwner(Document $document) {
		return $this->id === $document->user_id;
	}

	public function isAdmin() {
		return $this->options && $this->options->is_admin;
	}

	public static function guestID() {
		return \Cookie::get('guest_id') ?:
			(\Cookie::hasQueued('guest_id') ? \Cookie::queued('guest_id')->getValue() : null);
	}

	public static function getAdminEmails() {
		return User::whereRelation('options', 'is_admin', '=', 1)->select('email')->get()->map(function($user) {
			return $user->email;
		})->toArray();
	}

	public function shouldShowEditorTutorial() {
		return $this->options && !$this->options->is_editor_tutorial_shown;
	}

	public static function shouldShowWizardTutorial() {
		if (auth()->check()) {
			$user_options = auth()->user()->options;
			return $user_options && !$user_options->is_wizard_tutorial_shown && !\Cookie::has('wizardTutorialShown');
		} else {
			return !\Cookie::has('wizardTutorialShown');
		}
	}

	public static function getTranslationSupportEmail() {
		return app()->isProduction() ? '<EMAIL>' : env('DEVELOPER_EMAIL');
	}

}
