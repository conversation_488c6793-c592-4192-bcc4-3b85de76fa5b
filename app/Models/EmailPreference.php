<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EmailPreference extends Model
{
	CONST DEFAULT_GENERAL = true;
	CONST DEFAULT_NEWSLETTER = false;

	protected $fillable = ['email', 'general', 'newsletter'];

	public static function isAllowedGeneralEmails(String $email) {

		if($model = self::where('email', $email)->first()) {
			return $model->general;
		}

		return self::DEFAULT_GENERAL;
	}

	public static function isAllowedNewsletterEmails(String $email) {

		if($model = self::where('email', $email)->first()) {
			return $model->newsletter;
		}

		return self::DEFAULT_NEWSLETTER;
	}

	public static function subscribeToGeneral(String $email) {
		return self::updateOrCreate(
			['email' => $email],
			['general' => true]
		);
	}

	public static function subscribeToNewsletter(String $email) {
		return self::updateOrCreate(
			['email' => $email],
			['newsletter' => true]
		);
	}

	public static function unsubsubscribeFromGeneral(String $email) {
		return self::updateOrCreate(
			['email' => $email],
			['general' => false]
		);
	}

	public static function unsubsubscribeFromNewsletter(String $email) {
		return self::updateOrCreate(
			['email' => $email],
			['newsletter' => false]
		);
	}
}
