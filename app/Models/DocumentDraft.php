<?php

namespace App\Models;

use App\Helpers\DocumentDasher;
use App\Helpers\DocumentPDF;
use App\Helpers\PDFHelper;
use App\Helpers\StringHelper;
use App\Traits\Encryptable;
use Corcel\Model\Post;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Mtvs\EloquentHashids\HasHashid;
use Mtvs\EloquentHashids\HashidRouting;
use Spatie\Activitylog\Models\Activity;

class DocumentDraft extends Model
{
	use HasHashid, HashidRouting, Encryptable, SoftDeletes, Cachable;

	// php artisan modelCache:clear

	public static $version = "1";

	public static $types = [
		'contract' => 1,
		'statement' => 2
	];

	public static $render_types = [
		'normal' => 1,
		'solemnization' => 2,
	];

	protected $encryptable = [
		'html',
	];

	protected $fillable = ['user_id', 'type_id', 'render_type_id', 'document_id', 'post_id', 'title', 'html', 'comment', 'is_visible'];

	public function activities()
	{
		return $this->morphMany(Activity::class, 'subject');
	}

	public function user() {

		return $this->belongsTo(User::class);
	}

	public function document() {

		return $this->belongsTo(Document::class)->withTrashed();
	}

	public function template()
	{
		return $this->hasOneThrough(
			'App\Models\DocumentTemplate',
            'App\Models\Document',
			'id', // Foreign key on Document table
			'id', // Foreign key on DocumentTemplate table
			'document_id', // Local key on DocumentDraft table
			'template_id' // Local key on Document table
		)->withTrashedParents();
	}

	// finds a corresponding WordPress post (document if document_id defined, otherwise example)
	public function getPost()
	{
		// return wp document
		if ($this->document_id) {
			$slug = optional($this->template()->first())->slug;
			return Post::where('post_name', $slug)->first();
		}

		// return wp example
		return Post::find($this->post_id);
	}


	public function isOutdated() {

		return $this->version_id != self::$version;
	}

	public function createStorageDirectory() {
		// create directories for storing pdfs and images
		$directories = [
			storage_path('app/editor/drafts'),
			storage_path('app/editor/drafts/' . $this->id),
			storage_path('app/editor/drafts/' . $this->id) . '/img', // for assembling an image-only pdf
		];

		foreach ($directories as $_directory) {
			if ( ! \File::exists($_directory)) {
				\File::makeDirectory($_directory, 0755, true); // the third parameter set to true enables recursive creation
			}
		}
	}

	public function savePdf() {

		// if solemization, save dashed pdf, otherwise save normal
		if($this->render_type_id === DocumentDraft::$render_types['solemnization']) {
			$this->saveDashedPdf();
		} else {
			$template = new DocumentPDF(
				$this->html,
				['title' => $this->title],
				""
			);

			// save new pdf
			Storage::put(
				'editor/drafts/' . $this->id . '/document.pdf',
				$template->output()
			);
		}

		// delete (existing) image PDF version
		Storage::delete('editor/drafts/' . $this->id . '/document_image.pdf');
	}

	private function saveDashedPdf() {

        $path = storage_path("app/editor/drafts/{$this->id}/document.pdf");

		$this->createStorageDirectory();

		$dasher = new DocumentDasher(
			$this->html,
			$path
		);

		$dasher->save();
	}

	// dependencies: img2pdf (https://pypi.org/project/img2pdf/), ghostscript
	public function saveImagePdf() {

		$document_directory = storage_path("app/editor/drafts/{$this->id}");
		$pdf_path =  "$document_directory/document.pdf";
		$document_img_directory = "$document_directory/img";
		$img_pdf_path = "$document_directory/document_image.pdf";
		$temp_protected_pdf_path = "$document_directory/temp_protected.pdf";

		// re-create document img directory
		\File::deleteDirectory($document_img_directory);
		\File::ensureDirectoryExists($document_img_directory);

		// PDF to images
		$command = "gs -o $document_img_directory/img_%03d.png -sDEVICE=png16m -sPAPERSIZE=a4 -r400 -dDownScaleFactor=2 -dTextAlphaBits=4 -dGraphicsAlphaBits=4 $pdf_path";
		shell_exec($command);

		// Collect image paths
		$image_paths = array_map(function($item) {
			return $item->getPathname();
		}, \File::files($document_img_directory));

		// Retry if no images yet
		if (empty($image_paths)) {
			sleep(2);
			$image_paths = array_map(function ($item) {
				return $item->getPathname();
			}, \File::files($document_img_directory));
		}

		if(!empty($image_paths)) {
			// Generate image-based PDF
			shell_exec("img2pdf --pagesize A4 " . implode(' ', array_map('escapeshellarg', $image_paths)) . " -o " . escapeshellarg($img_pdf_path));

			// Apply qpdf protection and overwrite original
			$qpdf_command = "qpdf --encrypt \"\" \"\" 256 --extract=n --print=full --modify=none -- " .
			                escapeshellarg($img_pdf_path) . " " . escapeshellarg($temp_protected_pdf_path);
			shell_exec($qpdf_command);

			// Replace original with protected version
			\File::move($temp_protected_pdf_path, $img_pdf_path);

			return $img_pdf_path;
		} else {
			throw new \Exception('Image encoding failed');
		}
	}

	public function shouldProtectCopyPasting() {
		return !App::isLocal();
	}

	public function hasDirtySegments() {
		$dirty_segment_class = 'segment-dirty';
		$body = StringHelper::getSubstringBetween($this->html, '<body>', '</body>');
		return boolval(Str::substrCount($body, $dirty_segment_class));
	}

	public function isContract() {
		return $this->type_id === self::$types['contract'];
	}

	public function isStatement() {
		return $this->type_id === self::$types['statement'];
	}

	public function createTranslation($lang = 'en') {

		// ensure pdf exists
		$pdf_path = 'editor/drafts/' . $this->id . '/document.pdf';

		if (Storage::missing($pdf_path)) {
			$this->savePdf();
		}

		// ensure image pdf exists
		if($this->shouldProtectCopyPasting()) {

			$image_pdf_path = 'editor/drafts/' . $this->id . '/document_image.pdf';

			if (Storage::missing($image_pdf_path)) {
				$this->saveImagePdf();
			}

		} else {
			$image_pdf_path = $pdf_path;
		}

        // create order
        $translation = Translation::create([
            'title' => $this->title,
            'user_id' => $this->user_id,
            'source_id' => Translation::$sources['pravomat'],
            'lang' => $lang
        ]);

		if($translation) {

			// log order history
			activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Narudžba stvorena.');

            // create document
            $document = TranslationDocument::create([
                'translation_id' => $translation->id,
                'title' => $this->title,
                'character_count' => PDFHelper::countCharacters(storage_path('app/' . $pdf_path)),
            ]);

			// copy PDF files
            $document->createStorageDirectory();

			Storage::copy($pdf_path, 'translations/' . $translation->id . '/documents/' . $document->id . '/input.pdf');
			Storage::copy($image_pdf_path, 'translations/' . $translation->id . '/documents/' . $document->id . '/input_image.pdf');

            // update input path
            $document->update([
                'input_path' => 'app/translations/' . $translation->id . '/documents/' . $document->id . '/input_image.pdf',
            ]);

		    return $translation;
		}

        return null;
	}
}
