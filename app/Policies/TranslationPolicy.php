<?php

namespace App\Policies;

use App\Models\Translation;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\Route;

class TranslationPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if translation can be updated by current user
     *
     * @param User $user
     * @param Translation $translation
     *
     * @return bool
     */
    public function update(User $user, Translation $translation): bool {

        $admin_routes = [
	        'translation.order.confirmation.download',
            'translation.receipt.download',
            'translation.storno.receipt.download',
        ];

        // admins can access certain routes
        if (auth()->user()->isAdmin() && in_array(Route::currentRouteName(), $admin_routes)) {
            return true;
        }

        return $translation->user_id === $user->id;
    }
}
