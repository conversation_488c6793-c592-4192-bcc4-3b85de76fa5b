<?php

namespace App\Policies;

use App\Models\Invoice;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\Route;

class InvoicePolicy
{

	use HandlesAuthorization;

    /**
     * Create a new policy instance.
     */
    public function __construct()
    {
        //
    }

	/**
	 * Determine if invoice can be updated by current user
	 *
	 * @param User $user
	 * @param Invoice $invoice
	 *
	 * @return bool
	 */
	public function update(User $user, Invoice $invoice): bool {

        $admin_routes = [
            'translation.invoice.download'
        ];

        // admins can access certain routes
        if (auth()->user()->isAdmin() && in_array(Route::currentRouteName(), $admin_routes)) {
            return true;
        }
		return $invoice->user_id === $user->id;
	}
}
