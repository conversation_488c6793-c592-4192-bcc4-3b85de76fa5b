<?php

namespace App\Rules;

use App\Models\DocumentTemplate;
use Illuminate\Contracts\Validation\Rule;

class ValidDocumentTemplate implements Rule {

	/**
	 * Create a new rule instance.
	 *
	 * @return void
	 */
	public function __construct() {
		//
	}

	/**
	 * Template should exist and should have sections with fields
	 *
	 * @param string $attribute
	 * @param mixed $value
	 *
	 * @return bool
	 */
	public function passes($attribute, $value) {

        // fail if value is not numeric
        if (!ctype_digit(strval($value))) {
            return false;
        }

		if ($template = DocumentTemplate::find($value)) {
		    //todo temporary
			if (app()->environment('production') && !tenancy()->initialized) {
				if ($template->title === 'WorkContractRemote') {
					$previous_url = url()->previous() ? url()->previous() : route('home');
					abort(503, 'Dokument trenutno nije dostupan zbog usklađivanja s novim zakonom o radu. Zahvaljujemo na razumijevanju. <br><br> <a onmouseover="this.style.textDecoration=\'underline\'" onmouseleave="this.style.textDecoration=\'none\'" href="' . $previous_url . '">Kliknite ovdje za povratak</a>');
				}
			}

			if (count($template->sections)) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message() {

		return 'Došlo je do pogreške. Pokušajte ponovo ili prijavite grešku.';
	}
}
