<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\DocumentTemplateSection;
use App\Models\SectionFormModel;
use App\Models\User;
use App\Models\UserOptions;
use Detection\MobileDetect;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class SectionController extends Controller {

	public function show(Document $document, DocumentTemplateSection $section) {

		if ($document->isOutdated()) {
			return redirect(route('documents'))->with('error', __('Document is outdated.'));
		}

		if ($document->isSigned()) {
			return redirect(route('documents'))->with('error', 'Dokumente koji sadrže e-Potpise nije moguće naknadno uređivati.');
		}

		if($document->template_id !== $section->template_id) {
			return redirect(route('home'));
		}

		//todo temporary
		if(App::isProduction()){{
			if($document->template->title === 'WorkContractRemote') {
				return back()->with('error', 'Dokument trenutno nije moguće uređivati zbog usklađivanja s novim zakonom o radu.');
			}
		}}

		// eager load values for all sections of the document (navbar warnings)
		$document->load('sectionValues');

		$section_form_model = new SectionFormModel($document, $section);
		$section_form_model->setupView();

		return view($section_form_model->view)->with(
			[
				'model' => $section_form_model,
				'js'    => $section_form_model->js,
				'route' => route('section.store', [$document, $section]),
				'enable_document_preview' => $this->hasEnabledDocumentPreview(),
				'should_show_wizard_tutorial' => User::shouldShowWizardTutorial()
			]
		);
	}

	private function hasEnabledDocumentPreview() {
		// if not mobile device and document preview is enabled
		$md = new MobileDetect();
		if(!$md->isMobile()) {
			return session()->get('enable_document_preview') ?? true;
		}

		return false;
	}

	public function store(Request $request, Document $document, DocumentTemplateSection $section) {

		$request->validate([
			'render_type_id' => 'nullable|in:' . implode(',', Document::$render_types),
		]);

		if ($document->isOutdated()) {
			return redirect(route('documents'))->with('error', __('Document is outdated.'));
		}

		if ( ! $document->isSigned()) {

		    $section_form_model = new SectionFormModel($document, $section);
            $result = $section_form_model->save($request->except('_token'), $request->ajax());

            if(!$result){
                throw new \Exception(json_encode([
                    'message' => 'Error saving section form model',
                    'document' => $document->id,
                    'section' => $section->id,
                    'request' => $request->toArray()
                ]));
            }

			// unless ajax preview
			if ( ! $request->ajax()) {
				// redirect to next section if exists, otherwise save document and redirect to user dashboard
				if ($section_form_model->next_section_url) {
					return redirect($section_form_model->next_section_url);
				} else {

					// set visible in dashboard
					$document->is_visible = true;

					// set render type
					if($render_type_id = $request->get('render_type_id')) {
						$document->render_type_id = $render_type_id;
					}

					$document->title = $document->template->public_title;

					if ( ! Auth::check()) {
						Session::put('auth_message',
							'Prijavite se ili <a href="/register">besplatno napravite račun</a> da biste preuzeli svoj dokument.'
						);
					} else {
						$document->user_id = Auth::id();
					}

					$document->save();

					return redirect(route('documents', ['tab' => 'wizard']))
						->with('success', 'Uspješno ste spremili dokument.')
						->withCookie(cookie('documentsTab', 'wizard',60*24*360, '/', null, null, false));
				}
			}
		} else {
			abort(403);
		}

	}

	public function setWizardTutorialShown() {

		if(auth()->check()) {
			UserOptions::updateOrCreate(
				[
					'user_id' => Auth::id()
				],
				[
					'is_wizard_tutorial_shown' => true
				]
			);
		} else {
			\Cookie::queue('wizardTutorialShown', 1, 60*24*360);
		}

	}
}
