<?php

namespace App\Http\Controllers;

use App\Models\DocumentTemplate;
use Corcel\Model\Post;
use Spatie\ResponseCache\Facades\ResponseCache;

class ApiController extends Controller
{
	public function syncTemplateTags()
	{
		request()->validate([
			'id' => 'required',
			'key' => 'required|string|in:' . env('PRAVOMAT_API_KEY')
		]);

		// sync model tags with WordPress tags
		if ($page = Post::find(request('id'))) {
			if ($template = DocumentTemplate::where('slug', $page->post_name)->first()) {
				$template->tags = $page->meta->tags;
				$template->save();
			}

			return true;
		}
	}

	public function syncElasticSearch() {

		request()->validate([
			'key' => 'required|string|in:' . env('PRAVOMAT_API_KEY'),
            'index' => 'nullable|in:document,example,article',
            'id' => 'nullable|integer'
		]);

		\Artisan::call('es:sync', [
		    'index' => request()->get('index'),
            'id' => request()->get('id')
        ]);
	}

    public function exampleImagesDelete() {

        request()->validate([
            'key' => 'required|string|in:' . env('PRAVOMAT_API_KEY'),
            'id' => 'integer'
        ]);

        if($id = request()->get('id')) {
            \File::deleteDirectory(storage_path('app/public/examples/screenshots/'.$id));
        }
    }

	public function clearCache() {
        request()->validate([
            'key' => 'required|string|in:' . env('PRAVOMAT_API_KEY')
        ]);

        $index = request()->get('index');

        if(in_array($index, ['document', 'example', 'post'])) {

            // clear search cache for "document" and "example" post types
            if(in_array($index, ['document', 'example'])) {

                \Cache::tags(["$index-search"])->flush();

                // if post type is "example", also clear article search cache
                if($index === 'example') {
                    \Cache::tags(["article-search"])->flush();
                }
            }

            ResponseCache::clear(["wordpress-$index"]);
        } else {
            ResponseCache::clear(["wordpress-page"]);
        }
    }
}
