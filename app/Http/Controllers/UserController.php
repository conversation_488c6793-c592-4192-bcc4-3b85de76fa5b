<?php

namespace App\Http\Controllers;

use App\Models\EmailPreference;
use App\Models\UserOptions;
use Carbon\Carbon;
use Cookie;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class UserController extends Controller {

	public function dashboard(Request $request) {
		return view('dashboard.index');
	}

	public function setCookiePreferences() {

		request()->validate([
			'ga_accepted' => 'present'
		]);

		Cookie::queue('ga_accepted', request()->get('ga_accepted'), 60*24*365);
	}

	public function documentPreviewSetting() {
		request()->validate([
			'action' => 'required|in:on,off'
		]);

		session()->put('enable_document_preview', request()->get('action') === 'on');

		return response()->json(['status' => 'success']);
	}

	public function deleteAccount(Request $request) {

		$user = \Auth::user();

		EmailPreference::where('email', $user->email)->delete();

		$user->name = "";
		$user->email = Str::random(30);
		$user->deleted_at = Carbon::now();
		$user->save();

		\Auth::logout();

		activity()->performedOn($user)->log('Delete account');

		return redirect(route('login'))->with('success', 'Uspješno ste izbrisali Vaš Pravomat račun.');
	}

	public function setDocumentTutorialShown() {

		UserOptions::updateOrCreate(
			[
				'user_id' => Auth::id()
			],
			[
				'is_document_tutorial_shown' => true
			]
		);
	}

	public function setDraftTutorialShown() {

		UserOptions::updateOrCreate(
			[
				'user_id' => Auth::id()
			],
			[
				'is_draft_tutorial_shown' => true
			]
		);
	}

	public function setEditorTutorialShown() {

		UserOptions::updateOrCreate(
			[
				'user_id' => Auth::id()
			],
			[
				'is_editor_tutorial_shown' => true
			]
		);
	}

	public function setTranslationTutorialShown() {

		UserOptions::updateOrCreate(
			[
				'user_id' => Auth::id()
			],
			[
				'is_translation_tutorial_shown' => true
			]
		);
	}

}
