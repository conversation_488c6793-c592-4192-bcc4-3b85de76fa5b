<?php

namespace App\Http\Controllers;

use App\Helpers\StringHelper;
use App\Models\TranslationDocument;
use App\Models\Translation;
use App\Notifications\TranslationUploaded;
use Illuminate\Support\Facades\Notification;

class TranslationDocumentController extends Controller
{
	public function uploadOutput(Translation $translation) {

		request()->validate([
            'translations' => 'required|array',
			'translations.*' => 'required|file|mimes:pdf',
		]);

        foreach(request()->file('translations') as $_id => $_translation) {
            $_document = TranslationDocument::find($_id);

            $output_path = $_translation->storeAs(
                'translations/' . $translation->id . '/documents/' . $_id,
                $_translation->getClientOriginalName()
            );

            $_document->update([
                'output_path' => 'app/' . $output_path,
            ]);
        }

        // update status to translated only if current status is "processing"
        // (because it could already be status "delivery")
        if($translation->status_id === Translation::$statuses['processing']) {
            $translation->update([
                'status_id' => Translation::$statuses['translated'],
            ]);
        }

        activity()->performedOn($translation)->log('Upload output');

		// log order history
		activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Prijevod prenesen.');

		// send notification to user
		$notification = new TranslationUploaded($translation);
		Notification::route('mail', $translation->user->email)->notify($notification);

		return redirect(route('translation.show.admin', $translation))
			->with('success', 'Uspješno ste uploadali prijevod.');
	}

	public function editOutput(Translation $translation) {
		request()->validate([
			'translations' => 'array',
			'translations.*' => 'file|mimes:pdf',
		]);

		if(request()->hasFile('translations')) {
			foreach(request()->file('translations') as $_id => $_translation) {
				$_document = TranslationDocument::find($_id);

				$output_path = $_translation->storeAs(
					'translations/' . $translation->id . '/documents/' . $_id,
					$_translation->getClientOriginalName()
				);

				$_document->update([
					'output_path' => 'app/' . $output_path,
				]);
			}

			activity()->performedOn($translation)->log('Edit output');

			// log order history
			activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Prijevod ažuriran.');

		}

		return redirect(route('translation.show.admin', $translation))
			->with('success', 'Uspješno ste uredili prijevod.');
	}

    public function downloadOutput(Translation $translation)
    {
        activity()->performedOn($translation)->log('Download output');

        if ($translation->documents()->count() > 1) {
            return $this->downloadOutputZip($translation);
        } else {
            $document = $translation->documents()->first();

            $filename = str($document->output_path)->afterLast('/');

            return response()->download(
                storage_path($document->output_path),
                StringHelper::sanatizeStringForDownload($filename)
            );
        }

    }

	public function downloadDocumentOutput(TranslationDocument $document) {

		activity()->performedOn($document)->log('Download output');

		$filename = str($document->output_path)->afterLast('/');

		return response()->download(
			storage_path($document->output_path),
			StringHelper::sanatizeStringForDownload($filename)
		);
	}

    public function download(TranslationDocument $document) {
        activity()->performedOn($document)->log('Download');

		$title = $document->title;
	    $input_path = str($document->input_path);

	    if(!$document->translation->isCustom()) {
		    // add .pdf extension
		    $title.= ".pdf";

		    // unprotected pdf
			if(auth()->user()->isAdmin()) {
				$input_path = $input_path->replaceLast('input_image.pdf', 'input.pdf');
			}
	    }

        return response()->download(
            storage_path($input_path),
            StringHelper::sanatizeStringForDownload($title)
        );
    }

	public function downloadInput(Translation $translation) {

        activity()->performedOn($translation)->log('Download input');

        if($translation->documents()->count() > 1) {
            return $this->downloadInputZip($translation);
        } else {
            $document = $translation->documents()->first();

            $title = StringHelper::sanatizeStringForDownload($document->title);

            // add .pdf extension
            if(!$translation->isCustom()) {
                $title.= ".pdf";
            }

            return response()->download(storage_path($document->input_path), $title);
        }
	}

	public function downloadInputAdmin(Translation $translation) {

        activity()->performedOn($translation)->log('Download input (admin)');

        if($translation->documents()->count() > 1) {
            return $this->downloadInputZip($translation);
        } else {
            $document = $translation->documents()->first();

            $title = StringHelper::sanatizeStringForDownload($document->title);

            $input_path = str($document->input_path);

            if(!$translation->isCustom()) {
                // unprotected pdf
                $input_path = $input_path->replaceLast('input_image.pdf', 'input.pdf');

                // add .pdf extension
                $title.= ".pdf";
            }

            return response()->download(storage_path($input_path), $title);
        }
	}

	private function downloadInputZip(Translation $translation)
	{
		$zip = new \ZipArchive();
		$zip_name = storage_path('app/translations/'.$translation->id.'/documents-' . uniqid() . '.zip');

		if ($zip->open($zip_name, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === true) {
			foreach ($translation->documents as $_i => $_document) {
				$_filename = ($_i+1) . '-' .$_document->title;
				$zip->addFile(storage_path($_document->input_path), $_filename);
			}
			$zip->close();
		} else {
			throw new \Exception('Could not create ZIP file');
		}

		return response()->streamDownload(function () use ($zip_name) {
			echo file_get_contents($zip_name);
			unlink($zip_name); // clean up the temporary file
		}, 'Pravomat_dokumenti_za_prijevod.zip');
	}


	private function downloadOutputZip(Translation $translation)
	{
		$zip = new \ZipArchive();
		$zip_name = storage_path('app/translations/'.$translation->id.'/translations-' . uniqid() . '.zip');

		if ($zip->open($zip_name, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === true) {
			foreach ($translation->documents as $_i => $_document) {
				$_filename = ($_i + 1) . '-' . str($_document->output_path)->afterLast('/');
				$zip->addFile(storage_path($_document->output_path), $_filename);
			}
			$zip->close();
		} else {
			throw new \Exception('Could not create ZIP file');
		}

		return response()->streamDownload(function () use ($zip_name) {
			echo file_get_contents($zip_name);
			unlink($zip_name); // clean up the temporary file
		}, 'Pravomat_prijevodi.zip');
	}


}
