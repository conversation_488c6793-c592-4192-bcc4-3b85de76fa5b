<?php

namespace App\Http\Controllers;

use App\Helpers\ElasticHelper;
use App\Helpers\Post;
use App\Models\DocumentTemplate;
use App\Models\SearchLog;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{

    public function templates()
    {
        request()->validate([
            'query' => 'max:100'
        ]);

        // log search query
        if (!empty(request('query'))) {
            SearchLog::create(request('query'));
        }

        $cache_key = 'search.template.'.App::environment().':'. request('query');

        // try fetching from cache, otherwise query search
        return Cache::tags(['template-search'])->rememberForever($cache_key, function () {

            $results = [];
            $hits = DocumentTemplate::search(request('query'));

			// add *NEW* badge to the latest template
			$max_id = DocumentTemplate::max('id');

			// add *UPDATED* badge to these templates
	        //$updated_templates = ['Ugovor o radu'];
            $updated_templates = [];

            // different result depending on whether query is empty
            if (request('query')) {
                foreach ($hits as $_hit) {

	                $_title = $_hit->public_title;

					if($_hit->id === $max_id) {
						$_title = "{$_hit->public_title} [NOVO]";
					} elseif(in_array($_hit->public_title, $updated_templates)) {
						$_title = "{$_hit->public_title} [OSVJEŽENO]";
					}

                    $results[] = [
                        'id' => $_hit->id,
                        'title' => $_title,
                    ];
                }
            } else {
                // generate category groups
                foreach ($hits as $_hit) {
                    $results[$_hit->category->order_index] = [
                        'category' => $_hit->category->title,
                        'order_index' => $_hit->category->order_index,
                        'templates' => []
                    ];
                }

                // sort categories
                $results = collect($results)->sortKeys()->keyBy('category')->toArray();

                // populate categories with templates
                foreach ($hits as $_hit) {

	                $_title = $_hit->public_title;

	                if($_hit->id === $max_id) {
		                $_title = "{$_hit->public_title} [NOVO]";
	                } elseif(in_array($_hit->public_title, $updated_templates)) {
		                $_title = "{$_hit->public_title} [OSVJEŽENO]";
	                }

                    $results[$_hit->category->title]['templates'][$_hit->categoryTemplate->order_index] = [
                        'id' => $_hit->id,
                        'title' => $_title,
                        'order_index' => $_hit->categoryTemplate->order_index
                    ];
                }

                // sort templates within categories
                foreach ($results as &$_result) {
                    $_result['templates'] = collect($_result['templates'])->sortKeys()->toArray();
                }
            }

            return [
                'results' => $results,
                'query' => request('query')
            ];
        });
    }

	public function wordpress(): array
    {
		request()->validate([
			'index' => 'required|in:example,document,article',
			'query' => 'present|max:100',
			'page' => 'present'
		]);

		$index = request('index');
		$query = request('query');
		$filter = request('filter');
		$page = request('page') ?? 0;

		$pagination_size = 10;

		if(!empty($query) || !empty($filter)){

			// log search query
			if (!empty($query)) {
				SearchLog::create($query);
			}

			$cache_key = 'search.' . $index . '.' . App::environment().':'. $page . ':' . $pagination_size . ':' . $filter . ':' . $query;

			// try fetching from cache, otherwise perform elastic search
			$results = Cache::tags(["$index-search"])->rememberForever($cache_key , function () use ($index, $query, $page, $filter, $pagination_size) {

				// apply potential filtering by related examples
				$filters = [];

				if(!empty($filter)) {
					if($example = Post::find($filter)) {

						$related_examples = $example->acf->related_examples;

						$filters = [
							'ids' => $related_examples
								? $related_examples->pluck('ID')->toArray()
								: []
						];
					}

				}

				$search_results = $this->elasticSearch($index, $query, $filters, $page, $pagination_size);

				return [
					'ids' => $search_results['ids'],
					'total_count' => $search_results['total']
				];
			});

			$ids = $results['ids'];
			$total_count = $results['total_count'];

			$order = sprintf('FIELD(id, %s)', implode(',', $ids));

			$posts = Post::whereIn('id', $ids)->orderByRaw($order)->paginate($pagination_size);
		}
		else{
			$posts = Post::type($index)->status('publish')->orderBy('post_date', 'desc')->paginate($pagination_size);
			$total_count = Post::type($index)->status('publish')->count();
		}

		return [
			'html' => view("wordpress.$index.partials.results", [
				'posts' => $posts,
				'query' => $query,
				'filter' => $filter,
			])->render(),
			'totalCount' => $total_count
		];
	}

	private function elasticSearch($index, $query, $filters, $from, $size): array {
		$query = ElasticHelper::prepareInput($query);

		$search_results = ['ids' => [], 'total' => 0];

		if ($query || $filters) {
			$es = new ElasticHelper();
			$results = $es->search($index, $query, $size, $from, $filters);

			// Check if there are any hits
			if (!empty($results['hits']['hits'])) {
				foreach ($results['hits']['hits'] as $_hit) {
					$search_results['ids'][] = $_hit['_source']['id'];
				}

				$search_results['total'] = $results['hits']['total']['value'];
			}
		}

		return $search_results;
	}
}
