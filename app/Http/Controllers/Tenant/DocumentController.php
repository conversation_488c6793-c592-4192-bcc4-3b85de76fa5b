<?php

namespace App\Http\Controllers\Tenant;

use App\Helpers\DocumentBuilder;
use App\Helpers\Post;
use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\SelectDocumentTemplateRequest;
use App\Models\Document;
use App\Models\DocumentTemplate;
use App\Models\DocumentTemplateSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Yajra\DataTables\DataTables;

class DocumentController extends Controller {

	public function index() {

		$device = new \Detection\MobileDetect;

		return view('documents.index', [
			'is_desktop' => !$device->isMobile() && !$device->isTablet()
		]);
	}

	public function create(SelectDocumentTemplateRequest $request) {

		// validate chosen template
		$request->validated();

		if ($document = Document::create($request->get('template_id'), Auth::check() ? Auth::id() : null)) {
			return redirect(route('section.show', [$document, $document->template->firstSection()]));
		}

		return back()->with('error', 'Hmm, nešto je pošlo po zlu. Pokušajte ponovo ili nas obavijestite o pogrešci');

	}

	public function previewPdf(Document $document) {
		return view('documents.preview', [
			'document' => $document
		]);
	}

	public function previewHtml(Request $request) {

		$request->validate([
			'document_id' => 'required',
			'section_id' => 'required'
		]);

		$id = $request->get('document_id');
		$id = \Hashids::decode($id)[0];

		$section_id = $request->get('section_id');
		$section_id = \Hashids::decode($section_id)[0];

		if(($document = Document::find($id)) && ($section = DocumentTemplateSection::find($section_id))) {

			// get all user input
			$section_data = $request->except(['_token', 'document_id', 'section_id']);

			$document_builder = new DocumentBuilder($document, $section->view);

			return $document_builder
				->updateSectionValues($section->id, $section_data)
				->getPreviewContent();
		}

		abort(400);
	}

	public function stream(Document $document) {

		$builder = new DocumentBuilder($document);

		if ($document->isDownloadable()) {
			return $builder->stream(null);
		}

		return $builder->stream();

	}

	public function download(Document $document) {

		$pdf_path = 'documents/' . $document->id . '/document.pdf';

		// precautionary check
		if (Storage::missing($pdf_path)) {
			$document->savePdf();
		}

		// read image file to avoid copy-pasting text from PDF
		if($document->shouldProtectCopyPasting()) {

			$image_pdf_path = 'documents/' . $document->id . '/document_image.pdf';

            if (Storage::missing($image_pdf_path)) {
                $document->saveImagePdf();
            }

			$pdf_path = $image_pdf_path;
        }

		// attach a (httponly=false) cookie indicating to frontend that download has started (for loader toggle)
		Cookie::queue(cookie('download_started', 1, 1, null, null, false, false));

        activity()->performedOn($document)->log('Download');

		return response()->download(
			storage_path("app/$pdf_path"),
			StringHelper::sanatizeStringForDownload($document->template->public_title) . ".pdf",
			[
				'Cache-Control' => 'public, must-revalidate, max-age=0',
				'Expires' => 'Sat, 26 Jul 1997 05:00:00 GMT'
			]
		);
	}

	public function delete(Document $document) {

		if ($document->delete()) {
			return back()->with('success', 'Uspješno ste izbrisali dokument.');
		} else {
			return back()->with('error', 'Došlo je do pogreške prilikom brisanja dokumenta.');
		}
	}

	public function duplicate(Document $document) {

		if ( ! $document->isOutdated()) {

			$cloned_document = $document->replicate();
			$cloned_document->is_sent = 1; // avoid sending emails for duplicated documents
			$cloned_document->save();

			foreach ($document->parties as $_party) {
				$_party->replicate()->fill([
					'document_id' => $cloned_document->id,
					'signed_at'   => null,
					'signature'   => null
				])->saveQuietly();
			}

			foreach ($document->sectionValues as $_section_values) {
				$_section_values->replicate()->fill([
					'document_id' => $cloned_document->id
				])->saveQuietly();
			}

			$cloned_document->createStorageDirectory();
			$cloned_document->savePdf();

            activity()->performedOn($document)->log('Duplicate');

			return back()->with('success', 'Uspješno ste duplicirali dokument.');
		} else {
			return back()->with('error', __('Document is outdated.'));
		}

	}

	public function duplicateAdmin(Document $document) {

		if ( ! $document->isOutdated()) {

			$cloned_document = $document->replicate();
			$cloned_document->is_sent = 1; // avoid sending emails for duplicated documents
			$cloned_document->user_id = Auth::id(); // overwrite ownership
			$cloned_document->save();

			foreach ($document->parties as $_party) {
				$_party->replicate()->fill([
					'document_id' => $cloned_document->id,
					'signed_at'   => null,
					'signature'   => null
				])->save();
			}

			foreach ($document->sectionValues as $_section_values) {
				$_section_values->replicate()->fill([
					'document_id' => $cloned_document->id
				])->save();
			}

			$cloned_document->createStorageDirectory();
			$cloned_document->savePdf();

            activity()->performedOn($document)->log('Duplicate (admin)');

			return back()->with('success', 'Uspješno ste duplicirali dokument.');
		} else {
			return back()->with('error', __('Document is outdated.'));
		}

	}

	public function exportToEditor(Document $document) {
		try{
			$draft = $document->createDraft();
			return redirect(route('editor.draft.edit', $draft));
		} catch(\Exception $e) {
			report($e);
			return back()->with('error', 'Ups, negdje je došlo do pogreške. Pokušajte ponovo.');
		}
	}

	public function fork(Document $document) {

		if ($document->template->isPrecontract()) {
			if ( ! $document->isOutdated()) {

				$document_template = DocumentTemplate::find($document->template->precontract_for_id);

				$cloned_document                       = $document->replicate();
				$cloned_document->template_id          = $document->template->precontract_for_id;
				$cloned_document->title                = $document_template->public_title;
				$cloned_document->save();

				foreach ($document->parties as $_party) {
					$_party->replicate()->fill([
						'document_id' => $cloned_document->id,
						'signed_at'   => null,
						'signature'   => null
					])->save();
				}

				foreach ($document->sectionValues as $_section_values) {
					$cloned_document_section = DocumentTemplateSection::where([
						'template_id' => $cloned_document->template_id,
						'title'                => $_section_values->section->title
					])->first();

					if ($cloned_document_section) {
						$_section_values->replicate()->fill([
							'document_id'                  => $cloned_document->id,
							'document_template_section_id' => $cloned_document_section->id
						])->save();
					}
				}

				$cloned_document->createStorageDirectory();
				$cloned_document->savePdf();

				activity()->performedOn($document)->log('Fork');

				return back()->with('success', 'Uspješno ste generirali ' . lcfirst($document_template->public_title) . '.');
			} else {
				return back()->with('error', __('Document is outdated.'));
			}
		} else {
			return back()->with('error', 'Potrebno je odabrati predugovor.');
		}
	}

	public function dataTables() {

		$columns = ['id', 'title', 'comment', 'user_id', 'created_at', 'updated_at', 'template_id'];

		$documents = Auth::user()->isAdmin() ?
			Document::where('is_visible', true)->select($columns)
			: Auth::user()->documents()->select($columns);

		return Datatables::of($documents->with(['template', 'template.sections', 'signedParties', 'parties', 'user']))
		                 ->editColumn('id', function ($document) {

			                 return $document->hashid();
		                 })
		                 ->editColumn('created_at', function ($document) {

			                 return date('d.m.Y.', strtotime($document->created_at));
		                 })
		                 ->editColumn('title', function ($document) {

							 $parties = $document->getPartiesString();

							 $title = !empty($parties) ?
								 $document->title . " (" . $document->getPartiesString() . ")" :
								 $document->title;

			                 if(Auth::user()->isAdmin() && $document->user) {
				                 $title.= " <div class='text-muted'><small>" . $document->user->email . "</small></div>";
			                 }

							 return $title;
		                 })
		                 ->editColumn('comment', function ($document) {

			                 return nl2br($document->comment ?: '');
		                 })
		                 ->addColumn('links', function ($document) {

			                 return [
				                 'edit'           => [
					                 'url'      => $document->signedParties->count() ? 'javascript:void(0)' : route('section.show', [
						                 $document,
						                 $document->template->firstSection()
					                 ]),
					                 'disabled' => $document->signedParties->count() ? 'disabled' : ''
				                 ],
				                 'translate'      => [
					                 'url' => route('document.translate', $document),
				                 ],
				                 'download'       => [
					                 'url' => URL::signedRoute('document.download', $document),
				                 ],
				                 'duplicate'      => [
					                 'url' => Auth::user()->isAdmin() ? route('document.duplicate.admin', $document) : route('document.duplicate', $document),
				                 ],
				                 'fork'           => [
					                 'is_precontract' => $document->template->isPrecontract(),
					                 'url'            => route('document.fork', $document),
				                 ],
				                 'exportToEditor' => [
					                 'url' => route('document.exportToEditor', $document),
				                 ],
				                 'send'           => [
					                 'url' => route('email.document', $document),
				                 ],
				                 'signatures'     => [
					                 'url' => route('signature.index', $document),
				                 ],
				                 'comment'        => [
					                 'id'      => $document->hashid(),  // because we're using ajax to update
					                 'comment' => $document->comment
				                 ],
				                 'delete'         => [
					                 'url'      => route('document.delete', $document),
					                 'disabled' => ''
				                 ],
			                 ];
		                 })
		                 ->rawColumns(['comment', 'title'])
		                 ->filter(function ($query) {

			                 // perform search by document title, comment and parties names (and email if admin)

			                 if ( ! empty(request('search')['value'])) {
				                 $term = request('search')['value'];

				                 // admin searching by email
				                 if (Auth::user()->isAdmin() && filter_var($term, FILTER_VALIDATE_EMAIL)) {
					                 $query->whereHas('user', function($q) use ($term) {
						                 $q->where('email', 'like', "%$term%");
					                 });
				                 } else {
					                 $matching_parties_document_ids = [];

					                 if (\App::environment(['production']) && Auth::user()->isAdmin()) {
						                 $parties = DB::table('document_parties')->select('document_id', 'name', 'label')->get();
					                 } else {
						                 $parties = DB::table('document_parties')
						                              ->join('documents', 'document_parties.document_id', '=', 'documents.id')
						                              ->where('documents.user_id', '=', Auth::id())
						                              ->select('document_parties.document_id', 'document_parties.name', 'document_parties.label')
						                              ->get();
					                 }

					                 // because of encryption, we need to manually check if query term matches any of the document parties
					                 foreach ($parties as $_party) {
						                 $_party_label = Crypt::decrypt($_party->label);
						                 $_party_name = $_party_label;

						                 // if authorized person, append company name
						                 if ($_parenthesis_text = StringHelper::extractTextFromParenthesis($_party_label)) {
							                 $_party_name .= " $_parenthesis_text";
						                 }

						                 if (StringHelper::stringContainsAnotherStringWords($term, $_party_name)) {
							                 $matching_parties_document_ids[] = $_party->document_id;
						                 }
					                 }

					                 $query->where(function ($query) use ($term, $matching_parties_document_ids) {
						                 $query->where('title', 'like', "%$term%");
						                 $query->orWhere('comment', 'like', "%$term%");
						                 $query->orWhereIn('id', $matching_parties_document_ids);
					                 });
				                 }
			                 }

		                 })
						 // hide sensitive columns
		                 ->removeColumn('user_id')
		                 ->removeColumn('template_id')
		                 ->removeColumn('signed_parties')
		                 ->removeColumn('template')
		                 ->removeColumn('user')
		                 ->make();
	}

	public function comment(Document $document) {
		$document->comment = request()->get('comment');
		$document->timestamps = false;  // do not update timestamps
		return $document->save();
	}
}
