<?php

namespace App\Http\Controllers\Tenant;

use App\Helpers\ElasticHelper;
use App\Helpers\Post;
use App\Http\Controllers\Controller;
use App\Models\DocumentTemplate;
use App\Models\SearchLog;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{

    public function templates()
    {
        request()->validate([
            'query' => 'max:100'
        ]);

        // log search query
        if (!empty(request('query'))) {
            SearchLog::create(request('query'));
        }

        $cache_key = 'search.template.'.App::environment().':'. request('query');

        // try fetching from cache, otherwise query search
        return Cache::tags(['template-search'])->rememberForever($cache_key, function () {

            $results = [];
            $hits = DocumentTemplate::search(request('query'));

			// add *NEW* badge to the latest template
			$max_id = DocumentTemplate::max('id');

			// add *UPDATED* badge to these templates
	        //$updated_templates = ['Ugovor o radu'];
            $updated_templates = [];

            // different result depending on whether query is empty
            if (request('query')) {
                foreach ($hits as $_hit) {

	                $_title = $_hit->public_title;

					if($_hit->id === $max_id) {
						$_title = "{$_hit->public_title} [NOVO]";
					} elseif(in_array($_hit->public_title, $updated_templates)) {
						$_title = "{$_hit->public_title} [OSVJEŽENO]";
					}

                    $results[] = [
                        'id' => $_hit->id,
                        'title' => $_title,
                    ];
                }
            } else {
                // generate category groups
                foreach ($hits as $_hit) {
                    $results[$_hit->category->order_index] = [
                        'category' => $_hit->category->title,
                        'order_index' => $_hit->category->order_index,
                        'templates' => []
                    ];
                }

                // sort categories
                $results = collect($results)->sortKeys()->keyBy('category')->toArray();

                // populate categories with templates
                foreach ($hits as $_hit) {

	                $_title = $_hit->public_title;

	                if($_hit->id === $max_id) {
		                $_title = "{$_hit->public_title} [NOVO]";
	                } elseif(in_array($_hit->public_title, $updated_templates)) {
		                $_title = "{$_hit->public_title} [OSVJEŽENO]";
	                }

                    $results[$_hit->category->title]['templates'][$_hit->categoryTemplate->order_index] = [
                        'id' => $_hit->id,
                        'title' => $_title,
                        'order_index' => $_hit->categoryTemplate->order_index
                    ];
                }

                // sort templates within categories
                foreach ($results as &$_result) {
                    $_result['templates'] = collect($_result['templates'])->sortKeys()->toArray();
                }
            }

            return [
                'results' => $results,
                'query' => request('query')
            ];
        });
    }

	private function elasticSearch($index, $query, $filters, $from, $size): array {
		$query = ElasticHelper::prepareInput($query);

		$search_results = ['ids' => [], 'total' => 0];

		if ($query || $filters) {
			$es = new ElasticHelper();
			$results = $es->search($index, $query, $size, $from, $filters);

			// Check if there are any hits
			if (!empty($results['hits']['hits'])) {
				foreach ($results['hits']['hits'] as $_hit) {
					$search_results['ids'][] = $_hit['_source']['id'];
				}

				$search_results['total'] = $results['hits']['total']['value'];
			}
		}

		return $search_results;
	}
}
