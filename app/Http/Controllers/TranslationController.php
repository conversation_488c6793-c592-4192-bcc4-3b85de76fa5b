<?php

namespace App\Http\Controllers;

use App\Helpers\ISOCountryCodes;
use App\Helpers\StringHelper;
use App\Models\Receipt;
use App\Models\TranslationDocument;
use App\Models\Translation;
use App\Models\User;
use App\Notifications\SendTranslationOrderConfirmation;
use App\Notifications\TranslationRejected;
use App\Notifications\TranslationRequested;
use App\Notifications\TranslationShipped;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Vinkla\Hashids\Facades\Hashids;
use Laravel\Cashier\Exceptions\IncompletePayment;
use Yajra\DataTables\DataTables;

class TranslationController extends Controller
{
	public function dataTables() {

		$translations = Auth::user()->isAdmin() ?
            Translation::select('translations.*')->with(['user', 'documents'])
			: Auth::user()->translations()
			              ->where('status_id', '!=', Translation::$statuses['cancelled'])
			              ->select('translations.*')->with(['user', 'documents']);

		return Datatables::of($translations)
		                 ->editColumn('id', function ($translation) {

			                 return $translation->hashid();
		                 })
		                 ->addColumn('status', function ($translation) {

			                 return $translation->status_badge;
		                 })
                         ->addColumn('has_many_documents', function ($translation) {
                             return $translation->documents->count() > 1;
                         })
		                 ->addColumn('title', function ($translation) {

			                 $title = StringHelper::generateExcerpt($translation->title, 150);

			                 if(Auth::user()->isAdmin() && $translation->user) {
				                 $title.= " <div class='text-muted'><small>" . $translation->user->email . "</small></div>";
			                 }

			                 return $title;
		                 })
		                 ->editColumn('created_at', function ($translation) {

			                 return date('d.m.Y.', strtotime($translation->created_at));
		                 })
		                 ->addColumn('links', function ($translation) {

			                 match ($translation->status_id) {
				                 Translation::$statuses['cart'] => $links = [
					                 'downloadInput' => [
						                 'url' => route(
							                 auth()->user()->isAdmin()
								                 ? 'translation.download.input.admin'
								                 : 'translation.download.input'
							                 , $translation
						                 ),
					                 ],
					                 'checkout' => [
						                 'url' => route('translation.checkout', $translation),
					                 ],
					                 'cancel' => [
						                 'url' => route('translation.cancel', $translation),
					                 ],
				                 ],
				                 Translation::$statuses['pending_invoice'], Translation::$statuses['invoiced'], Translation::$statuses['invoice_expired'] => $links = [
					                 'downloadInput' => [
						                 'url' => route(
							                 auth()->user()->isAdmin()
								                 ? 'translation.download.input.admin'
								                 : 'translation.download.input'
							                 , $translation
						                 ),
					                 ],
					                 'details' => [
						                 'url' => route(
							                 auth()->user()->isAdmin()
								                 ? 'translation.show.admin'
								                 : 'translation.show'
							                 , $translation
						                 ),
					                 ],
					                 'cancel' => [
						                 'url' => route('translation.cancel', $translation),
					                 ],
				                 ],
				                 Translation::$statuses['processing'] => $links = [
					                 'downloadInput' => [
						                 'url' => route(
							                 auth()->user()->isAdmin()
								                 ? 'translation.download.input.admin'
								                 : 'translation.download.input'
							                 , $translation
						                 ),
					                 ],
					                 'details' => [
						                 'url' => route(
							                 auth()->user()->isAdmin()
								                 ? 'translation.show.admin'
								                 : 'translation.show'
							                 , $translation
						                 ),
					                 ]
				                 ],
				                 Translation::$statuses['translated'], Translation::$statuses['delivery'], Translation::$statuses['delivered'] => $links = [
                                     'details' => [
                                         'url' => route(
                                             auth()->user()->isAdmin()
                                                 ? 'translation.show.admin'
                                                 : 'translation.show'
                                             , $translation
                                         ),
                                     ],
					                 'downloadInput' => [
						                 'url' => route(
							                 auth()->user()->isAdmin()
								                 ? 'translation.download.input.admin'
								                 : 'translation.download.input'
							                 , $translation
						                 ),
					                 ]
				                 ],
				                 default => $links = [
					                 'details' => [
						                 'url' => route(
							                 auth()->user()->isAdmin()
								                 ? 'translation.show.admin'
								                 : 'translation.show'
							                 , $translation
						                 ),
					                 ],
				                 ],
			                 };

			                 // add downloadOutput and send links only if output exists
			                 if (in_array($translation->status_id, [
					                 Translation::$statuses['translated'],
					                 Translation::$statuses['delivery'],
					                 Translation::$statuses['delivered']
				                 ]) && $translation->hasOutput()) {
				                 $links['downloadOutput'] = [
					                 'url' => URL::signedRoute('translation.download', $translation),
				                 ];
				                 $links['send'] = [
					                 'url' => route('email.translation', $translation),
				                 ];
			                 }

			                 return $links;
		                 })
		                 ->filter(function ($query) {

			                 // perform search by translation title
			                 if ( ! empty(request('search')['value'])) {
				                 $term = request('search')['value'];

				                 // admin searching by email
				                 if (Auth::user()->isAdmin() && filter_var($term, FILTER_VALIDATE_EMAIL)) {
					                 $query->whereHas('user', function($q) use ($term) {
						                 $q->where('email', 'like', "%$term%");
					                 });
				                 } else {
                                     $query->where('title', 'like', "%$term%");
				                 }
			                 }
		                 })
                         ->rawColumns(['status', 'title'])
						 // hide sensitive columns
		                 ->removeColumn('user')
		                 ->removeColumn('translation')
		                 ->removeColumn('translation_id')
		                 ->removeColumn('stripe_id')
		                 ->removeColumn('shipment_tracking_code')
		                 ->removeColumn('eta')
		                 ->removeColumn('deadline')
		                 ->removeColumn('deleted_at')
		                 ->make();
	}

	public function show(Translation $translation) {

		return view('translations.show', compact('translation'));
	}

    public function edit(Translation $translation) {
        if($translation->status_id !== Translation::$statuses['pending_invoice']) {
            return redirect(route('translation.show', $translation))
	            ->with('error', 'Ponuda za ovu narudžbu je već poslana, stoga uređivanje narudžbe više nije moguće.');
        }

        return view('translations.edit', compact('translation'));
    }

    public function update(Translation $translation) {
        activity()->causedBy(auth()->user())
            ->withProperties(['request' => request()->all()])
            ->log('Update custom translation order');

        if($translation->status_id !== Translation::$statuses['pending_invoice']) {
            abort(400);
        }

        try{
            request()->validate([
                'type' => 'required|string|in:certified,normal',
                'existing_documents' => 'required_without:documents|array|max:10',
                'documents' => 'required_without:existing_documents|array|max:10',
                'documents.*' => 'required_if:existing_documents,null|file|mimes:pdf,doc,docx,txt,jpg,jpeg,png|max:20480',
                'person_type' => 'required|string|in:individual,business',
                'binding_type' => 'required|string|in:scanned,original',
                'scanned_delivery_type' => 'required_if:binding_type,scanned|string|in:post,zagreb,rijeka',
                'original_delivery_type' => 'required_if:binding_type,original|string|in:zagreb,rijeka',

                'name' => 'required|string',
                'email' => 'required|email',
                'address' => 'required|string',
                'city' => 'required|string',
                'postal_code' => 'required|string',
                'country' => 'required|string|in:'. implode(',', array_keys(ISOCountryCodes::get())),
                'oib' => 'nullable|required_if:person_type,business|numeric',
                'phone' => 'nullable|required_if:type,certified|string',

                'use_different_shipping_address' => 'nullable|string|in:on',
                'shipping_name' => 'nullable|required_if:use_different_shipping_address,true|string',
                'shipping_address' => 'nullable|required_if:use_different_shipping_address,true|string',
                'shipping_city' => 'nullable|required_if:use_different_shipping_address,true|string',
                'shipping_postal_code' => 'nullable|required_if:use_different_shipping_address,true|string',
                'shipping_country' => 'nullable|required_if:use_different_shipping_address,true|string|in:'. implode(',', array_keys(ISOCountryCodes::get())),
                'shipping_phone' => [
                    'nullable',
                    'string',
                    function ($attribute, $value, $fail) {
                        if (request()->get('use_different_shipping_address') == 'true' && request()->get('type') == 'certified' && empty($value)) {
                            $fail();
                        }
                    },
                ],
            ]);

            DB::beginTransaction();

            // generate translation title from both existing and new documents
            $new_document_titles = collect(request()->file('documents'))->map(function ($document) {
                return $document->getClientOriginalName();
            });

            $decoded_ids = collect(request()->input('existing_documents'))->map(function ($hashed_id) {
                return Hashids::decode($hashed_id)[0];
            });

            $existing_documents = TranslationDocument::whereIn('id', $decoded_ids)->get()->keyBy('id');

            $existing_document_titles = $decoded_ids->map(function ($id) use ($existing_documents) {
                return $existing_documents->get($id) ? $existing_documents->get($id)->title : null;
            })->filter();

            $title = $new_document_titles->merge($existing_document_titles)->implode(', ');

            // create translation
            if($translation->update([
                'type_id' => Translation::$types[request()->get('type')],
                'binding_type_id' => Translation::$binding_types[request()->get('binding_type')],
                'scanned_delivery_type_id' => request()->get('binding_type') === 'scanned' ? Translation::$scanned_delivery_types[request()->get('scanned_delivery_type')] : null,
                'original_delivery_type_id' => request()->get('binding_type') === 'original' ? Translation::$original_delivery_types[request()->get('original_delivery_type')] : null,
                'title' => $title,
            ])) {

                // delete translation documents which are not found in existing_documents
                if(request()->has('existing_documents')) {
                    $existing_documents_ids = collect(request()->get('existing_documents'))->map(function ($hashed_id) {
                        return Hashids::decode($hashed_id)[0];
                    })->toArray();

                    TranslationDocument::where('translation_id', $translation->id)->whereNotIn('id', $existing_documents_ids)->delete();
                } else {
	                TranslationDocument::where('translation_id', $translation->id)->delete();
                }

                // create documents
                if(request()->has('documents')) {
                    foreach(request()->file('documents') as $_uploaded_document) {
                        $_document = TranslationDocument::create([
                            'translation_id' => $translation->id,
                            'title' => $_uploaded_document->getClientOriginalName(),
                        ]);

                        // update input path
                        $_document->update([
                            'input_path' => 'app/translations/' . $translation->id . '/documents/' . $_document->id . '/' . $_uploaded_document->getClientOriginalName(),
                        ]);

                        // save uploaded file to translation folder
                        $_document->createStorageDirectory();

                        $_uploaded_document->move(
                            storage_path('app/translations/' . $translation->id . '/documents' . '/' . $_document->id),
                            $_uploaded_document->getClientOriginalName()
                        );
                    }
                }

                $redirect_url = $this->finalizeOrder(
                    $translation,
                    request()->get('type') === 'certified'
                );

	            // log order history
	            activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Narudžba ažurirana.');

                DB::commit();

                return redirect()->to($redirect_url);
            }

        } catch(ValidationException $e) {
            report($e);

	        $contains_documents_error = collect($e->errors())->keys()->contains(function ($key) {
		        return Str::is('documents.*', $key);
	        });

            if ($contains_documents_error) {
                return redirect()->back()->withInput()->with('error', 'Dopušteni formati dokumenta su: pdf, doc, docx, txt, jpg, jpeg i png. Maksimalni broj dokumenata je 10 x 20 MB.');
            }
        }

        return redirect()->back()->withInput()->with('error', 'Došlo je do pogreške prilikom obrade zahtjeva.');
    }

	public function customCheckout() {

		return view('translations.custom_checkout');
	}

	public function processCustomCheckout() {

		activity()->causedBy(auth()->user())
		          ->withProperties(['request' => request()->all()])
		          ->log('Process custom translation order');

		try{
			request()->validate([
				'type' => 'required|string|in:certified,normal',
                'documents' => 'required|array|max:10',
                'documents.*' => 'required|file|mimes:pdf,doc,docx,txt,jpg,jpeg,png|max:20480',
				'person_type' => 'required|string|in:individual,business',
				'binding_type' => 'required|string|in:scanned,original',
				'scanned_delivery_type' => 'required_if:binding_type,scanned|string|in:post,zagreb,rijeka',
				'original_delivery_type' => 'required_if:binding_type,original|string|in:zagreb,rijeka',

				'name' => 'required|string',
				'email' => 'required|email',
				'address' => 'required|string',
				'city' => 'required|string',
				'postal_code' => 'required|string',
				'country' => 'required|string|in:'. implode(',', array_keys(ISOCountryCodes::get())),
				'oib' => 'nullable|required_if:person_type,business|numeric',
				'phone' => 'nullable|required_if:type,certified|string',

				'use_different_shipping_address' => 'nullable|string|in:on',
				'shipping_name' => 'nullable|required_if:use_different_shipping_address,true|string',
				'shipping_address' => 'nullable|required_if:use_different_shipping_address,true|string',
				'shipping_city' => 'nullable|required_if:use_different_shipping_address,true|string',
				'shipping_postal_code' => 'nullable|required_if:use_different_shipping_address,true|string',
				'shipping_country' => 'nullable|required_if:use_different_shipping_address,true|string|in:'. implode(',', array_keys(ISOCountryCodes::get())),
				'shipping_phone' => [
					'nullable',
					'string',
					function ($attribute, $value, $fail) {
						if (request()->get('use_different_shipping_address') == 'true' && request()->get('type') == 'certified' && empty($value)) {
							$fail();
						}
					},
				],
			]);

			DB::beginTransaction();

            // generate translation title
            $title = collect(request()->file('documents'))->map(function ($document) {
                return $document->getClientOriginalName();
            })->implode(', ');

			// create translation
			if($translation = Translation::create([
                'source_id' => Translation::$sources['custom'],
                'type_id' => Translation::$types[request()->get('type')],
                'binding_type_id' => Translation::$binding_types[request()->get('binding_type')],
                'scanned_delivery_type_id' => request()->get('binding_type') === 'scanned' ? Translation::$scanned_delivery_types[request()->get('scanned_delivery_type')] : null,
                'original_delivery_type_id' => request()->get('binding_type') === 'original' ? Translation::$original_delivery_types[request()->get('original_delivery_type')] : null,
                'user_id' => auth()->id() ?? null,
                'guest_id' => auth()->guest() ? User::guestID() : null,
				'title' => $title,
                'lang' => 'en',
			])) {

				// log order history
				activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Narudžba stvorena.');

				// create documents
                foreach(request()->file('documents') as $_uploaded_document) {
                    $_document = TranslationDocument::create([
                        'translation_id' => $translation->id,
                        'title' => $_uploaded_document->getClientOriginalName(),
                    ]);

                    // update input path
                    $_document->update([
                        'input_path' => 'app/translations/' . $translation->id . '/documents/' . $_document->id . '/' . $_uploaded_document->getClientOriginalName(),
                    ]);

                    // save uploaded file to translation folder
                    $_document->createStorageDirectory();

                    $_uploaded_document->move(
                        storage_path('app/translations/' . $translation->id . '/documents' . '/' . $_document->id),
                        $_uploaded_document->getClientOriginalName()
                    );
                }

				$redirect_url = $this->finalizeOrder(
					$translation,
					request()->get('type') === 'certified'
				);

				DB::commit();

				// send notification to admin (if user has created account, otherwise later)
                if(auth()->check()) {
	                $notification = new TranslationRequested($translation);
	                Notification::route('mail', User::getTranslationSupportEmail())->notify($notification);
                } else {
	                Session::put('auth_message',
		                'Prijavite se ili <a href="/register">besplatno napravite račun</a> za prijevod dokumenta.'
	                );
                }

				return redirect()->to($redirect_url);
			}

		} catch(ValidationException $e) {
			report($e);

            $errors = collect($e->errors());

			if ($errors->has('documents') || $errors->keys()->some(fn($key) => str_starts_with($key, 'documents.'))) {
				return redirect()->back()->with('error', 'Dopušteni formati dokumenta su: pdf, doc, docx, txt, jpg, jpeg i png. Maksimalni broj dokumenata je 10, a najveća veličina svakog dokumenta 20 MB.');
			}

		} catch(\Exception $e) {
			report($e);
		}

        return redirect()->back()->with('error', 'Došlo je do pogreške prilikom obrade zahtjeva.');
	}

	public function checkout(Translation $translation) {

		if($translation->status_id !== Translation::$statuses['cart']) {

			return redirect(route('documents'))
				->withCookie(cookie('documentsTab', 'translations',60*24*360, '/', null, null, false));
		}

		return view('translations.checkout', compact('translation'));
	}

	public function process() {

        activity()->causedBy(auth()->user())
            ->withProperties(['request' => request()->all()])
            ->log('Process translation order');

		try{
			request()->validate([
				'type' => 'required|string|in:certified,normal',
				'person_type' => 'required|string|in:individual,business',
				'payment_method' => 'required|string', // generated by Stripe.js
				'translation_id' => 'required|string',

				'name' => 'required|string',
				'email' => 'required|email',
				'address' => 'required|string',
				'city' => 'required|string',
				'postal_code' => 'required|string',
				'country' => 'required|string|in:'. implode(',', array_keys(ISOCountryCodes::get())),
				'oib' => 'nullable|required_if:person_type,business|numeric',
				'phone' => 'nullable|required_if:type,certified|string',

				'use_different_shipping_address' => 'nullable|string|in:on',
				'shipping_name' => 'required_if:use_different_shipping_address,true|string',
				'shipping_address' => 'required_if:use_different_shipping_address,true|string',
				'shipping_city' => 'required_if:use_different_shipping_address,true|string',
				'shipping_postal_code' => 'required_if:use_different_shipping_address,true|string',
				'shipping_country' => 'required_if:use_different_shipping_address,true|string|in:'. implode(',', array_keys(ISOCountryCodes::get())),
				'shipping_phone' => [
					'nullable',
					'string',
					function ($attribute, $value, $fail) {
						if (request()->get('use_different_shipping_address') == 'true' && request()->get('type') == 'certified' && empty($value)) {
							$fail();
						}
					},
				],
			]);
		} catch(ValidationException $e) {
			report($e);

            return response()->json([
                'status' => 'error',
                'message' => 'Ups, nešto je pošlo po zlu. Pokušajte ponovo ili nas kontaktirajte.',
            ]);
		}

		// translation_id is a hashid
		$translation_id = request()->get('translation_id');
		$translation = Translation::find(Hashids::decode($translation_id)[0]);

		if(!auth()->user()->can('update', $translation)) {
			return response()->json([
				'status' => 'error',
				'message' => 'Ups, nešto je pošlo po zlu. Pokušajte ponovo ili nas kontaktirajte.',
			]);
		}

		if($translation->status_id !== Translation::$statuses['cart']) {
			return response()->json([
				'status' => 'error',
				'message' => 'Ups, nešto je pošlo po zlu. Pokušajte ponovo ili nas kontaktirajte.',
			]);
		}

		$type = request()->get('type');
		$is_certified = $type === 'certified';
		$payment_method = request()->get('payment_method');
        $full_title = ($is_certified ? 'Ovjereni prijevod dokumenta: ' : 'Prijevod dokumenta: ') . $translation->document->title . ' (engleski ↔ hrvatski)';
		$description = 'Rezervacija sredstava za ' . lcfirst($full_title);
        $price = $is_certified ? $translation->certified_price_cents : $translation->normal_price_cents;

        try {
            // ensure stripe customer exists
            $this->createStripeCustomer();

            // create or retrieve payment intent
            if(request()->get('pid')) {
                $intent = auth()->user()->stripe()->paymentIntents->retrieve(
                    request()->get('pid')
                );
            } else {
                try {
                    $intent = auth()->user()->charge($price, $payment_method, [
                        'description' => $description,
                        'capture_method' => 'manual',
                        'metadata' => [
                            'url' => route('translation.show', $translation),
                        ],
                    ]);
                } catch(IncompletePayment $e) {
                    return $this->incompletePaymentResponse($e->payment->clientSecret());
                }
            }

            // finalize order if payment succeeded
            if ($intent->status === 'requires_capture') {
                DB::beginTransaction();

                $redirect_url = $this->finalizeOrder(
                    $translation,
	                $is_certified,
                    $intent
                );

                DB::commit();

	            // send notification to admin
	            $notification = new TranslationRequested($translation);
	            Notification::route('mail', User::getTranslationSupportEmail())->notify($notification);

                return response()->json([
                    'status' => 'success',
                    'redirect_url' => $redirect_url
                ]);
            }

        } catch (Exception $e) {
            report($e);
        }

		return response()->json([
			'status' => 'error',
			'message' => 'Ups, nešto je pošlo po zlu. Pokušajte ponovo ili nas kontaktirajte.',
		]);
	}

    // create stripe customer
    private function createStripeCustomer() {

        auth()->user()->createOrGetStripeCustomer([
            'name' => request()->get('name'),
            'email' => request()->get('email'),
            'preferred_locales' => ['hr'],
            'address' => [
                'line1' => request()->get('address'),
                'city' => request()->get('city'),
                'postal_code' => request()->get('postal_code'),
                'country' => request()->get('country'),
            ],
            'metadata' => [
                'id' => auth()->user()->id,
            ]
        ]);
    }

    private function incompletePaymentResponse($client_secret)
    {
        return response()->json([
            'status' => 'incomplete',
            'client_secret' => $client_secret,
        ]);
    }

	private function finalizeOrder(Translation $translation, $is_certified, $intent = null) {

        $uses_different_shipping_address = request()->get('use_different_shipping_address') === 'on';

		$customer_data = [
			'type' => request()->get('person_type'),
			'name' => request()->get('name'),
			'email' => request()->get('email'),
			'address'=> request()->get('address'),
			'city' => request()->get('city'),
			'postal_code' => request()->get('postal_code'),
			'country' => ISOCountryCodes::codeToCountry(request()->get('country')),
			'oib' => request()->get('oib'),
			'phone' => request()->get('phone'),
			'shipping_name' => null,
            'shipping_address' => null,
            'shipping_city' => null,
            'shipping_postal_code' => null,
            'shipping_country' => null,
            'shipping_phone' => null
		];

        // for certified translations, add shipping data
        if($is_certified) {
            $customer_data = array_merge($customer_data, [
                'shipping_name' => $uses_different_shipping_address ? request()->get('shipping_name') : request()->get('name'),
                'shipping_address' => $uses_different_shipping_address ? request()->get('shipping_address') : request()->get('address'),
                'shipping_city' => $uses_different_shipping_address ? request()->get('shipping_city') : request()->get('city'),
                'shipping_postal_code' => $uses_different_shipping_address ? request()->get('shipping_postal_code') : request()->get('postal_code'),
                'shipping_country' => ISOCountryCodes::codeToCountry($uses_different_shipping_address ? request()->get('shipping_country') : request()->get('country')),
                'shipping_phone' => $uses_different_shipping_address ? request()->get('shipping_phone') : request()->get('phone'),
            ]);
        }

        // update translation type
        $translation->update([
            'type_id' => $is_certified ? Translation::$types['certified'] : Translation::$types['normal'],
        ]);

        $charge = $intent ?
	        auth()->user()->stripe()->charges->retrieve($intent->latest_charge)
	        : null;

        // update translation
        $translation->update([
            'stripe_id' => $intent ? $intent->id : null,
            'status_id' => $intent ? Translation::$statuses['processing'] : Translation::$statuses['pending_invoice'],
            'payment_status_id' => $intent ? Translation::$payment_statuses['reserved'] : Translation::$payment_statuses['pending_invoice'],
            'eta' => $intent ? $translation->calculateETA() : null,
            'deadline' => $intent ? $translation->calculateDeadline() : null,
            'shipping_name' => $customer_data['shipping_name'],
            'shipping_address' => $customer_data['shipping_address'],
            'shipping_city' => $customer_data['shipping_city'],
            'shipping_postal_code' => $customer_data['shipping_postal_code'],
            'shipping_country' => $customer_data['shipping_country'],
            'shipping_phone' => $customer_data['shipping_phone'],
            'shipment_tracking_code' => null, // in case order was previously cancelled
        ]);

		if(!$intent) {  // custom translation order
            $translation->generateInvoice($translation, $customer_data);
		} else {
            $this->generateReceipt($translation, $is_certified, $customer_data, $charge);

			// send order confirmation email
			$notification = new SendTranslationOrderConfirmation($translation);
			Notification::route('mail', $translation->user->email)->notify($notification);
		}

		// show translation details
		return route('translation.show', [
			'translation' => $translation,
			'success' => true
		]);
	}

	private function generateReceipt(Translation $translation, $is_certified, $customer_data, $charge) {

		$price = $is_certified ? $translation->certified_price : $translation->normal_price;
		$payment_method = 'Kartica - ' . str($charge->payment_method_details->card->brand)->upper() . ' (' . $charge->payment_method_details->card->last4 . ')';

		$items = [
			[
				'name' => $translation->document->full_title,
				'unit' => 'kartica',
				'unit_amount' => $is_certified ? $translation::CERTIFIED_PRICE_PER_CARD : $translation::NORMAL_PRICE_PER_CARD,
				'quantity' => $translation->document->card_count,
				'amount' => $price,
				'vat'   => Translation::VAT,
			]
		];

		$translation_data = [
			'total' => $price,
			'vat' => Translation::VAT,
            'shipping' => $is_certified ? 0 : null,
			'items' => $items,
			'payment_method' => $payment_method,
		];

		$translation->generateReceipt($translation, $translation_data, $customer_data);
	}

	public function cancel(Translation $translation) {
		if ($translation->update(['status_id' => Translation::$statuses['cancelled']])) {
			// log order history
			activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Narudžba otkazana.');
			return redirect(route('documents'))
				->withCookie(cookie('documentsTab', 'translations',60*24*360, '/', null, null, false))
				->with('success', 'Uspješno ste uklonili zahtjev za prijevod dokumenta.');
		} else {
			return redirect(route('documents'))
				->withCookie(cookie('documentsTab', 'translations',60*24*360, '/', null, null, false))
				->with('error', 'Došlo je do pogreške prilikom brisanja zahtjeva za prijevod dokumenta.');
		}
	}

	public function delete(Translation $translation) {
		$translation->delete();
		activity()->performedOn($translation)->log('Delete');
		return redirect(route('documents'))
			->withCookie(cookie('documentsTab', 'translations',60*24*360, '/', null, null, false))
			->with('success', 'Uspješno ste izbrisali narudžbu prijevoda.');
	}

	public function downloadOrderConfirmation(Translation $translation) {
		if(!$translation->orderConfirmation) {
			abort(404);
		}

		activity()->performedOn($translation->orderConfirmation)->log('Download');
		return $translation->orderConfirmation->download();
	}

    public function downloadReceipt(Translation $translation) {
		if(!$translation->receipt) {
			abort(404);
		}
        activity()->performedOn($translation->receipt)->log('Download');
        return $translation->receipt->download();
    }

    public function downloadStornoReceipt(Translation $translation) {
		if(!$translation->stornoReceipt) {
			abort(404);
		}
        activity()->performedOn($translation->stornoReceipt)->log('Download');
        return $translation->stornoReceipt->download();
    }

    public function authorizePayment(Translation $translation) {

        try{
            $translation->authorizePayment();
            activity()->performedOn($translation)->log('Authorize payment');

	        // log order history
	        activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Plaćanje autorizirano.');

            return redirect(route('translation.show.admin', $translation))->with('success', 'Uspješno ste autorizirali plaćanje.');
        } catch(\Exception $e) {
            report($e);
            return redirect(route('translation.show.admin', $translation))->with('error', 'Došlo je do pogreške prilikom autorizacije naplate.');
        }
    }

    public function reject(Translation $translation) {

		request()->validate([
			'reason' => 'required|string', // what we're displaying on the order page
			'notification' => 'required|string', // what we're sending to user via email notification
		]);

        try{
	        $translation->update([
		        'status_id' => Translation::$statuses['rejected'],
		        'rejected_reason' => request()->input('reason'),
	        ]);

	        // send notification to user
	        $notification = new TranslationRejected($translation, request()->input('notification'));
	        Notification::route('mail', $translation->user->email)->notify($notification);

	        activity()->performedOn($translation)->log('Reject');

	        // log order history
	        activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Narudžba odbijena.');

			if($translation->stripe_id) {
				$translation->releaseReservedFunds();
			}

            return redirect(route('translation.show.admin', $translation))->with('success', 'Uspješno ste otkazali narudžbu.');
        } catch(\Exception $e) {
            report($e);
            return redirect(route('translation.show.admin', $translation))->with('error', 'Došlo je do pogreške prilikom otkazivanja narudžbe.');
        }
    }

    public function ship(Translation $translation) {

        request()->validate([
            'shipment_tracking_code' => 'required|string',
        ]);

        $translation->update([
            'status_id' => Translation::$statuses['delivery'],
            'shipment_tracking_code' => request()->get('shipment_tracking_code'),
        ]);

        activity()->performedOn($translation)->log('Ship');

	    // log order history
	    activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Pošiljka dodana.');

	    // send notification to user
	    $notification = new TranslationShipped($translation);
	    Notification::route('mail', $translation->user->email)->notify($notification);

        return redirect(route('translation.show.admin', $translation))->with('success', 'Uspješno ste dodali pošiljku za narudžbu.');
    }

	public function editShipment(Translation $translation) {
		request()->validate([
			'shipment_tracking_code' => 'required|string',
		]);

		$translation->update([
			'shipment_tracking_code' => request()->get('shipment_tracking_code'),
		]);

		activity()->performedOn($translation)->log('Edit shipment');

		// log order history
		activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Pošiljka uređena.');

		return redirect(route('translation.show.admin', $translation))->with('success', 'Uspješno ste uredili pošiljku za narudžbu.');
	}

    public function deliver(Translation $translation) {
        $translation->update([
            'status_id' => Translation::$statuses['delivered'],
        ]);

        activity()->performedOn($translation)->log('Deliver');

	    // log order history
	    activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Narudžba dostavljena.');

        return redirect(route('translation.show.admin', $translation))->with('success', 'Narudžba je uspješno dostavljena.');
    }

    public function void(Translation $translation) {

	    request()->merge(['refund' => request()->has('refund')]);

		if(!$translation->receipt || !$translation->receipt->isFiscalized()) {
			return redirect()->back()->with('error', 'Ova narudžba nema fiskalizirani račun.');
		}

	    $translation->void(
			request()->input('refund')
	    );

        activity()->performedOn($translation)->log('Void');

	    // log order history
	    if(request()->get('note')) {
		    activity()->performedOn($translation)->withProperties(['type' => 'history'])->log(request()->get('note'));
	    }

		$description = request()->get('refund') ? 'Narudžba stornirana s povratom sredstava.' : 'Narudžba stornirana bez povrata sredstava.';
	    activity()->performedOn($translation)->withProperties(['type' => 'history'])->log($description);

        return redirect(route('translation.show.admin', $translation))
            ->with('success', 'Uspješno ste stornirali narudžbu.')
            ->withCookie(cookie('documentsTab', 'translations',60*24*360, '/', null, null, false));
    }

    public function requestReinvoice(Translation $translation) {

        if($translation->status_id !== Translation::$statuses['invoice_expired']) {
            abort(400);
        }

        $translation->update([
            'status_id' => Translation::$statuses['pending_invoice'],
        ]);

	    // send notification to admins
	    $notification = new TranslationRequested($translation);
	    Notification::route('mail', User::getTranslationSupportEmail())->notify($notification);

        activity()->performedOn($translation)->log('Request reinvoice');

	    // log order history
	    activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Nova ponuda zatražena.');

        return redirect(route('translation.show', $translation));
    }

	public function trackShipment(Translation $translation) {
		return redirect()->away($translation->shipment_tracking_url);
	}

	public function recreateReceipt(Translation $translation)
	{
		$data = request()->validate([
			'type'        => 'required|string|in:certified,normal',
			'person_type' => 'required|string|in:individual,business',
			'name'        => 'required|string',
			'email'       => 'required|email',
			'address'     => 'required|string',
			'city'        => 'required|string',
			'postal_code' => 'required|string',
			'country'     => 'required|string|in:' . implode(',', array_keys(ISOCountryCodes::get())),
			'oib'         => 'nullable|required_if:person_type,business|numeric',
			'phone'       => 'nullable|required_if:type,certified|string',
		]);

		if (!$translation->receipt()->exists()) {
			return redirect()->back()->with('error', 'Narudžba nema račun.');
		}

		DB::beginTransaction();

		$previous_receipt = $translation->receipt;

		$customer_data = array_merge($data, [
			'type' => $data['person_type'],
		]);

		$receipt_details = [
			'total'          => $previous_receipt->total,
			'status_id'      => $previous_receipt->status_id,
			'vat'            => $previous_receipt->vat,
			'shipping'       => $previous_receipt->shipping,
			'payment_method' => $previous_receipt->payment_method,
			'items'          => $previous_receipt->items,
		];

		$new_receipt = $translation->generateReceipt($translation, $receipt_details, $customer_data);
		$new_receipt->update(['status_id' => Receipt::$statuses['paid']]);

		$translation->update(['status_id' => $this->determineTranslationStatus($translation)]);

		// hide previous receipt(s)
		$previous_receipt->update(['is_visible' => false]);
		if ($previous_receipt->storno) {
			$previous_receipt->storno->update(['is_visible' => false]);
		}

		DB::commit();

		// log order history
		activity()->performedOn($translation)->withProperties(['type' => 'history'])->log('Račun ispravljen.');

		return redirect()->back()->with('success', 'Uspješno ste ispravili račun.');
	}

	private function determineTranslationStatus(Translation $translation): int
	{
		if ($translation->activities()->where('description', 'Deliver')->exists()) {
			return Translation::$statuses['delivered'];
		}

		if ($translation->shipmentExists()) {
			return Translation::$statuses['delivery'];
		}

		if ($translation->hasOutput()) {
			return Translation::$statuses['translated'];
		}

		return Translation::$statuses['processing'];
	}
}
