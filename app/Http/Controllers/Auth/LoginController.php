<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Rules\GoogleReCaptchaValidationRule;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller {

	/*
	|--------------------------------------------------------------------------
	| Login Controller
	|--------------------------------------------------------------------------
	|
	| This controller handles authenticating users for the application and
	| redirecting them to your home screen. The controller uses a trait
	| to conveniently provide its functionality to your applications.
	|
	*/

	use AuthenticatesUsers;


	/**
	 * Create a new controller instance.
	 *
	 * @return void
	 */
	public function __construct() {

		$this->middleware('guest')->except('logout');
	}

	/**
	 * Where to redirect users after login.
	 */
	public function redirectTo()
	{
		return redirect()->intended(route('documents'))->getTargetUrl();
	}

	/**
	 * Get the needed authorization credentials from the request.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return array
	 */
	protected function credentials(Request $request)
	{
		return array_merge($request->only($this->username(), 'password'), ['banned_at' => null]);
	}


	/**
	 * Validate the user login request.
	 *
	 * @param Request $request
	 *
	 * @return void
	 *
	 * @throws ValidationException
	 */
	protected function validateLogin(Request $request): void {
		$validate_params = [
			$this->username() => 'required|string',
			'password' => 'required|string',
			'g-recaptcha-response' => [new GoogleReCaptchaValidationRule('login')]
		];

		Validator::make($request->all(), $validate_params)->validate();
	}

	/**
	 * Handle a failed login attempt.
	 *
	 * If the user exists and is banned, show a custom banned message.
	 * Otherwise, show the default failed login message.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return void
	 *
	 * @throws \Illuminate\Validation\ValidationException
	 */

	protected function sendFailedLoginResponse(Request $request)
	{
		$user = User::where($this->username(), $request->{$this->username()})->first();

		if ($user && $user->banned_at !== null) {
			throw ValidationException::withMessages([
				$this->username() => [__('auth.banned')],
			]);
		}

		throw ValidationException::withMessages([
			$this->username() => [trans('auth.failed')],
		]);
	}



	public function logout(Request $request)
    {
        $this->guard()->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        if ($response = $this->loggedOut($request)) {
            return $response;
        }

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect('/login');   // redirect to login page
    }
}
