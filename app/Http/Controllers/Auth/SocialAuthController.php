<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\EmailPreference;
use App\Models\User;
use App\Models\UserOptions;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Session;

class SocialAuthController extends Controller
{
    /**
     * Redirect the user to the provider authentication page.
     *
     */
    public function redirectToProvider($provider)
    {
		// save newsletter preference
		if(request()->has('newsletter')) {
			Session::put('newsletter', 1);
		}

		if(!in_array($provider, ['google', 'facebook', 'twitter', 'slack', 'linkedin', 'google', 'gitlab', 'github', 'bitbucket'])) {
			abort(400);
		}

        return Socialite::driver($provider)->redirect();
    }

    /**
     * Obtain the user information from the provider.
     *
     */
    public function handleProviderCallback($provider)
    {
        try {
            $user = Socialite::driver($provider)->user();
        } catch (Exception $e) {
	        report($e);
            return redirect('/login')->with('error', 'Ups, ne<PERSON>to je po<PERSON>lo po zlu. Pokušajte ponovo ili nas kontaktirajte.');
        }

		$existing_user = User::where('email', $user->getEmail())->first();

		// Banned check
		if ($existing_user && $existing_user->banned_at !== null) {
			return redirect('/login')->withErrors([
				'email' => __('auth.banned'),
			]);
		}

		if ($existing_user) {
			Auth::login($existing_user, true);
		} else {
			$new_user = User::create([
				'name' => $user->getName(),
				'email' => $user->getEmail(),
				'auth_id' => $user->getId(),
				'auth_type' => $provider,
				'email_verified_at' => Carbon::now(), // no need for email verification
			]);

			EmailPreference::updateOrCreate(
				['email' => $new_user->email],
				[
					'general' => true,
					'newsletter' => Session::has('newsletter')
				]
			);

			if (Session::has('newsletter')) {
				Session::forget('newsletter');
			}

			UserOptions::create([
				'user_id' => $new_user->id,
				'is_wizard_tutorial_shown' => \Cookie::has('wizardTutorialShown')
			]);

			Auth::login($new_user, true);
		}

        // if new user OR user has created new document(s) in guest mode
        if (!$existing_user || Session::has('success')) {

			// if no message is set, set a default welcome message
	        if(!Session::has('success')) {
		        Session::flash('success', 'Uspješno ste izradili Pravomat račun.');
	        }

            return redirect(route('documents'));
        }

        return redirect()->intended(route('documents'));
    }
}
