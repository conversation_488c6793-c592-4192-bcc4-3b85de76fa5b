<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\DocumentHelperAI;
use App\Http\Controllers\Controller;
use App\Models\DocumentTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;

class DocumentAIController extends Controller
{
    public function __construct()
    {
        if (!App::environment(['local', 'staging'])) {
            abort(404);
        }
    }

    /**
     * Display the admin AI template management view.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $templates = DocumentTemplate::orderBy('title')->get();
        return view('admin.document-ai.index', compact('templates'));
    }

    /**
     * Process the AI update request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function process(Request $request)
    {
        set_time_limit(0);
        ini_set('max_execution_time', 0);

        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:document_templates,id',
            'prompt' => 'required|string|min:10',
        ], [
            'template_id.required' => 'Molimo odaberite predložak.',
            'template_id.exists' => 'Odabrani predložak ne postoji.',
            'prompt.required' => 'Molimo unesite upute za AI.',
            'prompt.min' => 'Upute moraju sadržavati najmanje 10 znakova.'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $template = DocumentTemplate::findOrFail($request->template_id);
            $documentHelper = new DocumentHelperAI($template->title);

            // Start the update process
            if($documentHelper->updateDocument($request->prompt)) {
                // Log the successful update
                Log::info('AI template update completed', [
                    'template' => $template->title,
                    'user_id' => auth()->id(),
                    'prompt' => $request->prompt
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Predložak je uspješno ažuriran',
                    'template' => [
                        'id' => $template->id,
                        'title' => $template->title,
                        'public_title' => $template->public_title
                    ]
                ]);
            } else {
                Log::error('AI template update failed', [
                    'template' => $template->title,
                    'user_id' => auth()->id(),
                    'prompt' => $request->prompt
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Došlo je do pogreške prilikom ažuriranja predloška.'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error in AI template update', [
                'error' => $e->getMessage(),
                'template_id' => $request->template_id,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Došlo je do pogreške prilikom ažuriranja predloška: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if backups exist for a template.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkBackups(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:document_templates,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Neispravan predložak.'
            ], 422);
        }

        try {
            $template = DocumentTemplate::findOrFail($request->template_id);
            $hasBackups = $this->templateHasBackups($template->title);

            return response()->json([
                'success' => true,
                'hasBackups' => $hasBackups
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Došlo je do pogreške: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if a template has backup files.
     *
     * @param  string  $templateTitle
     * @return bool
     */
    private function templateHasBackups($templateTitle)
    {
        $documentHelper = new DocumentHelperAI($templateTitle);
        $directories = $documentHelper->getDirectories();

        foreach ($directories as $directory) {
            if (!is_dir($directory)) {
                continue;
            }

            $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory));
            foreach ($iterator as $file) {
                if ($file->isFile() && str_starts_with($file->getBasename(), 'backup_')) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Restore backups for a template.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function restoreBackups(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:document_templates,id',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.document-ai.index')
                ->with('error', 'Neispravan predložak.');
        }

        try {
            $template = DocumentTemplate::findOrFail($request->template_id);
            $documentHelper = new DocumentHelperAI($template->title);

            // Start output buffering to capture any output
            ob_start();
            $documentHelper->restoreBackups();
            ob_end_clean();

            // Log the successful restoration
            Log::info('AI template backups restored', [
                'template' => $template->title,
                'user_id' => auth()->id()
            ]);

            return redirect()->route('admin.document-ai.index')
                ->with('success', 'Sigurnosne kopije predloška su uspješno vraćene.');

        } catch (\Exception $e) {
            Log::error('Error restoring AI template backups', [
                'error' => $e->getMessage(),
                'template_id' => $request->template_id,
                'user_id' => auth()->id()
            ]);

            return redirect()->route('admin.document-ai.index')
                ->with('error', 'Došlo je do pogreške prilikom vraćanja sigurnosnih kopija: ' . $e->getMessage());
        }
    }
}
