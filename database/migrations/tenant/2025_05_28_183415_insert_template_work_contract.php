<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

		tenancy()->initialize('client');

        DB::insert("INSERT INTO `document_templates` (
            `title`, `public_title`, `tags`, `time_to_fill`, `slug`,
            `precontract_for_id`, `type_id`, `is_visible`, `created_at`, `updated_at`
        ) VALUES (
            'WorkContract', 'Ugovor o radu', 'ugovor o radu, rad, test',
            '10', 'ugovor-o-radu', NULL,
            '1', 1, NOW(), NOW())");

        $result = DB::select('SELECT MAX(id) as id FROM document_templates');
        $template_id = intval($result[0]->id);

        DB::insert("INSERT INTO `document_categories_templates` (
            `category_id`, `template_id`, `order_index`, `created_at`, `updated_at`
        ) VALUES ('1', ?, '1', NOW(), NOW())", [$template_id]);

        DB::insert("INSERT INTO `document_template_sections` (
            `template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`
        ) VALUES (?, 'Stranke', 'parties', '1', 'Stranke', NOW(), NOW())", [$template_id]);

        DB::insert("INSERT INTO `document_template_sections` (
            `template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`
        ) VALUES (?, 'Završne odredbe', 'final_provisions', '2', 'Završne odredbe', NOW(), NOW())", [$template_id]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

		tenancy()->initialize('client');

        $template_id = DB::table('document_templates')->where('title', 'WorkContract')->value('id');
        DB::table('document_templates')->where('id', $template_id)->delete();
        DB::table('document_categories_templates')->where('template_id', $template_id)->delete();
        DB::table('document_template_sections')->where('template_id', $template_id)->delete();

    }
};
