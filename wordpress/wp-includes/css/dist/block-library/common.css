:root{
  --wp-admin-theme-color:#007cba;
  --wp-admin-theme-color--rgb:0, 124, 186;
  --wp-admin-theme-color-darker-10:#006ba1;
  --wp-admin-theme-color-darker-10--rgb:0, 107, 161;
  --wp-admin-theme-color-darker-20:#005a87;
  --wp-admin-theme-color-darker-20--rgb:0, 90, 135;
  --wp-admin-border-width-focus:2px;
  --wp-block-synced-color:#7a00df;
  --wp-block-synced-color--rgb:122, 0, 223;
  --wp-bound-block-color:#9747ff;
}
@media (min-resolution:192dpi){
  :root{
    --wp-admin-border-width-focus:1.5px;
  }
}
.wp-element-button{
  cursor:pointer;
}

:root{
  --wp--preset--font-size--normal:16px;
  --wp--preset--font-size--huge:42px;
}
:root .has-very-light-gray-background-color{
  background-color:#eee;
}
:root .has-very-dark-gray-background-color{
  background-color:#313131;
}
:root .has-very-light-gray-color{
  color:#eee;
}
:root .has-very-dark-gray-color{
  color:#313131;
}
:root .has-vivid-green-cyan-to-vivid-cyan-blue-gradient-background{
  background:linear-gradient(135deg, #00d084, #0693e3);
}
:root .has-purple-crush-gradient-background{
  background:linear-gradient(135deg, #34e2e4, #4721fb 50%, #ab1dfe);
}
:root .has-hazy-dawn-gradient-background{
  background:linear-gradient(135deg, #faaca8, #dad0ec);
}
:root .has-subdued-olive-gradient-background{
  background:linear-gradient(135deg, #fafae1, #67a671);
}
:root .has-atomic-cream-gradient-background{
  background:linear-gradient(135deg, #fdd79a, #004a59);
}
:root .has-nightshade-gradient-background{
  background:linear-gradient(135deg, #330968, #31cdcf);
}
:root .has-midnight-gradient-background{
  background:linear-gradient(135deg, #020381, #2874fc);
}

.has-regular-font-size{
  font-size:1em;
}

.has-larger-font-size{
  font-size:2.625em;
}

.has-normal-font-size{
  font-size:var(--wp--preset--font-size--normal);
}

.has-huge-font-size{
  font-size:var(--wp--preset--font-size--huge);
}

.has-text-align-center{
  text-align:center;
}

.has-text-align-left{
  text-align:left;
}

.has-text-align-right{
  text-align:right;
}

#end-resizable-editor-section{
  display:none;
}

.aligncenter{
  clear:both;
}

.items-justified-left{
  justify-content:flex-start;
}

.items-justified-center{
  justify-content:center;
}

.items-justified-right{
  justify-content:flex-end;
}

.items-justified-space-between{
  justify-content:space-between;
}

.screen-reader-text{
  border:0;
  clip:rect(1px, 1px, 1px, 1px);
  -webkit-clip-path:inset(50%);
  clip-path:inset(50%);
  height:1px;
  margin:-1px;
  overflow:hidden;
  padding:0;
  position:absolute;
  width:1px;
  word-wrap:normal !important;
}

.screen-reader-text:focus{
  background-color:#ddd;
  clip:auto !important;
  -webkit-clip-path:none;
          clip-path:none;
  color:#444;
  display:block;
  font-size:1em;
  height:auto;
  left:5px;
  line-height:normal;
  padding:15px 23px 14px;
  text-decoration:none;
  top:5px;
  width:auto;
  z-index:100000;
}
html :where(.has-border-color){
  border-style:solid;
}

html :where([style*=border-top-color]){
  border-top-style:solid;
}

html :where([style*=border-right-color]){
  border-right-style:solid;
}

html :where([style*=border-bottom-color]){
  border-bottom-style:solid;
}

html :where([style*=border-left-color]){
  border-left-style:solid;
}

html :where([style*=border-width]){
  border-style:solid;
}

html :where([style*=border-top-width]){
  border-top-style:solid;
}

html :where([style*=border-right-width]){
  border-right-style:solid;
}

html :where([style*=border-bottom-width]){
  border-bottom-style:solid;
}

html :where([style*=border-left-width]){
  border-left-style:solid;
}
html :where(img[class*=wp-image-]){
  height:auto;
  max-width:100%;
}
:where(figure){
  margin:0 0 1em;
}

html :where(.is-position-sticky){
  --wp-admin--admin-bar--position-offset:var(--wp-admin--admin-bar--height, 0px);
}

@media screen and (max-width:600px){
  html :where(.is-position-sticky){
    --wp-admin--admin-bar--position-offset:0px;
  }
}