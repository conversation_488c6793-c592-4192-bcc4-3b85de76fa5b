.wp-block[data-align=center]>.wp-block-button{
  margin-left:auto;
  margin-right:auto;
  text-align:center;
}

.wp-block[data-align=right]>.wp-block-button{
  text-align:right;
}

.wp-block-button{
  cursor:text;
  position:relative;
}
.wp-block-button:focus{
  box-shadow:0 0 0 1px #fff, 0 0 0 3px var(--wp-admin-theme-color);
  outline:2px solid #0000;
  outline-offset:-2px;
}
.wp-block-button[data-rich-text-placeholder]:after{
  opacity:.8;
}

div[data-type="core/button"]{
  display:table;
}

.editor-styles-wrapper .wp-block-button[style*=text-decoration] .wp-block-button__link{
  text-decoration:inherit;
}

.editor-styles-wrapper .wp-block-button .wp-block-button__link:where(.has-border-color){
  border-width:initial;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-top-color]){
  border-top-width:medium;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-right-color]){
  border-left-width:medium;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-bottom-color]){
  border-bottom-width:medium;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-left-color]){
  border-right-width:medium;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-style]){
  border-width:initial;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-top-style]){
  border-top-width:medium;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-right-style]){
  border-left-width:medium;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-bottom-style]){
  border-bottom-width:medium;
}
.editor-styles-wrapper .wp-block-button .wp-block-button__link:where([style*=border-left-style]){
  border-right-width:medium;
}