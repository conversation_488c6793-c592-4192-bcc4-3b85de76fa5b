/*! This file is auto-generated */
window.wp=window.wp||{},function(i){var n,o=wp.customize;i.extend(i.support,{history:!(!window.history||!history.pushState),hashchange:"onhashchange"in window&&(void 0===document.documentMode||7<document.documentMode)}),n=i.extend({},o.Events,{initialize:function(){this.body=i(document.body),n.settings&&i.support.postMessage&&(i.support.cors||!n.settings.isCrossDomain)&&(this.window=i(window),this.element=i('<div id="customize-container" />').appendTo(this.body),this.bind("open",this.overlay.show),this.bind("close",this.overlay.hide),i("#wpbody").on("click",".load-customize",function(e){e.preventDefault(),n.link=i(this),n.open(n.link.attr("href"))}),i.support.history&&this.window.on("popstate",n.popstate),i.support.hashchange)&&(this.window.on("hashchange",n.hashchange),this.window.triggerHandler("hashchange"))},popstate:function(e){e=e.originalEvent.state;e&&e.customize?n.open(e.customize):n.active&&n.close()},hashchange:function(){var e=window.location.toString().split("#")[1];e&&0===e.indexOf("wp_customize=on")&&n.open(n.settings.url+"?"+e),e||i.support.history||n.close()},beforeunload:function(){if(!n.saved())return n.settings.l10n.saveAlert},open:function(e){if(!this.active){if(n.settings.browser.mobile)return window.location=e;this.originalDocumentTitle=document.title,this.active=!0,this.body.addClass("customize-loading"),this.saved=new o.Value(!0),this.iframe=i("<iframe />",{src:e,title:n.settings.l10n.mainIframeTitle}).appendTo(this.element),this.iframe.one("load",this.loaded),this.messenger=new o.Messenger({url:e,channel:"loader",targetWindow:this.iframe[0].contentWindow}),history.replaceState&&this.messenger.bind("changeset-uuid",function(e){var t=document.createElement("a");t.href=location.href,t.search=i.param(_.extend(o.utils.parseQueryString(t.search.substr(1)),{changeset_uuid:e})),history.replaceState({customize:t.href},"",t.href)}),this.messenger.bind("ready",function(){n.messenger.send("back")}),this.messenger.bind("close",function(){i.support.history?history.back():i.support.hashchange?window.location.hash="":n.close()}),i(window).on("beforeunload",this.beforeunload),this.messenger.bind("saved",function(){n.saved(!0)}),this.messenger.bind("change",function(){n.saved(!1)}),this.messenger.bind("title",function(e){window.document.title=e}),this.pushState(e),this.trigger("open")}},pushState:function(e){var t=e.split("?")[1];i.support.history&&window.location.href!==e?history.pushState({customize:e},"",e):!i.support.history&&i.support.hashchange&&t&&(window.location.hash="wp_customize=on&"+t),this.trigger("open")},opened:function(){n.body.addClass("customize-active full-overlay-active").attr("aria-busy","true")},close:function(){var t,i=this;i.active&&(i.messenger.bind("confirmed-close",t=function(e){e?(i.active=!1,i.trigger("close"),i.originalDocumentTitle&&(document.title=i.originalDocumentTitle)):history.forward(),i.messenger.unbind("confirmed-close",t)}),n.messenger.send("confirm-close"))},closed:function(){n.iframe.remove(),n.messenger.destroy(),n.iframe=null,n.messenger=null,n.saved=null,n.body.removeClass("customize-active full-overlay-active").removeClass("customize-loading"),i(window).off("beforeunload",n.beforeunload),n.link&&n.link.focus()},loaded:function(){n.body.removeClass("customize-loading").attr("aria-busy","false")},overlay:{show:function(){this.element.fadeIn(200,n.opened)},hide:function(){this.element.fadeOut(200,n.closed)}}}),i(function(){n.settings=_wpCustomizeLoaderSettings,n.initialize()}),o.Loader=n}((wp,jQuery));