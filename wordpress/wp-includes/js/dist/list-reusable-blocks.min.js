/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t);const n=window.React,o=window.wp.element,r=window.wp.i18n;var a=function(){return a=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},a.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function s(e){return e.toLowerCase()}var i=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],l=/[^A-Z0-9]+/gi;function c(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}function p(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,o=void 0===n?i:n,r=t.stripRegexp,a=void 0===r?l:r,p=t.transform,d=void 0===p?s:p,u=t.delimiter,w=void 0===u?" ":u,m=c(c(e,o,"$1\0$2"),a,"\0"),f=0,b=m.length;"\0"===m.charAt(f);)f++;for(;"\0"===m.charAt(b-1);)b--;return m.slice(f,b).split("\0").map(d).join(w)}(e,a({delimiter:"."},t))}const d=window.wp.apiFetch;var u=e.n(d);const w=window.wp.blob;const m=async function(e){const t=await u()({path:"/wp/v2/types/wp_block"}),n=await u()({path:`/wp/v2/${t.rest_base}/${e}?context=edit`}),o=n.title.raw,r=n.content.raw,s=n.wp_pattern_sync_status,i=JSON.stringify({__file:"wp_block",title:o,content:r,syncStatus:s},null,2),l=(void 0===c&&(c={}),p(o,a({delimiter:"-"},c))+".json");var c;(0,w.downloadBlob)(l,i,"application/json")},f=window.wp.compose,b=window.wp.components;const _=async function(e){const t=await function(e){const t=new window.FileReader;return new Promise((n=>{t.onload=()=>{n(t.result)},t.readAsText(e)}))}(e);let n;try{n=JSON.parse(t)}catch(e){throw new Error("Invalid JSON file")}if("wp_block"!==n.__file||!n.title||!n.content||"string"!=typeof n.title||"string"!=typeof n.content||n.syncStatus&&"string"!=typeof n.syncStatus)throw new Error("Invalid pattern JSON file");const o=await u()({path:"/wp/v2/types/wp_block"});return await u()({path:`/wp/v2/${o.rest_base}`,data:{title:n.title,content:n.content,status:"publish",meta:"unsynced"===n.syncStatus?{wp_pattern_sync_status:n.syncStatus}:void 0},method:"POST"})};const v=(0,f.withInstanceId)((function({instanceId:e,onUpload:t}){const a="list-reusable-blocks-import-form-"+e,s=(0,o.useRef)(),[i,l]=(0,o.useState)(!1),[c,p]=(0,o.useState)(null),[d,u]=(0,o.useState)(null);return(0,n.createElement)("form",{className:"list-reusable-blocks-import-form",onSubmit:e=>{e.preventDefault(),d&&(l({isLoading:!0}),_(d).then((e=>{s&&(l(!1),t(e))})).catch((e=>{if(!s)return;let t;switch(e.message){case"Invalid JSON file":t=(0,r.__)("Invalid JSON file");break;case"Invalid pattern JSON file":t=(0,r.__)("Invalid pattern JSON file");break;default:t=(0,r.__)("Unknown error")}l(!1),p(t)})))},ref:s},c&&(0,n.createElement)(b.Notice,{status:"error",onRemove:()=>{p(null)}},c),(0,n.createElement)("label",{htmlFor:a,className:"list-reusable-blocks-import-form__label"},(0,r.__)("File")),(0,n.createElement)("input",{id:a,type:"file",onChange:e=>{u(e.target.files[0]),p(null)}}),(0,n.createElement)(b.Button,{type:"submit",isBusy:i,disabled:!d||i,variant:"secondary",className:"list-reusable-blocks-import-form__button"},(0,r._x)("Import","button label")))}));const y=function({onUpload:e}){return(0,n.createElement)(b.Dropdown,{popoverProps:{placement:"bottom-start"},contentClassName:"list-reusable-blocks-import-dropdown__content",renderToggle:({isOpen:e,onToggle:t})=>(0,n.createElement)(b.Button,{"aria-expanded":e,onClick:t,variant:"primary"},(0,r.__)("Import from JSON")),renderContent:({onClose:t})=>(0,n.createElement)(v,{onUpload:(0,f.pipe)(t,e)})})};document.body.addEventListener("click",(e=>{e.target.classList.contains("wp-list-reusable-blocks__export")&&(e.preventDefault(),m(e.target.dataset.id))})),document.addEventListener("DOMContentLoaded",(()=>{const e=document.querySelector(".page-title-action");if(!e)return;const t=document.createElement("div");t.className="list-reusable-blocks__container",e.parentNode.insertBefore(t,e),(0,o.createRoot)(t).render((0,n.createElement)(y,{onUpload:()=>{const e=document.createElement("div");e.className="notice notice-success is-dismissible",e.innerHTML=`<p>${(0,r.__)("Pattern imported successfully!")}</p>`;const t=document.querySelector(".wp-header-end");t&&t.parentNode.insertBefore(e,t)}}))})),(window.wp=window.wp||{}).listReusableBlocks=t})();