/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ReusableBlocksMenuItems:()=>T,store:()=>_});var n={};e.r(n),e.d(n,{__experimentalConvertBlockToStatic:()=>a,__experimentalConvertBlocksToReusable:()=>i,__experimentalDeleteReusableBlock:()=>u,__experimentalSetEditingReusableBlock:()=>p});var o={};e.r(o),e.d(o,{__experimentalIsEditingReusableBlock:()=>m});const r=window.wp.data,l=window.wp.blockEditor,c=window.wp.blocks,s=window.wp.i18n,a=e=>({registry:t})=>{const n=t.select(l.store).getBlock(e),o=t.select("core").getEditedEntityRecord("postType","wp_block",n.attributes.ref),r=(0,c.parse)("function"==typeof o.content?o.content(o):o.content);t.dispatch(l.store).replaceBlocks(n.clientId,r)},i=(e,t,n)=>async({registry:o,dispatch:r})=>{const a="unsynced"===n?{wp_pattern_sync_status:n}:void 0,i={title:t||(0,s.__)("Untitled pattern block"),content:(0,c.serialize)(o.select(l.store).getBlocksByClientId(e)),status:"publish",meta:a},u=await o.dispatch("core").saveEntityRecord("postType","wp_block",i);if("unsynced"===n)return;const p=(0,c.createBlock)("core/block",{ref:u.id});o.dispatch(l.store).replaceBlocks(e,p),r.__experimentalSetEditingReusableBlock(p.clientId,!0)},u=e=>async({registry:t})=>{if(!t.select("core").getEditedEntityRecord("postType","wp_block",e))return;const n=t.select(l.store).getBlocks().filter((t=>(0,c.isReusableBlock)(t)&&t.attributes.ref===e)).map((e=>e.clientId));n.length&&t.dispatch(l.store).removeBlocks(n),await t.dispatch("core").deleteEntityRecord("postType","wp_block",e)};function p(e,t){return{type:"SET_EDITING_REUSABLE_BLOCK",clientId:e,isEditing:t}}const d=(0,r.combineReducers)({isEditingReusableBlock:function(e={},t){return"SET_EDITING_REUSABLE_BLOCK"===t?.type?{...e,[t.clientId]:t.isEditing}:e}});function m(e,t){return e.isEditingReusableBlock[t]}const _=(0,r.createReduxStore)("core/reusable-blocks",{actions:n,reducer:d,selectors:o});(0,r.register)(_);const b=window.React,k=window.wp.element,w=window.wp.components,y=window.wp.primitives,g=(0,b.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,b.createElement)(y.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.1.1.1.3 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})),B=window.wp.notices,E=window.wp.coreData,v=window.wp.privateApis,{unlock:h}=(0,v.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I know using unstable features means my theme or plugin will inevitably break in the next version of WordPress.","@wordpress/reusable-blocks"),{useReusableBlocksRenameHint:C,ReusableBlocksRenameHint:f}=h(l.privateApis);function R({clientIds:e,rootClientId:t,onClose:n}){const o=C(),[a,i]=(0,k.useState)(void 0),[u,p]=(0,k.useState)(!1),[d,m]=(0,k.useState)(""),y=(0,r.useSelect)((n=>{var o;const{canUser:r}=n(E.store),{getBlocksByClientId:s,canInsertBlockType:a,getBlockRootClientId:i}=n(l.store),u=t||(e.length>0?i(e[0]):void 0),p=null!==(o=s(e))&&void 0!==o?o:[];return!(1===p.length&&p[0]&&(0,c.isReusableBlock)(p[0])&&!!n(E.store).getEntityRecord("postType","wp_block",p[0].attributes.ref))&&a("core/block",u)&&p.every((e=>!!e&&e.isValid&&(0,c.hasBlockSupport)(e.name,"reusable",!0)))&&!!r("create","blocks")}),[e,t]),{__experimentalConvertBlocksToReusable:v}=(0,r.useDispatch)(_),{createSuccessNotice:h,createErrorNotice:R}=(0,r.useDispatch)(B.store),I=(0,k.useCallback)((async function(t){try{await v(e,t,a),h(a?(0,s.sprintf)((0,s.__)("Unsynced pattern created: %s"),t):(0,s.sprintf)((0,s.__)("Synced pattern created: %s"),t),{type:"snackbar",id:"convert-to-reusable-block-success"})}catch(e){R(e.message,{type:"snackbar",id:"convert-to-reusable-block-error"})}}),[v,e,a,h,R]);return y?(0,b.createElement)(b.Fragment,null,(0,b.createElement)(w.MenuItem,{icon:g,onClick:()=>p(!0)},o?(0,s.__)("Create pattern/reusable block"):(0,s.__)("Create pattern")),u&&(0,b.createElement)(w.Modal,{title:(0,s.__)("Create pattern"),onRequestClose:()=>{p(!1),m("")},overlayClassName:"reusable-blocks-menu-items__convert-modal"},(0,b.createElement)("form",{onSubmit:e=>{e.preventDefault(),I(d),p(!1),m(""),n()}},(0,b.createElement)(w.__experimentalVStack,{spacing:"5"},(0,b.createElement)(f,null),(0,b.createElement)(w.TextControl,{__nextHasNoMarginBottom:!0,label:(0,s.__)("Name"),value:d,onChange:m,placeholder:(0,s.__)("My pattern")}),(0,b.createElement)(w.ToggleControl,{label:(0,s._x)("Synced","Option that makes an individual pattern synchronized"),help:(0,s.__)("Sync this pattern across multiple locations."),checked:!a,onChange:()=>{i(a?void 0:"unsynced")}}),(0,b.createElement)(w.__experimentalHStack,{justify:"right"},(0,b.createElement)(w.Button,{variant:"tertiary",onClick:()=>{p(!1),m("")}},(0,s.__)("Cancel")),(0,b.createElement)(w.Button,{variant:"primary",type:"submit"},(0,s.__)("Create"))))))):null}const I=window.wp.url;const S=function({clientId:e}){const{canRemove:t,isVisible:n,managePatternsUrl:o}=(0,r.useSelect)((t=>{const{getBlock:n,canRemoveBlock:o,getBlockCount:r,getSettings:s}=t(l.store),{canUser:a}=t(E.store),i=n(e),u=s().__unstableIsBlockBasedTheme;return{canRemove:o(e),isVisible:!!i&&(0,c.isReusableBlock)(i)&&!!a("update","blocks",i.attributes.ref),innerBlockCount:r(e),managePatternsUrl:u&&a("read","templates")?(0,I.addQueryArgs)("site-editor.php",{path:"/patterns"}):(0,I.addQueryArgs)("edit.php",{post_type:"wp_block"})}}),[e]),{__experimentalConvertBlockToStatic:a}=(0,r.useDispatch)(_);return n?(0,b.createElement)(b.Fragment,null,(0,b.createElement)(w.MenuItem,{href:o},(0,s.__)("Manage patterns")),t&&(0,b.createElement)(w.MenuItem,{onClick:()=>a(e)},(0,s.__)("Detach"))):null};function T({rootClientId:e}){return(0,b.createElement)(l.BlockSettingsMenuControls,null,(({onClose:t,selectedClientIds:n})=>(0,b.createElement)(b.Fragment,null,(0,b.createElement)(R,{clientIds:n,rootClientId:e,onClose:t}),1===n.length&&(0,b.createElement)(S,{clientId:n[0]}))))}(window.wp=window.wp||{}).reusableBlocks=t})();