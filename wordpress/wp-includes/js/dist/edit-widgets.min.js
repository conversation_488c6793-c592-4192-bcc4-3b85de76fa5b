/*! This file is auto-generated */
(()=>{var e={5755:(e,t)=>{var r;
/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var a={}.hasOwnProperty;function n(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)){if(r.length){var o=n.apply(null,r);o&&e.push(o)}}else if("object"===i){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var s in r)a.call(r,s)&&r[s]&&e.push(s)}}}return e.join(" ")}e.exports?(n.default=n,e.exports=n):void 0===(r=function(){return n}.apply(t,[]))||(e.exports=r)}()}},t={};function r(a){var n=t[a];if(void 0!==n)return n.exports;var i=t[a]={exports:{}};return e[a](i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};(()=>{"use strict";r.r(a),r.d(a,{initialize:()=>Sr,initializeEditor:()=>kr,reinitializeEditor:()=>Ar,store:()=>nt});var e={};r.r(e),r.d(e,{closeModal:()=>H,disableComplementaryArea:()=>R,enableComplementaryArea:()=>P,openModal:()=>G,pinItem:()=>O,setDefaultComplementaryArea:()=>L,setFeatureDefaults:()=>F,setFeatureValue:()=>D,toggleFeature:()=>V,unpinItem:()=>M});var t={};r.r(t),r.d(t,{getActiveComplementaryArea:()=>z,isComplementaryAreaLoading:()=>U,isFeatureActive:()=>j,isItemPinned:()=>$,isModalActive:()=>Y});var n={};r.r(n),r.d(n,{closeGeneralSidebar:()=>Re,moveBlockToWidgetArea:()=>Oe,persistStubPost:()=>Ae,saveEditedWidgetAreas:()=>Ie,saveWidgetArea:()=>Be,saveWidgetAreas:()=>Ce,setIsInserterOpened:()=>Le,setIsListViewOpened:()=>Pe,setIsWidgetAreaOpen:()=>We,setWidgetAreasOpenState:()=>Te,setWidgetIdForClientId:()=>xe});var i={};r.r(i),r.d(i,{getWidgetAreas:()=>Me,getWidgets:()=>Ve});var o={};r.r(o),r.d(o,{__experimentalGetInsertionPoint:()=>qe,canInsertBlockInWidgetArea:()=>Qe,getEditedWidgetAreas:()=>$e,getIsWidgetAreaOpen:()=>Ke,getParentWidgetAreaBlock:()=>Ue,getReferenceWidgetBlocks:()=>je,getWidget:()=>Ge,getWidgetAreaForWidgetId:()=>ze,getWidgetAreas:()=>He,getWidgets:()=>Fe,isInserterOpened:()=>Ze,isListViewOpened:()=>Je,isSavingWidgetAreas:()=>Ye});var s={};r.r(s),r.d(s,{getListViewToggleRef:()=>Xe});var c={};r.r(c),r.d(c,{metadata:()=>mt,name:()=>ut,settings:()=>gt});const l=window.React,d=window.wp.blocks,m=window.wp.data,u=window.wp.deprecated;var g=r.n(u);const p=window.wp.element,h=window.wp.blockLibrary,w=window.wp.coreData,_=window.wp.widgets,b=window.wp.preferences,E=window.wp.apiFetch;var f=r.n(E);const y=(0,m.combineReducers)({blockInserterPanel:function(e=!1,t){switch(t.type){case"SET_IS_LIST_VIEW_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return t.value}return e},listViewPanel:function(e=!1,t){switch(t.type){case"SET_IS_INSERTER_OPENED":return!t.value&&e;case"SET_IS_LIST_VIEW_OPENED":return t.isOpen}return e},widgetAreasOpenState:function(e={},t){const{type:r}=t;switch(r){case"SET_WIDGET_AREAS_OPEN_STATE":return t.widgetAreasOpenState;case"SET_IS_WIDGET_AREA_OPEN":{const{clientId:r,isOpen:a}=t;return{...e,[r]:a}}default:return e}}}),v=window.wp.i18n,k=window.wp.notices;var S=r(5755),A=r.n(S);const I=window.wp.components,C=window.wp.primitives,B=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})),N=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})),x=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})),T=window.wp.viewport,W=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),L=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e,area:t}),P=(e,t)=>({registry:r,dispatch:a})=>{if(!t)return;r.select(b.store).get(e,"isComplementaryAreaVisible")||r.dispatch(b.store).set(e,"isComplementaryAreaVisible",!0),a({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t})},R=e=>({registry:t})=>{t.select(b.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(b.store).set(e,"isComplementaryAreaVisible",!1)},O=(e,t)=>({registry:r})=>{if(!t)return;const a=r.select(b.store).get(e,"pinnedItems");!0!==a?.[t]&&r.dispatch(b.store).set(e,"pinnedItems",{...a,[t]:!0})},M=(e,t)=>({registry:r})=>{if(!t)return;const a=r.select(b.store).get(e,"pinnedItems");r.dispatch(b.store).set(e,"pinnedItems",{...a,[t]:!1})};function V(e,t){return function({registry:r}){g()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),r.dispatch(b.store).toggle(e,t)}}function D(e,t,r){return function({registry:a}){g()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),a.dispatch(b.store).set(e,t,!!r)}}function F(e,t){return function({registry:r}){g()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),r.dispatch(b.store).setDefaults(e,t)}}function G(e){return{type:"OPEN_MODAL",name:e}}function H(){return{type:"CLOSE_MODAL"}}const z=(0,m.createRegistrySelector)((e=>(t,r)=>{const a=e(b.store).get(r,"isComplementaryAreaVisible");if(void 0!==a)return!1===a?null:t?.complementaryAreas?.[r]})),U=(0,m.createRegistrySelector)((e=>(t,r)=>{const a=e(b.store).get(r,"isComplementaryAreaVisible"),n=t?.complementaryAreas?.[r];return a&&void 0===n})),$=(0,m.createRegistrySelector)((e=>(t,r,a)=>{var n;const i=e(b.store).get(r,"pinnedItems");return null===(n=i?.[a])||void 0===n||n})),j=(0,m.createRegistrySelector)((e=>(t,r,a)=>(g()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(b.store).get(r,a))));function Y(e,t){return e.activeModal===t}const K=(0,m.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:r,area:a}=t;return e[r]?e:{...e,[r]:a}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:r,area:a}=t;return{...e,[r]:a}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}}),Z=(0,m.createReduxStore)("core/interface",{reducer:K,actions:e,selectors:t});(0,m.register)(Z);const q=window.wp.plugins,Q=(0,q.withPluginContext)(((e,t)=>({icon:t.icon||e.icon,identifier:t.identifier||`${e.name}/${t.name}`})));const J=Q((function({as:e=I.Button,scope:t,identifier:r,icon:a,selectedIcon:n,name:i,...o}){const s=e,c=(0,m.useSelect)((e=>e(Z).getActiveComplementaryArea(t)===r),[r,t]),{enableComplementaryArea:d,disableComplementaryArea:u}=(0,m.useDispatch)(Z);return(0,l.createElement)(s,{icon:n&&c?n:a,"aria-controls":r.replace("/",":"),onClick:()=>{c?u(t):d(t,r)},...o})})),X=({smallScreenTitle:e,children:t,className:r,toggleButtonProps:a})=>{const n=(0,l.createElement)(J,{icon:W,...a});return(0,l.createElement)(l.Fragment,null,(0,l.createElement)("div",{className:"components-panel__header interface-complementary-area-header__small"},e&&(0,l.createElement)("span",{className:"interface-complementary-area-header__small-title"},e),n),(0,l.createElement)("div",{className:A()("components-panel__header","interface-complementary-area-header",r),tabIndex:-1},t,n))},ee=()=>{};function te({name:e,as:t=I.Button,onClick:r,...a}){return(0,l.createElement)(I.Fill,{name:e},(({onClick:e})=>(0,l.createElement)(t,{onClick:r||e?(...t)=>{(r||ee)(...t),(e||ee)(...t)}:void 0,...a})))}te.Slot=function({name:e,as:t=I.ButtonGroup,fillProps:r={},bubblesVirtually:a,...n}){return(0,l.createElement)(I.Slot,{name:e,bubblesVirtually:a,fillProps:r},(e=>{if(!p.Children.toArray(e).length)return null;const r=[];p.Children.forEach(e,(({props:{__unstableExplicitMenuItem:e,__unstableTarget:t}})=>{t&&e&&r.push(t)}));const a=p.Children.map(e,(e=>!e.props.__unstableExplicitMenuItem&&r.includes(e.props.__unstableTarget)?null:e));return(0,l.createElement)(t,{...n},a)}))};const re=te,ae=({__unstableExplicitMenuItem:e,__unstableTarget:t,...r})=>(0,l.createElement)(I.MenuItem,{...r});function ne({scope:e,target:t,__unstableExplicitMenuItem:r,...a}){return(0,l.createElement)(J,{as:a=>(0,l.createElement)(re,{__unstableExplicitMenuItem:r,__unstableTarget:`${e}/${t}`,as:ae,name:`${e}/plugin-more-menu`,...a}),role:"menuitemcheckbox",selectedIcon:B,name:t,scope:e,...a})}function ie({scope:e,...t}){return(0,l.createElement)(I.Fill,{name:`PinnedItems/${e}`,...t})}ie.Slot=function({scope:e,className:t,...r}){return(0,l.createElement)(I.Slot,{name:`PinnedItems/${e}`,...r},(e=>e?.length>0&&(0,l.createElement)("div",{className:A()(t,"interface-pinned-items")},e)))};const oe=ie;function se({scope:e,children:t,className:r,id:a}){return(0,l.createElement)(I.Fill,{name:`ComplementaryArea/${e}`},(0,l.createElement)("div",{id:a,className:r},t))}const ce=Q((function({children:e,className:t,closeLabel:r=(0,v.__)("Close plugin"),identifier:a,header:n,headerClassName:i,icon:o,isPinnable:s=!0,panelClassName:c,scope:d,name:u,smallScreenTitle:g,title:h,toggleShortcut:w,isActiveByDefault:_}){const{isLoading:E,isActive:f,isPinned:y,activeArea:k,isSmall:S,isLarge:C,showIconLabels:W}=(0,m.useSelect)((e=>{const{getActiveComplementaryArea:t,isComplementaryAreaLoading:r,isItemPinned:n}=e(Z),{get:i}=e(b.store),o=t(d);return{isLoading:r(d),isActive:o===a,isPinned:n(d,a),activeArea:o,isSmall:e(T.store).isViewportMatch("< medium"),isLarge:e(T.store).isViewportMatch("large"),showIconLabels:i("core","showIconLabels")}}),[a,d]);!function(e,t,r,a,n){const i=(0,p.useRef)(!1),o=(0,p.useRef)(!1),{enableComplementaryArea:s,disableComplementaryArea:c}=(0,m.useDispatch)(Z);(0,p.useEffect)((()=>{a&&n&&!i.current?(c(e),o.current=!0):o.current&&!n&&i.current?(o.current=!1,s(e,t)):o.current&&r&&r!==t&&(o.current=!1),n!==i.current&&(i.current=n)}),[a,n,e,t,r,c,s])}(d,a,k,f,S);const{enableComplementaryArea:L,disableComplementaryArea:P,pinItem:R,unpinItem:O}=(0,m.useDispatch)(Z);return(0,p.useEffect)((()=>{_&&void 0===k&&!S?L(d,a):void 0===k&&S&&P(d,a)}),[k,_,d,a,S,L,P]),(0,l.createElement)(l.Fragment,null,s&&(0,l.createElement)(oe,{scope:d},y&&(0,l.createElement)(J,{scope:d,identifier:a,isPressed:f&&(!W||C),"aria-expanded":f,"aria-disabled":E,label:h,icon:W?B:o,showTooltip:!W,variant:W?"tertiary":void 0,size:"compact"})),u&&s&&(0,l.createElement)(ne,{target:u,scope:d,icon:o},h),f&&(0,l.createElement)(se,{className:A()("interface-complementary-area",t),scope:d,id:a.replace("/",":")},(0,l.createElement)(X,{className:i,closeLabel:r,onClose:()=>P(d),smallScreenTitle:g,toggleButtonProps:{label:r,shortcut:w,scope:d,identifier:a}},n||(0,l.createElement)(l.Fragment,null,(0,l.createElement)("strong",null,h),s&&(0,l.createElement)(I.Button,{className:"interface-complementary-area__pin-unpin-item",icon:y?N:x,label:y?(0,v.__)("Unpin from toolbar"):(0,v.__)("Pin to toolbar"),onClick:()=>(y?O:R)(d,a),isPressed:y,"aria-expanded":y}))),(0,l.createElement)(I.Panel,{className:c},e)))}));ce.Slot=function({scope:e,...t}){return(0,l.createElement)(I.Slot,{name:`ComplementaryArea/${e}`,...t})};const le=ce,de=window.wp.compose;function me({children:e,className:t,ariaLabel:r,as:a="div",...n}){return(0,l.createElement)(a,{className:A()("interface-navigable-region",t),"aria-label":r,role:"region",tabIndex:"-1",...n},e)}const ue={hidden:{opacity:0},hover:{opacity:1,transition:{type:"tween",delay:.2,delayChildren:.2}},distractionFreeInactive:{opacity:1,transition:{delay:0}}};const ge=(0,p.forwardRef)((function({isDistractionFree:e,footer:t,header:r,editorNotices:a,sidebar:n,secondarySidebar:i,notices:o,content:s,actions:c,labels:d,className:m,enableRegionNavigation:u=!0,shortcuts:g},h){const w=(0,I.__unstableUseNavigateRegions)(g);!function(e){(0,p.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const _={...{header:(0,v._x)("Header","header landmark area"),body:(0,v.__)("Content"),secondarySidebar:(0,v.__)("Block Library"),sidebar:(0,v.__)("Settings"),actions:(0,v.__)("Publish"),footer:(0,v.__)("Footer")},...d};return(0,l.createElement)("div",{...u?w:{},ref:(0,de.useMergeRefs)([h,u?w.ref:void 0]),className:A()(m,"interface-interface-skeleton",w.className,!!t&&"has-footer")},(0,l.createElement)("div",{className:"interface-interface-skeleton__editor"},!!r&&(0,l.createElement)(me,{as:I.__unstableMotion.div,className:"interface-interface-skeleton__header","aria-label":_.header,initial:e?"hidden":"distractionFreeInactive",whileHover:e?"hover":"distractionFreeInactive",animate:e?"hidden":"distractionFreeInactive",variants:ue,transition:e?{type:"tween",delay:.8}:void 0},r),e&&(0,l.createElement)("div",{className:"interface-interface-skeleton__header"},a),(0,l.createElement)("div",{className:"interface-interface-skeleton__body"},!!i&&(0,l.createElement)(me,{className:"interface-interface-skeleton__secondary-sidebar",ariaLabel:_.secondarySidebar},i),!!o&&(0,l.createElement)("div",{className:"interface-interface-skeleton__notices"},o),(0,l.createElement)(me,{className:"interface-interface-skeleton__content",ariaLabel:_.body},s),!!n&&(0,l.createElement)(me,{className:"interface-interface-skeleton__sidebar",ariaLabel:_.sidebar},n),!!c&&(0,l.createElement)(me,{className:"interface-interface-skeleton__actions",ariaLabel:_.actions},c))),!!t&&(0,l.createElement)(me,{className:"interface-interface-skeleton__footer",ariaLabel:_.footer},t))})),pe=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}));function he({as:e=I.DropdownMenu,className:t,label:r=(0,v.__)("Options"),popoverProps:a,toggleProps:n,children:i}){return(0,l.createElement)(e,{className:A()("interface-more-menu-dropdown",t),icon:pe,label:r,popoverProps:{placement:"bottom-end",...a,className:A()("interface-more-menu-dropdown__content",a?.className)},toggleProps:{tooltipPosition:"bottom",...n,size:"compact"}},(e=>i(e)))}const we=window.wp.blockEditor;function _e(e){if("block"===e.id_base){const t=(0,d.parse)(e.instance.raw.content,{__unstableSkipAutop:!0});return t.length?(0,_.addWidgetIdToBlock)(t[0],e.id):(0,_.addWidgetIdToBlock)((0,d.createBlock)("core/paragraph",{},[]),e.id)}let t;return t=e._embedded.about[0].is_multi?{idBase:e.id_base,instance:e.instance}:{id:e.id},(0,_.addWidgetIdToBlock)((0,d.createBlock)("core/legacy-widget",t,[]),e.id)}function be(e,t={}){let r;var a,n,i;"core/legacy-widget"===e.name&&(e.attributes.id||e.attributes.instance)?r={...t,id:null!==(a=e.attributes.id)&&void 0!==a?a:t.id,id_base:null!==(n=e.attributes.idBase)&&void 0!==n?n:t.id_base,instance:null!==(i=e.attributes.instance)&&void 0!==i?i:t.instance}:r={...t,id_base:"block",instance:{raw:{content:(0,d.serialize)(e)}}};return delete r.rendered,delete r.rendered_form,r}const Ee="root",fe="sidebar",ye="postType",ve=e=>`widget-area-${e}`,ke=()=>"widget-areas";const Se="core/edit-widgets",Ae=(e,t)=>({registry:r})=>{const a=((e,t)=>({id:e,slug:e,status:"draft",type:"page",blocks:t,meta:{widgetAreaId:e}}))(e,t);return r.dispatch(w.store).receiveEntityRecords(Ee,ye,a,{id:a.id},!1),a},Ie=()=>async({select:e,dispatch:t,registry:r})=>{const a=e.getEditedWidgetAreas();if(a?.length)try{await t.saveWidgetAreas(a),r.dispatch(k.store).createSuccessNotice((0,v.__)("Widgets saved."),{type:"snackbar"})}catch(e){r.dispatch(k.store).createErrorNotice((0,v.sprintf)((0,v.__)("There was an error. %s"),e.message),{type:"snackbar"})}},Ce=e=>async({dispatch:t,registry:r})=>{try{for(const r of e)await t.saveWidgetArea(r.id)}finally{await r.dispatch(w.store).finishResolution("getEntityRecord",Ee,fe,{per_page:-1})}},Be=e=>async({dispatch:t,select:r,registry:a})=>{const n=r.getWidgets(),i=a.select(w.store).getEditedEntityRecord(Ee,ye,ve(e)),o=Object.values(n).filter((({sidebar:t})=>t===e)),s=[],c=i.blocks.filter((e=>{const{id:t}=e.attributes;if("core/legacy-widget"===e.name&&t){if(s.includes(t))return!1;s.push(t)}return!0})),l=[];for(const e of o){r.getWidgetAreaForWidgetId(e.id)||l.push(e)}const d=[],m=[],u=[];for(let t=0;t<c.length;t++){const r=c[t],i=(0,_.getWidgetIdFromBlock)(r),o=n[i],s=be(r,o);if(u.push(i),o){a.dispatch(w.store).editEntityRecord("root","widget",i,{...s,sidebar:e},{undoIgnore:!0});if(!a.select(w.store).hasEditsForEntityRecord("root","widget",i))continue;m.push((({saveEditedEntityRecord:e})=>e("root","widget",i)))}else m.push((({saveEntityRecord:t})=>t("root","widget",{...s,sidebar:e})));d.push({block:r,position:t,clientId:r.clientId})}for(const e of l)m.push((({deleteEntityRecord:t})=>t("root","widget",e.id,{force:!0})));const g=(await a.dispatch(w.store).__experimentalBatch(m)).filter((e=>!e.hasOwnProperty("deleted"))),p=[];for(let e=0;e<g.length;e++){const t=g[e],{block:r,position:n}=d[e];i.blocks[n].attributes.__internalWidgetId=t.id;a.select(w.store).getLastEntitySaveError("root","widget",t.id)&&p.push(r.attributes?.name||r?.name),u[n]||(u[n]=t.id)}if(p.length)throw new Error((0,v.sprintf)((0,v.__)("Could not save the following widgets: %s."),p.join(", ")));a.dispatch(w.store).editEntityRecord(Ee,fe,e,{widgets:u},{undoIgnore:!0}),t(Ne(e)),a.dispatch(w.store).receiveEntityRecords(Ee,ye,i,void 0)},Ne=e=>({registry:t})=>{t.dispatch(w.store).saveEditedEntityRecord(Ee,fe,e,{throwOnError:!0})};function xe(e,t){return{type:"SET_WIDGET_ID_FOR_CLIENT_ID",clientId:e,widgetId:t}}function Te(e){return{type:"SET_WIDGET_AREAS_OPEN_STATE",widgetAreasOpenState:e}}function We(e,t){return{type:"SET_IS_WIDGET_AREA_OPEN",clientId:e,isOpen:t}}function Le(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}function Pe(e){return{type:"SET_IS_LIST_VIEW_OPENED",isOpen:e}}const Re=()=>({registry:e})=>{e.dispatch(Z).disableComplementaryArea(Se)},Oe=(e,t)=>async({dispatch:r,select:a,registry:n})=>{const i=n.select(we.store).getBlockRootClientId(e),o=n.select(we.store).getBlocks().find((({attributes:e})=>e.id===t)).clientId,s=n.select(we.store).getBlockOrder(o).length;a.getIsWidgetAreaOpen(o)||r.setIsWidgetAreaOpen(o,!0),n.dispatch(we.store).moveBlocksToPosition([e],i,o,s)},Me=()=>async({dispatch:e,registry:t})=>{const r={per_page:-1},a=[],n=(await t.resolveSelect(w.store).getEntityRecords(Ee,fe,r)).sort(((e,t)=>"wp_inactive_widgets"===e.id?1:"wp_inactive_widgets"===t.id?-1:0));for(const t of n)a.push((0,d.createBlock)("core/widget-area",{id:t.id,name:t.name})),t.widgets.length||e(Ae(ve(t.id),[]));const i={};a.forEach(((e,t)=>{i[e.clientId]=0===t})),e(Te(i)),e(Ae(ke(),a))},Ve=()=>async({dispatch:e,registry:t})=>{const r={per_page:-1,_embed:"about"},a=await t.resolveSelect(w.store).getEntityRecords("root","widget",r),n={};for(const e of a){const t=_e(e);n[e.sidebar]=n[e.sidebar]||[],n[e.sidebar].push(t)}for(const t in n)n.hasOwnProperty(t)&&e(Ae(ve(t),n[t]))},De={rootClientId:void 0,insertionIndex:void 0},Fe=(0,m.createRegistrySelector)((e=>()=>{const t=e(w.store).getEntityRecords("root","widget",{per_page:-1,_embed:"about"});return t?.reduce(((e,t)=>({...e,[t.id]:t})),{})||{}})),Ge=(0,m.createRegistrySelector)((e=>(t,r)=>e(Se).getWidgets()[r])),He=(0,m.createRegistrySelector)((e=>()=>{const t={per_page:-1};return e(w.store).getEntityRecords(Ee,fe,t)})),ze=(0,m.createRegistrySelector)((e=>(t,r)=>e(Se).getWidgetAreas().find((t=>e(w.store).getEditedEntityRecord(Ee,ye,ve(t.id)).blocks.map((e=>(0,_.getWidgetIdFromBlock)(e))).includes(r))))),Ue=(0,m.createRegistrySelector)((e=>(t,r)=>{const{getBlock:a,getBlockName:n,getBlockParents:i}=e(we.store);return a(i(r).find((e=>"core/widget-area"===n(e))))})),$e=(0,m.createRegistrySelector)((e=>(t,r)=>{let a=e(Se).getWidgetAreas();return a?(r&&(a=a.filter((({id:e})=>r.includes(e)))),a.filter((({id:t})=>e(w.store).hasEditsForEntityRecord(Ee,ye,ve(t)))).map((({id:t})=>e(w.store).getEditedEntityRecord(Ee,fe,t)))):[]})),je=(0,m.createRegistrySelector)((e=>(t,r=null)=>{const a=[],n=e(Se).getWidgetAreas();for(const t of n){const n=e(w.store).getEditedEntityRecord(Ee,ye,ve(t.id));for(const e of n.blocks)"core/legacy-widget"!==e.name||r&&e.attributes?.referenceWidgetName!==r||a.push(e)}return a})),Ye=(0,m.createRegistrySelector)((e=>()=>{const t=e(Se).getWidgetAreas()?.map((({id:e})=>e));if(!t)return!1;for(const r of t){if(e(w.store).isSavingEntityRecord(Ee,fe,r))return!0}const r=[...Object.keys(e(Se).getWidgets()),void 0];for(const t of r){if(e(w.store).isSavingEntityRecord("root","widget",t))return!0}return!1})),Ke=(e,t)=>{const{widgetAreasOpenState:r}=e;return!!r[t]};function Ze(e){return!!e.blockInserterPanel}function qe(e){return"boolean"==typeof e.blockInserterPanel?De:e.blockInserterPanel}const Qe=(0,m.createRegistrySelector)((e=>(t,r)=>{const a=e(we.store).getBlocks(),[n]=a;return e(we.store).canInsertBlockType(r,n.clientId)}));function Je(e){return e.listViewPanel}function Xe(e){return e.listViewToggleRef}const et=window.wp.privateApis,{lock:tt,unlock:rt}=(0,et.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I know using unstable features means my theme or plugin will inevitably break in the next version of WordPress.","@wordpress/edit-widgets"),at={reducer:y,selectors:o,resolvers:i,actions:n},nt=(0,m.createReduxStore)(Se,at);(0,m.register)(nt),f().use((function(e,t){return 0===e.path?.indexOf("/wp/v2/types/widget-area")?Promise.resolve({}):t(e)})),rt(nt).registerPrivateSelectors(s);const it=window.wp.hooks,ot=(0,de.createHigherOrderComponent)((e=>t=>{const{clientId:r,name:a}=t,{widgetAreas:n,currentWidgetAreaId:i,canInsertBlockInWidgetArea:o}=(0,m.useSelect)((e=>{if("core/widget-area"===a)return{};const t=e(nt),n=t.getParentWidgetAreaBlock(r);return{widgetAreas:t.getWidgetAreas(),currentWidgetAreaId:n?.attributes?.id,canInsertBlockInWidgetArea:t.canInsertBlockInWidgetArea(a)}}),[r,a]),{moveBlockToWidgetArea:s}=(0,m.useDispatch)(nt),c="core/widget-area"!==a&&n?.length>1&&o;return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(e,{...t}),c&&(0,l.createElement)(we.BlockControls,null,(0,l.createElement)(_.MoveToWidgetArea,{widgetAreas:n,currentWidgetAreaId:i,onSelect:e=>{s(t.clientId,e)}})))}),"withMoveToWidgetAreaToolbarItem");(0,it.addFilter)("editor.BlockEdit","core/edit-widgets/block-edit",ot);const st=window.wp.mediaUtils;(0,it.addFilter)("editor.MediaUpload","core/edit-widgets/replace-media-upload",(()=>st.MediaUpload));const ct=e=>{const[t,r]=(0,p.useState)(!1);return(0,p.useEffect)((()=>{const{ownerDocument:t}=e.current;function a(e){i(e)}function n(){r(!1)}function i(t){e.current.contains(t.target)?r(!0):r(!1)}return t.addEventListener("dragstart",a),t.addEventListener("dragend",n),t.addEventListener("dragenter",i),()=>{t.removeEventListener("dragstart",a),t.removeEventListener("dragend",n),t.removeEventListener("dragenter",i)}}),[]),t};function lt({id:e}){const[t,r,a]=(0,w.useEntityBlockEditor)("root","postType"),n=(0,p.useRef)(),i=ct(n),o=(0,we.useInnerBlocksProps)({ref:n},{value:t,onInput:r,onChange:a,templateLock:!1,renderAppender:we.InnerBlocks.ButtonBlockAppender});return(0,l.createElement)("div",{"data-widget-area-id":e,className:A()("wp-block-widget-area__inner-blocks block-editor-inner-blocks editor-styles-wrapper",{"wp-block-widget-area__highlight-drop-zone":i})},(0,l.createElement)("div",{...o}))}const dt=e=>{const[t,r]=(0,p.useState)(!1);return(0,p.useEffect)((()=>{const{ownerDocument:t}=e.current;function a(){r(!0)}function n(){r(!1)}return t.addEventListener("dragstart",a),t.addEventListener("dragend",n),()=>{t.removeEventListener("dragstart",a),t.removeEventListener("dragend",n)}}),[]),t},mt={$schema:"https://schemas.wp.org/trunk/block.json",name:"core/widget-area",category:"widgets",attributes:{id:{type:"string"},name:{type:"string"}},supports:{html:!1,inserter:!1,customClassName:!1,reusable:!1,__experimentalToolbar:!1,__experimentalParentSelector:!1,__experimentalDisableBlockOverlay:!0},editorStyle:"wp-block-widget-area-editor",style:"wp-block-widget-area"},{name:ut}=mt,gt={title:(0,v.__)("Widget Area"),description:(0,v.__)("A widget area container."),__experimentalLabel:({name:e})=>e,edit:function({clientId:e,className:t,attributes:{id:r,name:a}}){const n=(0,m.useSelect)((t=>t(nt).getIsWidgetAreaOpen(e)),[e]),{setIsWidgetAreaOpen:i}=(0,m.useDispatch)(nt),o=(0,p.useRef)(),s=(0,p.useCallback)((t=>i(e,t)),[e]),c=dt(o),d=ct(o),[u,g]=(0,p.useState)(!1);return(0,p.useEffect)((()=>{c?d&&!n?(s(!0),g(!0)):!d&&n&&u&&s(!1):g(!1)}),[n,c,d,u]),(0,l.createElement)(I.Panel,{className:t,ref:o},(0,l.createElement)(I.PanelBody,{title:a,opened:n,onToggle:()=>{i(e,!n)},scrollAfterOpen:!c},(({opened:e})=>(0,l.createElement)(I.__unstableDisclosureContent,{className:"wp-block-widget-area__panel-body-content",visible:e},(0,l.createElement)(w.EntityProvider,{kind:"root",type:"postType",id:`widget-area-${r}`},(0,l.createElement)(lt,{id:r}))))))}};function pt({text:e,children:t}){const r=(0,de.useCopyToClipboard)(e);return(0,l.createElement)(I.Button,{variant:"secondary",ref:r},t)}function ht({message:e,error:t}){const r=[(0,l.createElement)(pt,{key:"copy-error",text:t.stack},(0,v.__)("Copy Error"))];return(0,l.createElement)(we.Warning,{className:"edit-widgets-error-boundary",actions:r},e)}class wt extends p.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e){(0,it.doAction)("editor.ErrorBoundary.errorLogged",e)}static getDerivedStateFromError(e){return{error:e}}render(){return this.state.error?(0,l.createElement)(ht,{message:(0,v.__)("The editor has encountered an unexpected error."),error:this.state.error}):this.props.children}}const _t=window.wp.patterns,bt=window.wp.keyboardShortcuts,Et=window.wp.keycodes;function ft(){const{redo:e,undo:t}=(0,m.useDispatch)(w.store),{saveEditedWidgetAreas:r}=(0,m.useDispatch)(nt),{replaceBlocks:a}=(0,m.useDispatch)(we.store),{getBlockName:n,getSelectedBlockClientId:i,getBlockAttributes:o}=(0,m.useSelect)(we.store),s=(e,t)=>{e.preventDefault();const r=0===t?"core/paragraph":"core/heading",s=i();if(null===s)return;const c=n(s);if("core/paragraph"!==c&&"core/heading"!==c)return;const l=o(s),m="core/paragraph"===c?"align":"textAlign",u="core/paragraph"===r?"align":"textAlign";a(s,(0,d.createBlock)(r,{level:t,content:l.content,[u]:l[m]}))};return(0,bt.useShortcut)("core/edit-widgets/undo",(e=>{t(),e.preventDefault()})),(0,bt.useShortcut)("core/edit-widgets/redo",(t=>{e(),t.preventDefault()})),(0,bt.useShortcut)("core/edit-widgets/save",(e=>{e.preventDefault(),r()})),(0,bt.useShortcut)("core/edit-widgets/transform-heading-to-paragraph",(e=>s(e,0))),[1,2,3,4,5,6].forEach((e=>{(0,bt.useShortcut)(`core/edit-widgets/transform-paragraph-to-heading-${e}`,(t=>s(t,e)))})),null}ft.Register=function(){const{registerShortcut:e}=(0,m.useDispatch)(bt.store);return(0,p.useEffect)((()=>{e({name:"core/edit-widgets/undo",category:"global",description:(0,v.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/edit-widgets/redo",category:"global",description:(0,v.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"},aliases:(0,Et.isAppleOS)()?[]:[{modifier:"primary",character:"y"}]}),e({name:"core/edit-widgets/save",category:"global",description:(0,v.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),e({name:"core/edit-widgets/keyboard-shortcuts",category:"main",description:(0,v.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),e({name:"core/edit-widgets/next-region",category:"global",description:(0,v.__)("Navigate to the next part of the editor."),keyCombination:{modifier:"ctrl",character:"`"},aliases:[{modifier:"access",character:"n"}]}),e({name:"core/edit-widgets/previous-region",category:"global",description:(0,v.__)("Navigate to the previous part of the editor."),keyCombination:{modifier:"ctrlShift",character:"`"},aliases:[{modifier:"access",character:"p"},{modifier:"ctrlShift",character:"~"}]}),e({name:"core/edit-widgets/transform-heading-to-paragraph",category:"block-library",description:(0,v.__)("Transform heading to paragraph."),keyCombination:{modifier:"access",character:"0"}}),[1,2,3,4,5,6].forEach((t=>{e({name:`core/edit-widgets/transform-paragraph-to-heading-${t}`,category:"block-library",description:(0,v.__)("Transform paragraph to heading."),keyCombination:{modifier:"access",character:`${t}`}})}))}),[e]),null};const yt=ft,vt=()=>(0,m.useSelect)((e=>{const{getBlockSelectionEnd:t,getBlockName:r}=e(we.store),a=t();if("core/widget-area"===r(a))return a;const{getParentWidgetAreaBlock:n}=e(nt),i=n(a),o=i?.clientId;if(o)return o;const{getEntityRecord:s}=e(w.store),c=s(Ee,ye,ke());return c?.blocks[0]?.clientId}),[]),kt=!1,{ExperimentalBlockEditorProvider:St}=rt(we.privateApis),{PatternsMenuItems:At}=rt(_t.privateApis);function It({blockEditorSettings:e,children:t,...r}){const a=(0,w.useResourcePermissions)("media"),n=(0,de.useViewportMatch)("medium"),{reusableBlocks:i,isFixedToolbarActive:o,keepCaretInsideBlock:s,pageOnFront:c,pageForPosts:d}=(0,m.useSelect)((e=>{const{canUser:t,getEntityRecord:r,getEntityRecords:a}=e(w.store),n=t("read","settings")?r("root","site"):void 0;return{widgetAreas:e(nt).getWidgetAreas(),widgets:e(nt).getWidgets(),reusableBlocks:kt?a("postType","wp_block"):[],isFixedToolbarActive:!!e(b.store).get("core/edit-widgets","fixedToolbar"),keepCaretInsideBlock:!!e(b.store).get("core/edit-widgets","keepCaretInsideBlock"),pageOnFront:n?.page_on_front,pageForPosts:n?.page_for_posts}}),[]),{setIsInserterOpened:u}=(0,m.useDispatch)(nt),g=(0,p.useMemo)((()=>{let t;return a.canCreate&&(t=({onError:t,...r})=>{(0,st.uploadMedia)({wpAllowedMimeTypes:e.allowedMimeTypes,onError:({message:e})=>t(e),...r})}),{...e,__experimentalReusableBlocks:i,hasFixedToolbar:o||!n,keepCaretInsideBlock:s,mediaUpload:t,templateLock:"all",__experimentalSetIsInserterOpened:u,pageOnFront:c,pageForPosts:d}}),[e,o,n,s,a.canCreate,i,u,c,d]),h=vt(),[_,E,f]=(0,w.useEntityBlockEditor)(Ee,ye,{id:ke()});return(0,l.createElement)(I.SlotFillProvider,null,(0,l.createElement)(yt.Register,null),(0,l.createElement)(St,{value:_,onInput:E,onChange:f,settings:g,useSubRegistry:!1,...r},t,(0,l.createElement)(At,{rootClientId:h})))}const Ct=(0,l.createElement)(C.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM8.5 18.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h2.5v13zm10-.5c0 .3-.2.5-.5.5h-8v-13h8c.3 0 .5.2.5.5v12z"})),Bt=(0,l.createElement)(C.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-4 14.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h8v13zm4.5-.5c0 .3-.2.5-.5.5h-2.5v-13H18c.3 0 .5.2.5.5v12z"})),Nt=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})),xt=window.wp.url,Tt=window.wp.dom;function Wt({selectedWidgetAreaId:e}){const t=(0,m.useSelect)((e=>e(nt).getWidgetAreas()),[]),r=(0,p.useMemo)((()=>e&&t?.find((t=>t.id===e))),[e,t]);let a;return a=r?"wp_inactive_widgets"===e?(0,v.__)("Blocks in this Widget Area will not be displayed in your site."):r.description:(0,v.__)("Widget Areas are global parts in your site’s layout that can accept blocks. These vary by theme, but are typically parts like your Sidebar or Footer."),(0,l.createElement)("div",{className:"edit-widgets-widget-areas"},(0,l.createElement)("div",{className:"edit-widgets-widget-areas__top-container"},(0,l.createElement)(we.BlockIcon,{icon:Nt}),(0,l.createElement)("div",null,(0,l.createElement)("p",{dangerouslySetInnerHTML:{__html:(0,Tt.safeHTML)(a)}}),0===t?.length&&(0,l.createElement)("p",null,(0,v.__)("Your theme does not contain any Widget Areas.")),!r&&(0,l.createElement)(I.Button,{href:(0,xt.addQueryArgs)("customize.php",{"autofocus[panel]":"widgets",return:window.location.pathname}),variant:"tertiary"},(0,v.__)("Manage with live preview")))))}const Lt=p.Platform.select({web:!0,native:!1}),Pt="edit-widgets/block-inspector",Rt="edit-widgets/block-areas",{Tabs:Ot}=rt(I.privateApis);function Mt({selectedWidgetAreaBlock:e}){return(0,l.createElement)(Ot.TabList,null,(0,l.createElement)(Ot.Tab,{tabId:Rt},e?e.attributes.name:(0,v.__)("Widget Areas")),(0,l.createElement)(Ot.Tab,{tabId:Pt},(0,v.__)("Block")))}function Vt({hasSelectedNonAreaBlock:e,currentArea:t,isGeneralSidebarOpen:r,selectedWidgetAreaBlock:a}){const{enableComplementaryArea:n}=(0,m.useDispatch)(Z);(0,p.useEffect)((()=>{e&&t===Rt&&r&&n("core/edit-widgets",Pt),!e&&t===Pt&&r&&n("core/edit-widgets",Rt)}),[e,n]);const i=(0,p.useContext)(Ot.Context);return(0,l.createElement)(le,{className:"edit-widgets-sidebar",header:(0,l.createElement)(Ot.Context.Provider,{value:i},(0,l.createElement)(Mt,{selectedWidgetAreaBlock:a})),headerClassName:"edit-widgets-sidebar__panel-tabs",title:(0,v.__)("Settings"),closeLabel:(0,v.__)("Close Settings"),scope:"core/edit-widgets",identifier:t,icon:(0,v.isRTL)()?Ct:Bt,isActiveByDefault:Lt},(0,l.createElement)(Ot.Context.Provider,{value:i},(0,l.createElement)(Ot.TabPanel,{tabId:Rt,focusable:!1},(0,l.createElement)(Wt,{selectedWidgetAreaId:a?.attributes.id})),(0,l.createElement)(Ot.TabPanel,{tabId:Pt,focusable:!1},e?(0,l.createElement)(we.BlockInspector,null):(0,l.createElement)("span",{className:"block-editor-block-inspector__no-blocks"},(0,v.__)("No block selected.")))))}function Dt(){const{currentArea:e,hasSelectedNonAreaBlock:t,isGeneralSidebarOpen:r,selectedWidgetAreaBlock:a}=(0,m.useSelect)((e=>{const{getSelectedBlock:t,getBlock:r,getBlockParentsByBlockName:a}=e(we.store),{getActiveComplementaryArea:n}=e(Z),i=t(),o=n(nt.name);let s,c=o;return c||(c=i?Pt:Rt),i&&(s="core/widget-area"===i.name?i:r(a(i.clientId,"core/widget-area")[0])),{currentArea:c,hasSelectedNonAreaBlock:!(!i||"core/widget-area"===i.name),isGeneralSidebarOpen:!!o,selectedWidgetAreaBlock:s}}),[]),{enableComplementaryArea:n}=(0,m.useDispatch)(Z),i=(0,p.useCallback)((e=>{e&&n(nt.name,e)}),[n]);return(0,l.createElement)(Ot,{selectedTabId:r?e:null,onSelect:i,selectOnMove:!1},(0,l.createElement)(Vt,{hasSelectedNonAreaBlock:t,currentArea:e,isGeneralSidebarOpen:r,selectedWidgetAreaBlock:a}))}const Ft=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})),Gt=(0,l.createElement)(C.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,l.createElement)(C.Path,{d:"M3 6h11v1.5H3V6Zm3.5 5.5h11V13h-11v-1.5ZM21 17H10v1.5h11V17Z"})),Ht=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"})),zt=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"}));const Ut=(0,p.forwardRef)((function(e,t){const r=(0,m.useSelect)((e=>e(w.store).hasUndo()),[]),{undo:a}=(0,m.useDispatch)(w.store);return(0,l.createElement)(I.Button,{...e,ref:t,icon:(0,v.isRTL)()?zt:Ht,label:(0,v.__)("Undo"),shortcut:Et.displayShortcut.primary("z"),"aria-disabled":!r,onClick:r?a:void 0})}));const $t=(0,p.forwardRef)((function(e,t){const r=(0,Et.isAppleOS)()?Et.displayShortcut.primaryShift("z"):Et.displayShortcut.primary("y"),a=(0,m.useSelect)((e=>e(w.store).hasRedo()),[]),{redo:n}=(0,m.useDispatch)(w.store);return(0,l.createElement)(I.Button,{...e,ref:t,icon:(0,v.isRTL)()?Ht:zt,label:(0,v.__)("Redo"),shortcut:r,"aria-disabled":!a,onClick:a?n:void 0})})),{useCanBlockToolbarBeFocused:jt}=rt(we.privateApis);const Yt=function(){const e=(0,de.useViewportMatch)("medium"),t=(0,p.useRef)(),r=vt(),a=(0,m.useSelect)((e=>e(nt).getIsWidgetAreaOpen(r)),[r]),{isInserterOpen:n,isListViewOpen:i,listViewToggleRef:o}=(0,m.useSelect)((e=>{const{isInserterOpened:t,isListViewOpened:r,getListViewToggleRef:a}=rt(e(nt));return{isInserterOpen:t(),isListViewOpen:r(),listViewToggleRef:a()}}),[]),{setIsWidgetAreaOpen:s,setIsInserterOpened:c,setIsListViewOpened:d}=(0,m.useDispatch)(nt),{selectBlock:u}=(0,m.useDispatch)(we.store),g=(0,p.useCallback)((()=>d(!i)),[d,i]),h=jt();return(0,l.createElement)(we.NavigableToolbar,{className:"edit-widgets-header-toolbar","aria-label":(0,v.__)("Document tools"),shouldUseKeyboardFocusShortcut:!h,variant:"unstyled"},(0,l.createElement)(I.ToolbarItem,{ref:t,as:I.Button,className:"edit-widgets-header-toolbar__inserter-toggle",variant:"primary",isPressed:n,onMouseDown:e=>{e.preventDefault()},onClick:()=>{n?c(!1):(a||(u(r),s(r,!0)),window.requestAnimationFrame((()=>c(!0))))},icon:Ft,label:(0,v._x)("Toggle block inserter","Generic label for block inserter button")}),e&&(0,l.createElement)(l.Fragment,null,(0,l.createElement)(I.ToolbarItem,{as:Ut}),(0,l.createElement)(I.ToolbarItem,{as:$t}),(0,l.createElement)(I.ToolbarItem,{as:I.Button,className:"edit-widgets-header-toolbar__list-view-toggle",icon:Gt,isPressed:i,label:(0,v.__)("List View"),onClick:g,ref:o})))};const Kt=function(){const{hasEditedWidgetAreaIds:e,isSaving:t}=(0,m.useSelect)((e=>{const{getEditedWidgetAreas:t,isSavingWidgetAreas:r}=e(nt);return{hasEditedWidgetAreaIds:t()?.length>0,isSaving:r()}}),[]),{saveEditedWidgetAreas:r}=(0,m.useDispatch)(nt),a=t||!e;return(0,l.createElement)(I.Button,{variant:"primary",isBusy:t,"aria-disabled":a,onClick:a?void 0:r},t?(0,v.__)("Saving…"):(0,v.__)("Update"))},Zt=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})),qt=[{keyCombination:{modifier:"primary",character:"b"},description:(0,v.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,v.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,v.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,v.__)("Remove a link.")},{keyCombination:{character:"[["},description:(0,v.__)("Insert a link to a post or page.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,v.__)("Underline the selected text.")},{keyCombination:{modifier:"access",character:"d"},description:(0,v.__)("Strikethrough the selected text.")},{keyCombination:{modifier:"access",character:"x"},description:(0,v.__)("Make the selected text inline code.")},{keyCombination:{modifier:"access",character:"0"},description:(0,v.__)("Convert the current heading to a paragraph.")},{keyCombination:{modifier:"access",character:"1-6"},description:(0,v.__)("Convert the current paragraph or heading to a heading of level 1 to 6.")}];function Qt({keyCombination:e,forceAriaLabel:t}){const r=e.modifier?Et.displayShortcutList[e.modifier](e.character):e.character,a=e.modifier?Et.shortcutAriaLabel[e.modifier](e.character):e.character,n=Array.isArray(r)?r:[r];return(0,l.createElement)("kbd",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":t||a},n.map(((e,t)=>"+"===e?(0,l.createElement)(p.Fragment,{key:t},e):(0,l.createElement)("kbd",{key:t,className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-key"},e))))}const Jt=function({description:e,keyCombination:t,aliases:r=[],ariaLabel:a}){return(0,l.createElement)(p.Fragment,null,(0,l.createElement)("div",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-description"},e),(0,l.createElement)("div",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-term"},(0,l.createElement)(Qt,{keyCombination:t,forceAriaLabel:a}),r.map(((e,t)=>(0,l.createElement)(Qt,{keyCombination:e,forceAriaLabel:a,key:t})))))};const Xt=function({name:e}){const{keyCombination:t,description:r,aliases:a}=(0,m.useSelect)((t=>{const{getShortcutKeyCombination:r,getShortcutDescription:a,getShortcutAliases:n}=t(bt.store);return{keyCombination:r(e),aliases:n(e),description:a(e)}}),[e]);return t?(0,l.createElement)(Jt,{keyCombination:t,description:r,aliases:a}):null},er=({shortcuts:e})=>(0,l.createElement)("ul",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut-list",role:"list"},e.map(((e,t)=>(0,l.createElement)("li",{className:"edit-widgets-keyboard-shortcut-help-modal__shortcut",key:t},"string"==typeof e?(0,l.createElement)(Xt,{name:e}):(0,l.createElement)(Jt,{...e}))))),tr=({title:e,shortcuts:t,className:r})=>(0,l.createElement)("section",{className:A()("edit-widgets-keyboard-shortcut-help-modal__section",r)},!!e&&(0,l.createElement)("h2",{className:"edit-widgets-keyboard-shortcut-help-modal__section-title"},e),(0,l.createElement)(er,{shortcuts:t})),rr=({title:e,categoryName:t,additionalShortcuts:r=[]})=>{const a=(0,m.useSelect)((e=>e(bt.store).getCategoryShortcuts(t)),[t]);return(0,l.createElement)(tr,{title:e,shortcuts:a.concat(r)})};function ar({isModalActive:e,toggleModal:t}){return(0,bt.useShortcut)("core/edit-widgets/keyboard-shortcuts",t,{bindGlobal:!0}),e?(0,l.createElement)(I.Modal,{className:"edit-widgets-keyboard-shortcut-help-modal",title:(0,v.__)("Keyboard shortcuts"),onRequestClose:t},(0,l.createElement)(tr,{className:"edit-widgets-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/edit-widgets/keyboard-shortcuts"]}),(0,l.createElement)(rr,{title:(0,v.__)("Global shortcuts"),categoryName:"global"}),(0,l.createElement)(rr,{title:(0,v.__)("Selection shortcuts"),categoryName:"selection"}),(0,l.createElement)(rr,{title:(0,v.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,v.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,v.__)("Forward-slash")}]}),(0,l.createElement)(tr,{title:(0,v.__)("Text formatting"),shortcuts:qt})):null}const{Fill:nr,Slot:ir}=(0,I.createSlotFill)("EditWidgetsToolsMoreMenuGroup");nr.Slot=({fillProps:e})=>(0,l.createElement)(ir,{fillProps:e},(e=>e.length>0&&e));const or=nr;function sr(){const[e,t]=(0,p.useState)(!1),r=()=>t(!e);(0,bt.useShortcut)("core/edit-widgets/keyboard-shortcuts",r);const a=(0,de.useViewportMatch)("medium");return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(he,null,(e=>(0,l.createElement)(l.Fragment,null,a&&(0,l.createElement)(I.MenuGroup,{label:(0,v._x)("View","noun")},(0,l.createElement)(b.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"fixedToolbar",label:(0,v.__)("Top toolbar"),info:(0,v.__)("Access all block and document tools in a single place"),messageActivated:(0,v.__)("Top toolbar activated"),messageDeactivated:(0,v.__)("Top toolbar deactivated")})),(0,l.createElement)(I.MenuGroup,{label:(0,v.__)("Tools")},(0,l.createElement)(I.MenuItem,{onClick:()=>{t(!0)},shortcut:Et.displayShortcut.access("h")},(0,v.__)("Keyboard shortcuts")),(0,l.createElement)(b.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"welcomeGuide",label:(0,v.__)("Welcome Guide")}),(0,l.createElement)(I.MenuItem,{role:"menuitem",icon:Zt,href:(0,v.__)("https://wordpress.org/documentation/article/block-based-widgets-editor/"),target:"_blank",rel:"noopener noreferrer"},(0,v.__)("Help"),(0,l.createElement)(I.VisuallyHidden,{as:"span"},(0,v.__)("(opens in a new tab)"))),(0,l.createElement)(or.Slot,{fillProps:{onClose:e}})),(0,l.createElement)(I.MenuGroup,{label:(0,v.__)("Preferences")},(0,l.createElement)(b.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"keepCaretInsideBlock",label:(0,v.__)("Contain text cursor inside block"),info:(0,v.__)("Aids screen readers by stopping text caret from leaving blocks."),messageActivated:(0,v.__)("Contain text cursor inside block activated"),messageDeactivated:(0,v.__)("Contain text cursor inside block deactivated")}),(0,l.createElement)(b.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"themeStyles",info:(0,v.__)("Make the editor look like your theme."),label:(0,v.__)("Use theme styles")}),a&&(0,l.createElement)(b.PreferenceToggleMenuItem,{scope:"core/edit-widgets",name:"showBlockBreadcrumbs",label:(0,v.__)("Display block breadcrumbs"),info:(0,v.__)("Shows block breadcrumbs at the bottom of the editor."),messageActivated:(0,v.__)("Display block breadcrumbs activated"),messageDeactivated:(0,v.__)("Display block breadcrumbs deactivated")}))))),(0,l.createElement)(ar,{isModalActive:e,toggleModal:r}))}const cr=function(){const e=(0,de.useViewportMatch)("medium"),t=(0,p.useRef)(),{hasFixedToolbar:r}=(0,m.useSelect)((e=>({hasFixedToolbar:!!e(b.store).get("core/edit-widgets","fixedToolbar")})),[]);return(0,l.createElement)(l.Fragment,null,(0,l.createElement)("div",{className:"edit-widgets-header"},(0,l.createElement)("div",{className:"edit-widgets-header__navigable-toolbar-wrapper"},e&&(0,l.createElement)("h1",{className:"edit-widgets-header__title"},(0,v.__)("Widgets")),!e&&(0,l.createElement)(I.VisuallyHidden,{as:"h1",className:"edit-widgets-header__title"},(0,v.__)("Widgets")),(0,l.createElement)(Yt,null),r&&e&&(0,l.createElement)(l.Fragment,null,(0,l.createElement)("div",{className:"selected-block-tools-wrapper"},(0,l.createElement)(we.BlockToolbar,{hideDragHandle:!0})),(0,l.createElement)(I.Popover.Slot,{ref:t,name:"block-toolbar"}))),(0,l.createElement)("div",{className:"edit-widgets-header__actions"},(0,l.createElement)(Kt,null),(0,l.createElement)(oe.Slot,{scope:"core/edit-widgets"}),(0,l.createElement)(sr,null))))};const lr=function(){const{removeNotice:e}=(0,m.useDispatch)(k.store),{notices:t}=(0,m.useSelect)((e=>({notices:e(k.store).getNotices()})),[]),r=t.filter((({isDismissible:e,type:t})=>e&&"default"===t)),a=t.filter((({isDismissible:e,type:t})=>!e&&"default"===t)),n=t.filter((({type:e})=>"snackbar"===e)).slice(-3);return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(I.NoticeList,{notices:a,className:"edit-widgets-notices__pinned"}),(0,l.createElement)(I.NoticeList,{notices:r,className:"edit-widgets-notices__dismissible",onRemove:e}),(0,l.createElement)(I.SnackbarList,{notices:n,className:"edit-widgets-notices__snackbar",onRemove:e}))};function dr({blockEditorSettings:e}){const t=(0,m.useSelect)((e=>!!e(b.store).get("core/edit-widgets","themeStyles")),[]),r=(0,de.useViewportMatch)("medium"),a=(0,p.useMemo)((()=>t?e.styles:[]),[e,t]);return(0,l.createElement)("div",{className:"edit-widgets-block-editor"},(0,l.createElement)(lr,null),!r&&(0,l.createElement)(we.BlockToolbar,{hideDragHandle:!0}),(0,l.createElement)(we.BlockTools,null,(0,l.createElement)(yt,null),(0,l.createElement)(we.__unstableEditorStyles,{styles:a,scope:".editor-styles-wrapper"}),(0,l.createElement)(we.BlockSelectionClearer,null,(0,l.createElement)(we.WritingFlow,null,(0,l.createElement)(we.BlockList,{className:"edit-widgets-main-block-list"})))))}const mr=(0,l.createElement)(C.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(C.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"})),ur=()=>{const e=(0,m.useSelect)((e=>{const{getEntityRecord:t}=e(w.store),r=t(Ee,ye,ke());return r?.blocks[0]?.clientId}),[]);return(0,m.useSelect)((t=>{const{getBlockRootClientId:r,getBlockSelectionEnd:a,getBlockOrder:n,getBlockIndex:i}=t(we.store),o=t(nt).__experimentalGetInsertionPoint();if(o.rootClientId)return o;const s=a()||e,c=r(s);return s&&""===c?{rootClientId:s,insertionIndex:n(s).length}:{rootClientId:c,insertionIndex:i(s)+1}}),[e])};function gr(){const e=(0,de.useViewportMatch)("medium","<"),{rootClientId:t,insertionIndex:r}=ur(),{setIsInserterOpened:a}=(0,m.useDispatch)(nt),n=(0,p.useCallback)((()=>a(!1)),[a]),i=e?"div":I.VisuallyHidden,[o,s]=(0,de.__experimentalUseDialog)({onClose:n,focusOnMount:null}),c=(0,p.useRef)();return(0,p.useEffect)((()=>{c.current.focusSearch()}),[]),(0,l.createElement)("div",{ref:o,...s,className:"edit-widgets-layout__inserter-panel"},(0,l.createElement)(i,{className:"edit-widgets-layout__inserter-panel-header"},(0,l.createElement)(I.Button,{icon:mr,onClick:n,label:(0,v.__)("Close block inserter")})),(0,l.createElement)("div",{className:"edit-widgets-layout__inserter-panel-content"},(0,l.createElement)(we.__experimentalLibrary,{showInserterHelpPanel:!0,shouldFocusBlock:e,rootClientId:t,__experimentalInsertionIndex:r,ref:c})))}function pr(){const{setIsListViewOpened:e}=(0,m.useDispatch)(nt),{getListViewToggleRef:t}=rt((0,m.useSelect)(nt)),[r,a]=(0,p.useState)(null),n=(0,de.useFocusOnMount)("firstElement"),i=(0,p.useCallback)((()=>{e(!1),t().current?.focus()}),[t,e]),o=(0,p.useCallback)((e=>{e.keyCode!==Et.ESCAPE||e.defaultPrevented||(e.preventDefault(),i())}),[i]);return(0,l.createElement)("div",{className:"edit-widgets-editor__list-view-panel",onKeyDown:o},(0,l.createElement)("div",{className:"edit-widgets-editor__list-view-panel-header"},(0,l.createElement)("strong",null,(0,v.__)("List View")),(0,l.createElement)(I.Button,{icon:W,label:(0,v.__)("Close"),onClick:i})),(0,l.createElement)("div",{className:"edit-widgets-editor__list-view-panel-content",ref:(0,de.useMergeRefs)([n,a])},(0,l.createElement)(we.__experimentalListView,{dropZoneElement:r})))}function hr(){const{isInserterOpen:e,isListViewOpen:t}=(0,m.useSelect)((e=>{const{isInserterOpened:t,isListViewOpened:r}=e(nt);return{isInserterOpen:t(),isListViewOpen:r()}}),[]);return e?(0,l.createElement)(gr,null):t?(0,l.createElement)(pr,null):null}const wr={header:(0,v.__)("Widgets top bar"),body:(0,v.__)("Widgets and blocks"),sidebar:(0,v.__)("Widgets settings"),footer:(0,v.__)("Widgets footer")};const _r=function({blockEditorSettings:e}){const t=(0,de.useViewportMatch)("medium","<"),r=(0,de.useViewportMatch)("huge",">="),{setIsInserterOpened:a,setIsListViewOpened:n,closeGeneralSidebar:i}=(0,m.useDispatch)(nt),{hasBlockBreadCrumbsEnabled:o,hasSidebarEnabled:s,isInserterOpened:c,isListViewOpened:d,previousShortcut:u,nextShortcut:g}=(0,m.useSelect)((e=>({hasSidebarEnabled:!!e(Z).getActiveComplementaryArea(nt.name),isInserterOpened:!!e(nt).isInserterOpened(),isListViewOpened:!!e(nt).isListViewOpened(),hasBlockBreadCrumbsEnabled:!!e(b.store).get("core/edit-widgets","showBlockBreadcrumbs"),previousShortcut:e(bt.store).getAllShortcutKeyCombinations("core/edit-widgets/previous-region"),nextShortcut:e(bt.store).getAllShortcutKeyCombinations("core/edit-widgets/next-region")})),[]);(0,p.useEffect)((()=>{s&&!r&&(a(!1),n(!1))}),[s,r]),(0,p.useEffect)((()=>{!c&&!d||r||i()}),[c,d,r]);const h=d?(0,v.__)("List View"):(0,v.__)("Block Library"),w=d||c;return(0,l.createElement)(ge,{labels:{...wr,secondarySidebar:h},header:(0,l.createElement)(cr,null),secondarySidebar:w&&(0,l.createElement)(hr,null),sidebar:s&&(0,l.createElement)(le.Slot,{scope:"core/edit-widgets"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)(dr,{blockEditorSettings:e})),footer:o&&!t&&(0,l.createElement)("div",{className:"edit-widgets-layout__footer"},(0,l.createElement)(we.BlockBreadcrumb,{rootLabelText:(0,v.__)("Widgets")})),shortcuts:{previous:u,next:g}})};function br(){const e=(0,m.useSelect)((e=>{const{getEditedWidgetAreas:t}=e(nt),r=t();return r?.length>0}),[]);return(0,p.useEffect)((()=>{const t=t=>{if(e)return t.returnValue=(0,v.__)("You have unsaved changes. If you proceed, they will be lost."),t.returnValue};return window.addEventListener("beforeunload",t),()=>{window.removeEventListener("beforeunload",t)}}),[e]),null}function Er(){var e;const t=(0,m.useSelect)((e=>!!e(b.store).get("core/edit-widgets","welcomeGuide")),[]),{toggle:r}=(0,m.useDispatch)(b.store),a=(0,m.useSelect)((e=>e(nt).getWidgetAreas({per_page:-1})),[]);if(!t)return null;const n=a?.every((e=>"wp_inactive_widgets"===e.id||e.widgets.every((e=>e.startsWith("block-"))))),i=null!==(e=a?.filter((e=>"wp_inactive_widgets"!==e.id)).length)&&void 0!==e?e:0;return(0,l.createElement)(I.Guide,{className:"edit-widgets-welcome-guide",contentLabel:(0,v.__)("Welcome to block Widgets"),finishButtonText:(0,v.__)("Get started"),onFinish:()=>r("core/edit-widgets","welcomeGuide"),pages:[{image:(0,l.createElement)(fr,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-canvas.gif"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)("h1",{className:"edit-widgets-welcome-guide__heading"},(0,v.__)("Welcome to block Widgets")),n?(0,l.createElement)(l.Fragment,null,(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,v.sprintf)((0,v._n)("Your theme provides %s “block” area for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site.","Your theme provides %s different “block” areas for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site.",i),i))):(0,l.createElement)(l.Fragment,null,(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,v.__)("You can now add any block to your site’s widget areas. Don’t worry, all of your favorite widgets still work flawlessly.")),(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,l.createElement)("strong",null,(0,v.__)("Want to stick with the old widgets?"))," ",(0,l.createElement)(I.ExternalLink,{href:(0,v.__)("https://wordpress.org/plugins/classic-widgets/")},(0,v.__)("Get the Classic Widgets plugin.")))))},{image:(0,l.createElement)(fr,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-editor.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-editor.gif"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)("h1",{className:"edit-widgets-welcome-guide__heading"},(0,v.__)("Make each block your own")),(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,v.__)("Each block comes with its own set of controls for changing things like color, width, and alignment. These will show and hide automatically when you have a block selected.")))},{image:(0,l.createElement)(fr,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-library.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-library.gif"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)("h1",{className:"edit-widgets-welcome-guide__heading"},(0,v.__)("Get to know the block library")),(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,p.createInterpolateElement)((0,v.__)("All of the blocks available to you live in the block library. You’ll find it wherever you see the <InserterIconImage /> icon."),{InserterIconImage:(0,l.createElement)("img",{className:"edit-widgets-welcome-guide__inserter-icon",alt:(0,v.__)("inserter"),src:"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='18' height='18' rx='2' fill='%231E1E1E'/%3E%3Cpath d='M9.22727 4V14M4 8.77273H14' stroke='white' stroke-width='1.5'/%3E%3C/svg%3E%0A"})})))},{image:(0,l.createElement)(fr,{nonAnimatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.svg",animatedSrc:"https://s.w.org/images/block-editor/welcome-documentation.gif"}),content:(0,l.createElement)(l.Fragment,null,(0,l.createElement)("h1",{className:"edit-widgets-welcome-guide__heading"},(0,v.__)("Learn how to use the block editor")),(0,l.createElement)("p",{className:"edit-widgets-welcome-guide__text"},(0,v.__)("New to the block editor? Want to learn more about using it? "),(0,l.createElement)(I.ExternalLink,{href:(0,v.__)("https://wordpress.org/documentation/article/wordpress-block-editor/")},(0,v.__)("Here's a detailed guide."))))}]})}function fr({nonAnimatedSrc:e,animatedSrc:t}){return(0,l.createElement)("picture",{className:"edit-widgets-welcome-guide__image"},(0,l.createElement)("source",{srcSet:e,media:"(prefers-reduced-motion: reduce)"}),(0,l.createElement)("img",{src:t,width:"312",height:"240",alt:""}))}const yr=function({blockEditorSettings:e}){const{createErrorNotice:t}=(0,m.useDispatch)(k.store);return(0,l.createElement)(wt,null,(0,l.createElement)(It,{blockEditorSettings:e},(0,l.createElement)(_r,{blockEditorSettings:e}),(0,l.createElement)(Dt,null),(0,l.createElement)(q.PluginArea,{onError:function(e){t((0,v.sprintf)((0,v.__)('The "%s" plugin has encountered an error and cannot be rendered.'),e))}}),(0,l.createElement)(br,null),(0,l.createElement)(Er,null)))},vr=["core/more","core/freeform","core/template-part",...kt?[]:["core/block"]];function kr(e,t){const r=document.getElementById(e),a=(0,p.createRoot)(r),n=(0,h.__experimentalGetCoreBlocks)().filter((e=>!(vr.includes(e.name)||e.name.startsWith("core/post")||e.name.startsWith("core/query")||e.name.startsWith("core/site")||e.name.startsWith("core/navigation"))));return(0,m.dispatch)(b.store).setDefaults("core/edit-widgets",{fixedToolbar:!1,welcomeGuide:!0,showBlockBreadcrumbs:!0,themeStyles:!0}),(0,m.dispatch)(d.store).reapplyBlockTypeFilters(),(0,h.registerCoreBlocks)(n),(0,_.registerLegacyWidgetBlock)(),(0,_.registerLegacyWidgetVariations)(t),Ir(c),(0,_.registerWidgetGroupBlock)(),t.__experimentalFetchLinkSuggestions=(e,r)=>(0,w.__experimentalFetchLinkSuggestions)(e,r,t),(0,d.setFreeformContentHandlerName)("core/html"),a.render((0,l.createElement)(yr,{blockEditorSettings:t})),a}const Sr=kr;function Ar(){g()("wp.editWidgets.reinitializeEditor",{since:"6.2",version:"6.3"})}const Ir=e=>{if(!e)return;const{metadata:t,settings:r,name:a}=e;t&&(0,d.unstable__bootstrapServerSideBlockDefinitions)({[a]:t}),(0,d.registerBlockType)(a,r)}})(),(window.wp=window.wp||{}).editWidgets=a})();