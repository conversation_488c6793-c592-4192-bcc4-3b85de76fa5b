/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,n)=>{for(var a in n)e.o(n,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:n[a]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{privateApis:()=>q,store:()=>C});var n={};e.r(n),e.d(n,{convertSyncedPatternToStatic:()=>g,createPattern:()=>m,createPatternFromFile:()=>_,setEditingPattern:()=>y});var a={};e.r(a),e.d(a,{isEditingPattern:()=>f});const r=window.wp.data;const o=(0,r.combineReducers)({isEditingPattern:function(e={},t){return"SET_EDITING_PATTERN"===t?.type?{...e,[t.clientId]:t.isEditing}:e}}),s=window.wp.blocks,c=window.wp.coreData,i=window.wp.blockEditor,l={theme:"pattern",user:"wp_block"},u="all-patterns",d={full:"fully",unsynced:"unsynced"},p={"core/paragraph":["content"],"core/heading":["content"],"core/button":["text","url","linkTarget","rel"],"core/image":["id","url","title","alt"]},m=(e,t,n,a)=>async({registry:r})=>{const o=t===d.unsynced?{wp_pattern_sync_status:t}:void 0,s={title:e,content:n,status:"publish",meta:o,wp_pattern_category:a};return await r.dispatch(c.store).saveEntityRecord("postType","wp_block",s)},_=(e,t)=>async({dispatch:n})=>{const a=await e.text();let r;try{r=JSON.parse(a)}catch(e){throw new Error("Invalid JSON file")}if("wp_block"!==r.__file||!r.title||!r.content||"string"!=typeof r.title||"string"!=typeof r.content||r.syncStatus&&"string"!=typeof r.syncStatus)throw new Error("Invalid pattern JSON file");return await n.createPattern(r.title,r.syncStatus,r.content,t)},g=e=>({registry:t})=>{const n=t.select(i.store).getBlock(e);t.dispatch(i.store).replaceBlocks(n.clientId,function e(t){return t.map((t=>{let n=t.attributes.metadata;return n&&(n={...n},delete n.id,delete n.bindings),(0,s.cloneBlock)(t,{metadata:n&&Object.keys(n).length>0?n:void 0},e(t.innerBlocks))}))}(n.innerBlocks))};function y(e,t){return{type:"SET_EDITING_PATTERN",clientId:e,isEditing:t}}function f(e,t){return e.isEditingPattern[t]}const w=window.wp.privateApis,{lock:E,unlock:b}=(0,w.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I know using unstable features means my theme or plugin will inevitably break in the next version of WordPress.","@wordpress/patterns"),S={reducer:o},C=(0,r.createReduxStore)("core/patterns",{...S});(0,r.register)(C),b(C).registerPrivateActions(n),b(C).registerPrivateSelectors(a);const k=window.React,v=window.wp.components,h=window.wp.i18n,P=window.wp.element,T=window.wp.notices,B=window.wp.compose,x=window.wp.htmlEntities,R=e=>(0,x.decodeEntities)(e),I="wp_pattern_category";function D({categoryTerms:e,onChange:t,categoryMap:n}){const[a,r]=(0,P.useState)(""),o=(0,B.useDebounce)(r,500),s=(0,P.useMemo)((()=>Array.from(n.values()).map((e=>R(e.label))).filter((e=>""===a||e.toLowerCase().includes(a.toLowerCase()))).sort(((e,t)=>e.localeCompare(t)))),[a,n]);return(0,k.createElement)(v.FormTokenField,{className:"patterns-menu-items__convert-modal-categories",value:e,suggestions:s,onChange:function(e){const n=e.reduce(((e,t)=>(e.some((e=>e.toLowerCase()===t.toLowerCase()))||e.push(t),e)),[]);t(n)},onInputChange:o,label:(0,h.__)("Categories"),tokenizeOnBlur:!0,__experimentalExpandOnFocus:!0,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0})}function N(){const{saveEntityRecord:e,invalidateResolution:t}=(0,r.useDispatch)(c.store),{corePatternCategories:n,userPatternCategories:a}=(0,r.useSelect)((e=>{const{getUserPatternCategories:t,getBlockPatternCategories:n}=e(c.store);return{corePatternCategories:n(),userPatternCategories:t()}}),[]),o=(0,P.useMemo)((()=>{const e=new Map;return a.forEach((t=>{e.set(t.label.toLowerCase(),{label:t.label,name:t.name,id:t.id})})),n.forEach((t=>{e.has(t.label.toLowerCase())||"query"===t.name||e.set(t.label.toLowerCase(),{label:t.label,name:t.name})})),e}),[a,n]);return{categoryMap:o,findOrCreateTerm:async function(n){try{const a=o.get(n.toLowerCase());if(a?.id)return a.id;const r=a?{name:a.label,slug:a.name}:{name:n},s=await e("taxonomy",I,r,{throwOnError:!0});return t("getUserPatternCategories"),s.id}catch(e){if("term_exists"!==e.code)throw e;return e.data.term_id}}}}function M({className:e="patterns-menu-items__convert-modal",modalTitle:t=(0,h.__)("Create pattern"),...n}){return(0,k.createElement)(v.Modal,{title:t,onRequestClose:n.onClose,overlayClassName:e},(0,k.createElement)(O,{...n}))}function O({confirmLabel:e=(0,h.__)("Create"),defaultCategories:t=[],content:n,onClose:a,onError:o,onSuccess:s,defaultSyncType:c=d.full,defaultTitle:i=""}){const[l,p]=(0,P.useState)(c),[m,_]=(0,P.useState)(t),[g,y]=(0,P.useState)(i),[f,w]=(0,P.useState)(!1),{createPattern:E}=b((0,r.useDispatch)(C)),{createErrorNotice:S}=(0,r.useDispatch)(T.store),{categoryMap:B,findOrCreateTerm:x}=N();return(0,k.createElement)("form",{onSubmit:e=>{e.preventDefault(),async function(e,t){if(g&&!f)try{w(!0);const a=await Promise.all(m.map((e=>x(e)))),r=await E(e,t,"function"==typeof n?n():n,a);s({pattern:r,categoryId:u})}catch(e){S(e.message,{type:"snackbar",id:"pattern-create"}),o?.()}finally{w(!1),_([]),y("")}}(g,l)}},(0,k.createElement)(v.__experimentalVStack,{spacing:"5"},(0,k.createElement)(v.TextControl,{label:(0,h.__)("Name"),value:g,onChange:y,placeholder:(0,h.__)("My pattern"),className:"patterns-create-modal__name-input",__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,k.createElement)(D,{categoryTerms:m,onChange:_,categoryMap:B}),(0,k.createElement)(v.ToggleControl,{label:(0,h._x)("Synced","Option that makes an individual pattern synchronized"),help:(0,h.__)("Sync this pattern across multiple locations."),checked:l===d.full,onChange:()=>{p(l===d.full?d.unsynced:d.full)}}),(0,k.createElement)(v.__experimentalHStack,{justify:"right"},(0,k.createElement)(v.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{a(),y("")}},(0,h.__)("Cancel")),(0,k.createElement)(v.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit","aria-disabled":!g||f,isBusy:f},e))))}function A(e,t){return e.type!==l.user?t.core?.filter((t=>e.categories.includes(t.name))).map((e=>e.label)):t.user?.filter((t=>e.wp_pattern_category.includes(t.id))).map((e=>e.label))}function L({pattern:e,onSuccess:t}){const{createSuccessNotice:n}=(0,r.useDispatch)(T.store),a=(0,r.useSelect)((e=>{const{getUserPatternCategories:t,getBlockPatternCategories:n}=e(c.store);return{core:n(),user:t()}}));return e?{content:e.content,defaultCategories:A(e,a),defaultSyncType:e.type!==l.user?d.unsynced:e.wp_pattern_sync_status||d.full,defaultTitle:(0,h.sprintf)((0,h.__)("%s (Copy)"),"string"==typeof e.title?e.title:e.title.raw),onSuccess:({pattern:e})=>{n((0,h.sprintf)((0,h.__)('"%s" duplicated.'),e.title.raw),{type:"snackbar",id:"patterns-create"}),t?.({pattern:e})}}:null}const z=window.wp.primitives,U=(0,k.createElement)(z.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,k.createElement)(z.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.1.1.1.3 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"}));function j({clientIds:e,rootClientId:t,closeBlockSettingsMenu:n}){const{createSuccessNotice:a}=(0,r.useDispatch)(T.store),{replaceBlocks:o}=(0,r.useDispatch)(i.store),{setEditingPattern:l}=b((0,r.useDispatch)(C)),[u,p]=(0,P.useState)(!1),m=(0,r.useSelect)((n=>{var a;const{canUser:r}=n(c.store),{getBlocksByClientId:o,canInsertBlockType:l,getBlockRootClientId:u}=n(i.store),d=t||(e.length>0?u(e[0]):void 0),p=null!==(a=o(e))&&void 0!==a?a:[];return!(1===p.length&&p[0]&&(0,s.isReusableBlock)(p[0])&&!!n(c.store).getEntityRecord("postType","wp_block",p[0].attributes.ref))&&l("core/block",d)&&p.every((e=>!!e&&e.isValid&&(0,s.hasBlockSupport)(e.name,"reusable",!0)))&&!!r("create","blocks")}),[e,t]),{getBlocksByClientId:_}=(0,r.useSelect)(i.store),g=(0,P.useCallback)((()=>(0,s.serialize)(_(e))),[_,e]);if(!m)return null;return(0,k.createElement)(k.Fragment,null,(0,k.createElement)(v.MenuItem,{icon:U,onClick:()=>p(!0),"aria-expanded":u,"aria-haspopup":"dialog"},(0,h.__)("Create pattern")),u&&(0,k.createElement)(M,{content:g,onSuccess:t=>{(({pattern:t})=>{if(t.wp_pattern_sync_status!==d.unsynced){const a=(0,s.createBlock)("core/block",{ref:t.id});o(e,a),l(a.clientId,!0),n()}a(t.wp_pattern_sync_status===d.unsynced?(0,h.sprintf)((0,h.__)("Unsynced pattern created: %s"),t.title.raw):(0,h.sprintf)((0,h.__)("Synced pattern created: %s"),t.title.raw),{type:"snackbar",id:"convert-to-pattern-success"}),p(!1)})(t)},onError:()=>{p(!1)},onClose:()=>{p(!1)}}))}const F=window.wp.url;const V=function({clientId:e}){const{canRemove:t,isVisible:n,managePatternsUrl:a}=(0,r.useSelect)((t=>{const{getBlock:n,canRemoveBlock:a,getBlockCount:r,getSettings:o}=t(i.store),{canUser:l}=t(c.store),u=n(e),d=o().__unstableIsBlockBasedTheme;return{canRemove:a(e),isVisible:!!u&&(0,s.isReusableBlock)(u)&&!!l("update","blocks",u.attributes.ref),innerBlockCount:r(e),managePatternsUrl:d&&l("read","templates")?(0,F.addQueryArgs)("site-editor.php",{path:"/patterns"}):(0,F.addQueryArgs)("edit.php",{post_type:"wp_block"})}}),[e]),{convertSyncedPatternToStatic:o}=b((0,r.useDispatch)(C));return n?(0,k.createElement)(k.Fragment,null,t&&(0,k.createElement)(v.MenuItem,{onClick:()=>o(e)},(0,h.__)("Detach")),(0,k.createElement)(v.MenuItem,{href:a},(0,h.__)("Manage patterns"))):null};const G=window.wp.a11y;function H(e,t){for(const n of e){if(n.attributes.metadata?.name===t)return n;const e=H(n.innerBlocks,t);if(e)return e}}const q={};E(q,{CreatePatternModal:M,CreatePatternModalContents:O,DuplicatePatternModal:function({pattern:e,onClose:t,onSuccess:n}){const a=L({pattern:e,onSuccess:n});return e?(0,k.createElement)(M,{modalTitle:(0,h.__)("Duplicate pattern"),confirmLabel:(0,h.__)("Duplicate"),onClose:t,onError:t,...a}):null},useDuplicatePatternProps:L,RenamePatternModal:function({onClose:e,onError:t,onSuccess:n,pattern:a,...o}){const s=(0,x.decodeEntities)(a.title),[i,l]=(0,P.useState)(s),[u,d]=(0,P.useState)(!1),{editEntityRecord:p,__experimentalSaveSpecifiedEntityEdits:m}=(0,r.useDispatch)(c.store),{createSuccessNotice:_,createErrorNotice:g}=(0,r.useDispatch)(T.store);return(0,k.createElement)(v.Modal,{title:(0,h.__)("Rename"),...o,onRequestClose:e},(0,k.createElement)("form",{onSubmit:async r=>{if(r.preventDefault(),i&&i!==a.title&&!u)try{await p("postType",a.type,a.id,{title:i}),d(!0),l(""),e?.();const t=await m("postType",a.type,a.id,["title"],{throwOnError:!0});n?.(t),_((0,h.__)("Pattern renamed"),{type:"snackbar",id:"pattern-update"})}catch(e){t?.();const n=e.message&&"unknown_error"!==e.code?e.message:(0,h.__)("An error occurred while renaming the pattern.");g(n,{type:"snackbar",id:"pattern-update"})}finally{d(!1),l("")}}},(0,k.createElement)(v.__experimentalVStack,{spacing:"5"},(0,k.createElement)(v.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,h.__)("Name"),value:i,onChange:l,required:!0}),(0,k.createElement)(v.__experimentalHStack,{justify:"right"},(0,k.createElement)(v.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{e?.(),l("")}},(0,h.__)("Cancel")),(0,k.createElement)(v.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit"},(0,h.__)("Save"))))))},PatternsMenuItems:function({rootClientId:e}){return(0,k.createElement)(i.BlockSettingsMenuControls,null,(({selectedClientIds:t,onClose:n})=>(0,k.createElement)(k.Fragment,null,(0,k.createElement)(j,{clientIds:t,rootClientId:e,closeBlockSettingsMenu:n}),1===t.length&&(0,k.createElement)(V,{clientId:t[0]}))))},RenamePatternCategoryModal:function({category:e,existingCategories:t,onClose:n,onError:a,onSuccess:o,...s}){const i=(0,P.useId)(),l=(0,P.useRef)(),[u,d]=(0,P.useState)((0,x.decodeEntities)(e.name)),[p,m]=(0,P.useState)(!1),[_,g]=(0,P.useState)(!1),y=_?`patterns-rename-pattern-category-modal__validation-message-${i}`:void 0,{saveEntityRecord:f,invalidateResolution:w}=(0,r.useDispatch)(c.store),{createErrorNotice:E,createSuccessNotice:b}=(0,r.useDispatch)(T.store),S=()=>{n(),d("")};return(0,k.createElement)(v.Modal,{title:(0,h.__)("Rename"),onRequestClose:S,...s},(0,k.createElement)("form",{onSubmit:async r=>{if(r.preventDefault(),!p){if(!u||u===e.name){const e=(0,h.__)("Please enter a new name for this category.");return(0,G.speak)(e,"assertive"),g(e),void l.current?.focus()}if(t.patternCategories.find((t=>t.id!==e.id&&t.label.toLowerCase()===u.toLowerCase()))){const e=(0,h.__)("This category already exists. Please use a different name.");return(0,G.speak)(e,"assertive"),g(e),void l.current?.focus()}try{m(!0);const t=await f("taxonomy",I,{id:e.id,slug:e.slug,name:u});w("getUserPatternCategories"),o?.(t),n(),b((0,h.__)("Pattern category renamed."),{type:"snackbar",id:"pattern-category-update"})}catch(e){a?.(),E(e.message,{type:"snackbar",id:"pattern-category-update"})}finally{m(!1),d("")}}}},(0,k.createElement)(v.__experimentalVStack,{spacing:"5"},(0,k.createElement)(v.__experimentalVStack,{spacing:"2"},(0,k.createElement)(v.TextControl,{ref:l,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,h.__)("Name"),value:u,onChange:e=>{_&&g(void 0),d(e)},"aria-describedby":y,required:!0}),_&&(0,k.createElement)("span",{className:"patterns-rename-pattern-category-modal__validation-message",id:y},_)),(0,k.createElement)(v.__experimentalHStack,{justify:"right"},(0,k.createElement)(v.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:S},(0,h.__)("Cancel")),(0,k.createElement)(v.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit","aria-disabled":!u||u===e.name||p,isBusy:p},(0,h.__)("Save"))))))},useSetPatternBindings:function({name:e,attributes:t,setAttributes:n},a){var o,c;const i=(0,r.useSelect)((e=>{const{getBlockBindingsSource:t}=b(e(s.store));return!!t("core/pattern-overrides")}),[]),l=null!==(o=t?.metadata?.name)&&void 0!==o?o:"",u=null!==(c=(0,B.usePrevious)(l))&&void 0!==c?c:"",d=t?.metadata?.bindings;(0,P.useEffect)((()=>{if(!i||"wp_block"!==a||l===u)return;const r=p[e];if(!r.map((e=>t.metadata?.bindings?.[e]?.source)).every((e=>e&&"core/pattern-overrides"!==e))){if(!l?.length&&u?.length){const e=function(e,t){let n={};for(const a of t)"core/pattern-overrides"!==e?.[a]?.source&&void 0!==e?.[a]?.source&&(n[a]=e[a]);return Object.keys(n).length||(n=void 0),n}(d,r);n({metadata:{...t.metadata,bindings:e}})}if(!u?.length&&l.length){const e=function(e,t){const n={...e};for(const a of t)e?.[a]||(n[a]={source:"core/pattern-overrides"});return n}(d,r);n({metadata:{...t.metadata,bindings:e}})}}}),[i,d,u,l,a,e,t.metadata,n])},ResetOverridesControl:function(e){const t=(0,r.useRegistry)(),n=e.attributes.metadata?.name,a=(0,r.useSelect)((t=>{if(!n)return;const{getBlockParentsByBlockName:a,getBlocksByClientId:r}=t(i.store),o=r(a(e.clientId,"core/block"))[0];return o?.attributes.content?.[n]?o:void 0}),[e.clientId,n]);return(0,k.createElement)(i.BlockControls,{group:"other"},(0,k.createElement)(v.ToolbarGroup,null,(0,k.createElement)(v.ToolbarButton,{onClick:async()=>{var r;const o=await t.resolveSelect(c.store).getEditedEntityRecord("postType","wp_block",a.attributes.ref),i=H(null!==(r=o.blocks)&&void 0!==r?r:(0,s.parse)(o.content),n),l=Object.assign(Object.fromEntries(Object.keys(e.attributes).map((e=>[e,void 0]))),i.attributes);e.setAttributes(l)},disabled:!a,__experimentalIsFocusable:!0},(0,h.__)("Reset"))))},useAddPatternCategory:N,PATTERN_TYPES:l,PATTERN_DEFAULT_CATEGORY:u,PATTERN_USER_CATEGORY:"my-patterns",EXCLUDED_PATTERN_SOURCES:["core","pattern-directory/core","pattern-directory/featured"],PATTERN_SYNC_TYPES:d,PARTIAL_SYNCING_SUPPORTED_BLOCKS:p}),(window.wp=window.wp||{}).patterns=t})();